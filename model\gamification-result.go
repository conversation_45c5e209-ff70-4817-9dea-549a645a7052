package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/model/cache"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type GamificationResult struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	GamificationCode string `json:"gamificationCode,omitempty" bson:"gamification_code,omitempty"`
	GamificationID   int64  `json:"gamificationID,omitempty" bson:"gamification_id,omitempty"`
	LuckyWheelCode   string `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`

	GamificationDetailCode string `json:"gamificationDetailCode,omitempty" bson:"gamification_detail_code,omitempty"`
	GamificationDetailID   int64  `json:"gamificationDetailID,omitempty" bson:"gamification_detail_id,omitempty"`

	GamificationResultCode string `json:"gamificationResultCode,omitempty" bson:"gamification_result_code,omitempty"`
	GamificationResultID   int64  `json:"gamificationResultID,omitempty" bson:"gamification_result_id,omitempty"`

	Status        enum.GamificationResultStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	CompletedTime *time.Time                         `json:"completedTime,omitempty" bson:"completed_time,omitempty"`

	AccountID     int64  `json:"account_id,omitempty" bson:"account_id,omitempty"`
	CustomerID    int64  `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	CustomerScope string `json:"customerScope,omitempty" bson:"customer_scope,omitempty"`
	CustomerPhone string `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`

	Value       int                             `json:"value,omitempty" bson:"value,omitempty"`
	Data        []*GamificationResultData       `json:"data,omitempty" bson:"data,omitempty"`
	OrderIds    []int                           `json:"orderIds,omitempty" bson:"order_ids,omitempty"`
	OrderValues []*cache.GamificationOrderValue `json:"orderValues,omitempty" bson:"order_values,omitempty"`
	DetailValue *DetailValue                    `json:"detailValue,omitempty" bson:"detail_value,omitempty"`

	Detail          *GamificationDetail `json:"detail,omitempty" bson:"-"`
	ComplexQuery    []*bson.M           `json:"-" bson:"$and,omitempty"`
	OrderID         int64               `json:"orderID,omitempty" bson:"-"`
	CreatedTimeFrom *time.Time          `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo   *time.Time          `json:"createdTimeTo,omitempty" bson:"-"`

	LogCode string `json:"-" bson:"log_code,omitempty"` // save deleted

	RewardStatus  enum.RewardProgressType `json:"rewardStatus,omitempty" bson:"reward_status,omitempty"`
	CreatedReward []*GamiCreatedReward    `json:"createdReward" bson:"created_reward,omitempty"`
}

type DetailValue struct {
	MinOrderCount    int            `json:"minOrderCount,omitempty" bson:"min_order_count,omitempty" validate:"omitempty,gte=0,lte=1000000000"`        // kind: ORDER
	MinSkuCount      int            `json:"minSkuCount,omitempty" bson:"min_sku_count,omitempty" validate:"omitempty,gte=0,lte=1000000000"`            // kind: ORDER
	MinTotalValue    int            `json:"minTotalValue,omitempty" bson:"min_total_value,omitempty" validate:"omitempty,gte=0,lte=1000000000"`        // kind: ORDER
	MinTotalSkuCount int            `json:"minTotalSkuCount,omitempty" bson:"min_total_sku_count,omitempty" validate:"omitempty,gte=0,lte=1000000000"` // kind: ORDER
	SkuUniques       map[string]int `json:"skuUniques,omitempty" bson:"sku_uniques,omitempty"`                                                         // kind: ORDER

	CompletedQuantityCount int `json:"completedQuantityCount,omitempty" bson:"completed_quantity_count,omitempty"`
}

type GamificationResultData struct {
	OrderID     int64      `json:"orderID,omitempty" bson:"order_id,omitempty"`
	Value       int        `json:"value,omitempty" bson:"value,omitempty"`
	CreatedTime *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
}

type GamiCreatedReward struct {
	// case 1: Reward for customer is voucher
	VoucherCode *string `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
}

var GamificationResultDB = &db.Instance{
	ColName:        "gamification_result",
	TemplateObject: &GamificationResult{},
}

// InitGamificationResultModel is func init model
func InitGamificationResultModel(s *mongo.Database) {
	GamificationResultDB.ApplyDatabase(s)

	t := true

	GamificationResultDB.CreateIndex(bson.D{
		primitive.E{Key: "gamification_detail_id", Value: 1},
		primitive.E{Key: "status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_result_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_result_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_detail_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_detail_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

var GamificationResultDeletedDB = &db.Instance{
	ColName:        "gamification_result_deleted",
	TemplateObject: &GamificationResult{},
}

// InitGamificationResultDeletedModel is func init model
func InitGamificationResultDeletedModel(s *mongo.Database) {
	GamificationResultDeletedDB.ApplyDatabase(s)

	// t := true
	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_result_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_detail_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_detail_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// GamificationResultDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
