package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type LuckyWheelEvent struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code           string       `json:"code,omitempty" bson:"code,omitempty"`
	Description    string       `json:"description,omitempty" bson:"description,omitempty"`
	IsActive       *bool        `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Name           string       `json:"name,omitempty" bson:"name,omitempty"`
	StartTime      *time.Time   `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime        *time.Time   `json:"endTime,omitempty" bson:"end_time,omitempty"`
	LuckyWheelCode string       `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	TimeSlots      *[]*TimeSlot `json:"timeSlots,omitempty" bson:"time_slots,omitempty"`

	ProvinceCodes     []string `json:"provinceCodes,omitempty" bson:"province_codes,omitempty"`
	RegionCodes       []string `json:"regionCodes,omitempty" bson:"region_codes,omitempty"`
	CustomerLevels    []string `json:"levels,omitempty" bson:"levels,omitempty"`
	CustomerScopes    []string `json:"scopes,omitempty" bson:"scopes,omitempty"`
	CustomerApplyType string   `json:"customerApplyType,omitempty" bson:"customer_apply_type,omitempty"` // ALL || MANY

	Items            []*LuckyWheelItem            `json:"items,omitempty" bson:"-"`
	ItemsDescription []*LuckyWheelItemDescription `json:"itemsDescription,omitempty" bson:"-"`
}

type TimeSlot struct {
	DateTime *time.Time `json:"dateTime,omitempty" bson:"date_time,omitempty"`
	Slots    *[]*Slot   `json:"slots,omitempty" bson:"slots,omitempty"`
}

type Slot struct {
	HourStart   *int `json:"hourStart,omitempty" bson:"hour_start,omitempty"`
	HourEnd     *int `json:"hourEnd,omitempty" bson:"hour_end,omitempty"`
	MinuteStart *int `json:"minuteStart,omitempty" bson:"minute_start,omitempty"`
	MinuteEnd   *int `json:"minuteEnd,omitempty" bson:"minute_end,omitempty"`
}

var LuckyWheelEventDB = &db.Instance{
	ColName:        "lucky_wheel_event",
	TemplateObject: &LuckyWheelEvent{},
}

// InitLuckyWheelEvent is func init model
func InitLuckyWheelEvent(s *mongo.Database) {
	LuckyWheelEventDB.ApplyDatabase(s)
}
