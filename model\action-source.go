package model

// Account represent user account
type Account struct {
	AccountID int64  `json:"accountId,omitempty" bson:"account_id,omitempty" `
	Username  string `json:"username,omitempty" bson:"username,omitempty" `
	Fullname  string `json:"fullname,omitempty" bson:"fullname,omitempty" `
	Email     string `json:"email,omitempty" bson:"email,omitempty" `

	// use for update employee
	Department *string `json:"department,omitempty" bson:"-" `
	Role       *string `json:"role,omitempty" bson:"-" `
	Customer   *string `json:"customer,omitempty" bson:"-"`
}

type UserRole struct {
	Username string  `json:"username,omitempty" bson:"username,omitempty"`
	RoleCode *string `json:"roleCode,omitempty" bson:"role_code,omitempty"`

	// scope
	DepartmentCode *string `json:"departmentCode,omitempty" bson:"department_code,omitempty"`
	CustomerID     *string `json:"customerId,omitempty" bson:"customer_id,omitempty"`

	// is main postion (for employee only)
	IsMain *bool `json:"isMain,omitempty" bson:"is_main,omitempty"`
}

type ActionSource struct {
	Account  *Account    `json:"account"`
	UserRole []*UserRole `json:"userRoles"`
}
