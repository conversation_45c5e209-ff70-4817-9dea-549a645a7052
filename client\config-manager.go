package client

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/conf"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetAppValue = "/core/config-manager/v1/app-value/single"
)

var ConfigManagerClient = &configManagerClient{}

// Client is model define config manager client
type configManagerClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewConfigManagerClient is func define new config manager client
func NewConfigManagerClient(session *mongo.Database) *configManagerClient {
	ConfigManagerClient = &configManagerClient{
		svc: client.NewRESTClient(conf.Config.APIHost, "config_manager_client ", DEFAULT_TIMEOUT, DEFAULT_RETRY_TIME, DEFAULT_WAIT_TIME),
		headers: map[string]string{
			"Authorization": conf.Config.APIKey,
		},
	}
	return ConfigManagerClient
}

// GetAppValue is func define get app value
func (cli *configManagerClient) GetAppValue(appCode string) *model.AppValueResponse {

	resp := &model.AppValueResponse{
		Status: common.APIStatus.Invalid,
	}

	params := map[string]string{
		"appCode": appCode,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetAppValue, &[]string{"GetAppValue", appCode})
	if err != nil {
		resp.Message = err.Error()
		return resp
	}

	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		resp.Message = err.Error()
		return resp
	}
	if len(resp.Data) == 0 {
		resp.Status = common.APIStatus.Invalid
		return resp
	}

	return resp
}
