package helper

import (
	"regexp"
	"strings"
	"unicode"

	"golang.org/x/text/transform"
	"golang.org/x/text/unicode/norm"
)

var mapVNICode = map[string]string{
	"a": "[à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ]",
	"ê": "[è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ]",
	"i": "[ì|í|ị|ỉ|ĩ]",
	"o": "[ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ]",
	"u": "[ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ]",
	"y": "[ỳ|ý|ỵ|ỷ|ỹ]",
	"d": "[đ]",
}

// NormalizeString ...
func NormalizeString(val string) string {
	//nolint
	tran := transform.Chain(norm.NFD, transform.RemoveFunc(isMn), norm.NFC)
	normStr, _, _ := transform.String(tran, strings.ToLower(val))
	normStr = formmatVNCode(normStr)
	normStr = strings.Replace(normStr, " ", "-", -1)
	normStr = strings.Replace(normStr, ",", "", -1)
	normStr = strings.Replace(normStr, ";", "", -1)
	normStr = strings.Replace(normStr, "(", "", -1)
	normStr = strings.Replace(normStr, ")", "", -1)
	normStr = strings.Replace(normStr, "/", "", -1)
	normStr = strings.Replace(normStr, "&", "", -1)
	normStr = strings.Replace(normStr, "%", "", -1)

	r, _ := regexp.Compile(`(\\W)`)
	qCheck := make(map[string]int)
	for i, v := range r.FindAllString(normStr, -1) {
		if v == "-" || qCheck[v] > 0 {
			continue
		}
		qCheck[v] = i + 1
		normStr = strings.ReplaceAll(normStr, v, ``)
	}

	normStr = strings.Replace(normStr, "--", "-", -1)

	return strings.ToLower(normStr)
}

func isMn(r rune) bool {
	return unicode.Is(unicode.Mn, r)
}

func formmatVNCode(str string) string {
	for key, val := range mapVNICode {
		m := regexp.MustCompile(val)
		str = m.ReplaceAllString(str, key)
	}
	return str
}

func IsContainsString(arr []string, key string) bool {

	if len(arr) == 0 {
		return false
	}

	for _, s := range arr {
		if key == s {
			return true
		}
	}

	return false
}
