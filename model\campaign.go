package model

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Campaign struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	CampaignID   int64                    `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	CampaignCode string                   `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	Banner       string                   `json:"banner,omitempty" bson:"banner,omitempty"`
	CampaignName string                   `json:"campaignName,omitempty" bson:"campaign_name,omitempty"`
	Description  string                   `json:"description,omitempty" bson:"description,omitempty"`
	CampaignType enum.CampaignValueType   `json:"campaignType,omitempty" bson:"campaign_type,omitempty"`
	Status       enum.CampaignStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	ShortName    *string                  `json:"shortName,omitempty" bson:"short_name,omitempty"`

	// new update 27-10-2021
	RegistrationStartTime time.Time                    `json:"registrationStartTime,omitempty" bson:"registration_start_time,omitempty"`
	RegistrationEndTime   time.Time                    `json:"registrationEndTime,omitempty" bson:"registration_end_time,omitempty"`
	StartTime             time.Time                    `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime               time.Time                    `json:"endTime,omitempty" bson:"end_time,omitempty"`
	FlashSaleTimes        []*FlashSaleTime             `json:"flashSaleTimes,omitempty" bson:"flash_sale_times,omitempty"`
	FlashSaleTimesView    []*CampaignFlashSaleTimeItem `json:"flashSaleTimesView,omitempty" bson:"flash_sale_times_view,omitempty"`
	Reward                *CampaignReward              `json:"reward,omitempty" bson:"reward,omitempty"`

	SubsidyType  string `json:"subsidyType,omitempty" bson:"subsidy_type,omitempty"`
	SubsidyValue int64  `json:"subsidyValue,omitempty" bson:"subsidy_value,omitempty"`

	SaleType enum.CampaignSaleValueType `json:"saleType,omitempty" bson:"sale_type,omitempty"`
	Fulfill  *float64                   `json:"fulfill,omitempty" bson:"fulfill,omitempty"`

	// condition
	SellerCodes    *[]string `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	CustomerScopes *[]string `json:"customerScopes,omitempty" bson:"customer_scopes,omitempty"`
	Regions        *[]string `json:"regions,omitempty" bson:"regions,omitempty"`

	//
	TotalProduct      *int64 `json:"totalProduct,omitempty" bson:"total_product,omitempty"`
	TotalSeller       *int64 `json:"totalSeller,omitempty" bson:"total_seller,omitempty"`
	TotalRevenue      *int64 `json:"totalRevenue,omitempty" bson:"total_revenue,omitempty"`
	TotalSoldQuantity *int64 `json:"totalSoldQuantity,omitempty" bson:"total_sold_quantity,omitempty"`

	IsActive           *bool     `json:"isActive,omitempty" bson:"is_active,omitempty"`
	NeedCheck          *string   `json:"needCheckSync,omitempty" bson:"need_check_sync,omitempty"`
	Version            string    `json:"version,omitempty" bson:"version,omitempty"`
	ChildCampaignCodes *[]string `json:"childCampaignCodes,omitempty" bson:"child_campaign_codes,omitempty"`
	ViewType           *string   `json:"viewType,omitempty" bson:"view_type,omitempty"`
	DisplayPriority    *int      `json:"displayPriority,omitempty" bson:"display_priority,omitempty"`

	SystemDisplay string `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`

	Slug             string              `json:"slug,omitempty" bson:"slug,omitempty"`
	HashTag          string              `json:"-" bson:"hash_tag,omitempty"`
	ComplexQuery     []*bson.M           `json:"-" bson:"$and,omitempty"`
	CampaignSaleTime []*CampaignSaleTime `json:"campaignSaleTime,omitempty" bson:"-"`
	IsJoined         *bool               `json:"isJoined,omitempty" bson:"-"`

	DaysOfNewProduct *int64 `json:"daysOfNewProduct,omitempty" bson:"days_of_new_product,omitempty"`

	ProcessingTimeFrom *time.Time `json:"processingTimeFrom,omitempty" bson:"-"`
	ProcessingTimeTo   *time.Time `json:"processingTimeTo,omitempty" bson:"-"`
}

type FlashSaleTime struct {
	Code      string                       `json:"code,omitempty" bson:"code,omitempty"`
	Name      string                       `json:"name,omitempty" bson:"name,omitempty"`
	StartTime time.Time                    `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *time.Time                   `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Detail    []*CampaignFlashSaleTimeItem `json:"detail,omitempty" bson:"detail,omitempty"`
}

//	type FlashSaleTimeItem struct {
//		Code          string          `json:"code,omitempty" bson:"code,omitempty"`
//		Ref           string          `json:"ref,omitempty" bson:"ref,omitempty"`
//		Name          string          `json:"name,omitempty" bson:"name,omitempty"`
//		StartTime     *time.Time      `json:"startTime,omitempty" bson:"start_time,omitempty"`
//		EndTime       *time.Time      `json:"endTime,omitempty" bson:"end_time,omitempty"`
//		ProductIDs    *[]int64        `json:"productIDs,omitempty" bson:"product_ids,omitempty"`
//		CategoryCodes *[]string       `json:"categoryCodes,omitempty" bson:"category_codes,omitempty"`
//		Hour          []time.Duration `json:"-" bson:"hour,omitempty"`
//		Day           int             `json:"-" bson:"day,omitempty"`
//	}
type CampaignReward struct {
	PercentageDiscount *int64 `json:"percentageDiscount,omitempty" bson:"percentage_discount,omitempty"`
	AbsoluteDiscount   *int64 `json:"absoluteDiscount,omitempty" bson:"absolute_discount,omitempty"`
	MaxDiscount        *int64 `json:"maxDiscount,omitempty" bson:"max_discount,omitempty"`
}

var CampaignDB = &db.Instance{
	ColName:        "campaign",
	TemplateObject: &Campaign{},
}

var CampaignCacheDB = &db.Instance{
	ColName:        "campaign",
	TemplateObject: &Campaign{},
}

// InitCampaignModel is func init model
func InitCampaignModel(dbInst *db.Instance, s *mongo.Database) {
	dbInst.ApplyDatabase(s)
	// Add indexes in `promotion-worker` source code
}

// InitCampaignCacheModel is func init model
func InitCampaignCacheModel(dbInst *db.Instance, s *mongo.Database) {
	dbInst.ApplyDatabase(s)
	// Add indexes in `promotion-worker` source code
}

func (c *Campaign) ToHashTag() {
	c.HashTag = fmt.Sprintf("%s,%s,%s,%d", c.CampaignName, utils.NormalizeString(c.CampaignName), c.CampaignCode, c.CampaignID)
}

var CampaignFlashSaleTime = map[string]*CampaignFlashSaleTimeItem{
	"0600-1159": &CampaignFlashSaleTimeItem{
		Code: "0600-1159",
		Name: "06h00 - 11h59",
		Hour: []time.Duration{
			time.Duration(6 * time.Hour),
			time.Duration(12*time.Hour - 1*time.Minute),
		},
	},
	"1200-1759": &CampaignFlashSaleTimeItem{
		Code: "1200-1759",
		Name: "12h00 - 17h59",
		Hour: []time.Duration{
			time.Duration(12 * time.Hour),
			time.Duration(18*time.Hour - 1*time.Minute),
		},
	},
	"1800-2400": &CampaignFlashSaleTimeItem{
		Code: "1800-2400",
		Name: "18h00 - 24h00",
		Hour: []time.Duration{
			time.Duration(18 * time.Hour),
			time.Duration(24 * time.Hour),
		},
	},
	"0600-0759": &CampaignFlashSaleTimeItem{
		Code: "0600-0759",
		Name: "06h00 - 07h59",
		Hour: []time.Duration{
			time.Duration(6 * time.Hour),
			time.Duration(8*time.Hour - 1*time.Minute),
		},
	},
	"0800-0959": &CampaignFlashSaleTimeItem{
		Code: "0800-0959",
		Name: "08h00 - 09h59",
		Hour: []time.Duration{
			time.Duration(8 * time.Hour),
			time.Duration(10*time.Hour - 1*time.Minute),
		},
	},
	"1000-1159": &CampaignFlashSaleTimeItem{
		Code: "1000-1159",
		Name: "10h00 - 11h59",
		Hour: []time.Duration{
			time.Duration(10 * time.Hour),
			time.Duration(12*time.Hour - 1*time.Minute),
		},
	},
	"1200-1359": &CampaignFlashSaleTimeItem{
		Code: "1200-1359",
		Name: "12h00 - 13h59",
		Hour: []time.Duration{
			time.Duration(12 * time.Hour),
			time.Duration(14*time.Hour - 1*time.Minute),
		},
	},
	"1400-1559": &CampaignFlashSaleTimeItem{
		Code: "1400-1559",
		Name: "14h00 - 15h59",
		Hour: []time.Duration{
			time.Duration(14 * time.Hour),
			time.Duration(16*time.Hour - 1*time.Minute),
		},
	},
	"1600-1759": &CampaignFlashSaleTimeItem{
		Code: "1600-1759",
		Name: "16h00 - 17h59",
		Hour: []time.Duration{
			time.Duration(16 * time.Hour),
			time.Duration(18*time.Hour - 1*time.Minute),
		},
	},
	"1800-1959": &CampaignFlashSaleTimeItem{
		Code: "1800-1959",
		Name: "18h00 - 19h59",
		Hour: []time.Duration{
			time.Duration(18 * time.Hour),
			time.Duration(20*time.Hour - 1*time.Minute),
		},
	},
	"2000-2159": &CampaignFlashSaleTimeItem{
		Code: "2000-2159",
		Name: "20h00 - 21h59",
		Hour: []time.Duration{
			time.Duration(20 * time.Hour),
			time.Duration(22*time.Hour - 1*time.Minute),
		},
	},
	"2200-2359": &CampaignFlashSaleTimeItem{
		Code: "2200-2359",
		Name: "22h00 - 23h59",
		Hour: []time.Duration{
			time.Duration(22 * time.Hour),
			time.Duration(24*time.Hour - 1*time.Minute),
		},
	},
}
