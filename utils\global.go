package utils

import (
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// ParseIntToPointer is func convert int to *int
func ParseIntToPointer(val int) *int {
	return &val
}

// ParseInt64ToPointer is func convert int to *int
func ParseInt64ToPointer(val int64) *int64 {
	return &val
}

// ParseFloat64ToPointer is func convert int to *int
func ParseFloat64ToPointer(val float64) *float64 {
	return &val
}

// ParseStringToPointer is func convert string to *string
func ParseStringToPointer(val string) *string {
	return &val
}

// ParseBoolToPointer is func convert bool to *bool
func ParseBoolToPointer(val bool) *bool {
	return &val
}

// ParseTimeToPointer is func convert time.Time to *time.Time
func ParseTimeToPointer(val time.Time) *time.Time {
	return &val
}

// PointerToInt ...
func PointerToInt(val *int) int {
	return *val
}

// GetHostName ...
func GetHostName() string {
	name, err := os.Hostname()
	if err != nil {
		return "undefined"
	}
	return name
}

// ParserQ ...
func ParserQ(q string) string {
	q = strings.Replace(NormalizeString(q), " ", "-", -1)
	r, _ := regexp.Compile(`(\\W)`)
	qCheck := make(map[string]int)
	for i, v := range r.FindAllString(q, -1) {
		if v == "-" || qCheck[v] > 0 {
			continue
		}
		qCheck[v] = i + 1
		q = strings.ReplaceAll(q, v, `\`+v)
	}
	return q
}

// PrintValue prints a value:
// - For primitive types: prints as is
// - For structs/complex types: prints as JSON
// - Handles nil pointers, nested structures, and recursive cases
func PrintValue(v interface{}) string {
	if v == nil {
		return "null"
	}

	val := reflect.ValueOf(v)
	return formatValueWithJSON(val)
}

func formatValueWithJSON(val reflect.Value) string {
	if !val.IsValid() {
		return "null"
	}

	// Handle pointers
	if val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return "null"
		}
		return formatValueWithJSON(val.Elem())
	}

	// Handle interfaces
	if val.Kind() == reflect.Interface {
		if val.IsNil() {
			return "null"
		}
		return formatValueWithJSON(val.Elem())
	}

	// Special handling for time.Time
	if val.Type() == reflect.TypeOf(time.Time{}) {
		t := val.Interface().(time.Time)
		b, _ := json.Marshal(t)
		return string(b)
	}

	// Handle primitive types
	switch val.Kind() {
	case reflect.Bool:
		return fmt.Sprintf("%v", val.Bool())
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return fmt.Sprintf("%d", val.Int())
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return fmt.Sprintf("%d", val.Uint())
	case reflect.Float32, reflect.Float64:
		return fmt.Sprintf("%g", val.Float())
	case reflect.String:
		b, _ := json.Marshal(val.String())
		return string(b)
	}

	// Handle complex types (struct, map, slice, array)
	if isComplexType(val.Kind()) {
		b, err := json.Marshal(val.Interface())
		if err != nil {
			return fmt.Sprintf("error: %v", err)
		}
		return string(b)
	}

	// Default case
	return fmt.Sprintf("%v", val.Interface())
}

func isComplexType(kind reflect.Kind) bool {
	switch kind {
	case reflect.Struct, reflect.Map, reflect.Slice, reflect.Array:
		return true
	default:
		return false
	}
}
