package api

import (
	"encoding/json"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
	"time"
)

func ImportResultGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		sort     = req.GetParam("sort")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 1000 {
		limit = 1000
	}

	if offset < 0 {
		offset = 0
	}

	var query = model.ImportResult{}
	if len(q) > 0 {
		if err := json.Unmarshal([]byte(q), &query); err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PARSER_DATA",
			})
		}
	}

	if query.CreatedTimeFrom != nil || query.CreatedTimeTo != nil {
		t := time.Now()
		if query.CreatedTimeFrom == nil {
			t = t.Add(-30 * 24 * time.Hour) // default from is last 30 days
			query.CreatedTimeFrom = &t
		}

		if query.CreatedTimeTo == nil {
			query.CreatedTimeTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFrom,
				"$lte": query.CreatedTimeTo,
			},
		})
	}

	if query.CompletedTimeFrom != nil || query.CompletedTimeTo != nil {
		t := time.Now()
		if query.CompletedTimeFrom == nil {
			t = t.Add(-30 * 24 * time.Hour) // default from is last 30 days
			query.CompletedTimeFrom = &t
		}

		if query.CompletedTimeTo == nil {
			query.CompletedTimeTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"completed_time": bson.M{
				"$gte": query.CompletedTimeFrom,
				"$lte": query.CompletedTimeTo,
			},
		})
	}

	return resp.Respond(action.ImportResultGetList(getActionSource(req), &query, offset, limit, getTotal, sort))
}

func ImportResultDetailGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		sort     = req.GetParam("sort")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 1000 {
		limit = 1000
	}

	if offset < 0 {
		offset = 0
	}

	var query = model.ImportResultDetail{}
	if len(q) > 0 {
		if err := json.Unmarshal([]byte(q), &query); err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PARSER_DATA",
			})
		}
	}

	if query.CreatedTimeFrom != nil || query.CreatedTimeTo != nil {
		t := time.Now()
		if query.CreatedTimeFrom == nil {
			t = t.Add(-30 * 24 * time.Hour) // default from is last 30 days
			query.CreatedTimeFrom = &t
		}

		if query.CreatedTimeTo == nil {
			query.CreatedTimeTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFrom,
				"$lte": query.CreatedTimeTo,
			},
		})
	}

	if query.CompletedTimeFrom != nil || query.CompletedTimeTo != nil {
		t := time.Now()
		if query.CompletedTimeFrom == nil {
			t = t.Add(-30 * 24 * time.Hour) // default from is last 30 days
			query.CompletedTimeFrom = &t
		}

		if query.CompletedTimeTo == nil {
			query.CompletedTimeTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"completed_time": bson.M{
				"$gte": query.CompletedTimeFrom,
				"$lte": query.CompletedTimeTo,
			},
		})
	}

	return resp.Respond(action.ImportResultDetailGetList(getActionSource(req), &query, offset, limit, getTotal, sort))
}
