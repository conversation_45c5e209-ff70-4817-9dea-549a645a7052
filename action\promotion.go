package action

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/helper"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// GetPromotionById get promotion by id
func GetPromotionById(PromotionID int64) *common.APIResponse {
	if PromotionID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Parameter promotionId must not empty.",
			ErrorCode: "ID_REQUIRED",
		}
	}
	return model.PromotionDB.QueryOne(bson.M{"promotion_id": PromotionID})
}

/*// GetActivePromotionWithVoucher get active promotion with voucher
func GetActivePromotionWithVoucher(account *model.Account, promotionID int64) *common.APIResponse {
	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(account.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Tài khoản không xác định " + err.Error(),
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}

	promotionListTemp := []*model.Promotion{}
	if promotionID == 0 {
		conditions := bson.M{
			"status":         enum.PromotionStatus.ACTIVE,
			"promotion_type": enum.PromotionType.VOUCHERCODE,
		}

		conditions["$or"] = []bson.M{
			{
				"scopes.customer_level_codes": customerInfo.Level,
			},
			{
				"scopes.customer_level_codes": "ALL",
			},
			{
				"scopes.customer_level_codes": bson.M{
					"$exists": false,
				},
			},
		}

		queryResult := model.PromotionDB.Query(
			conditions,
			0,
			1000,
			nil,
		)
		if queryResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.NotFound,
				Message:   "Không có chương trình khuyến mãi.",
				ErrorCode: "PROMOTION_NOT_FOUND",
			}
		}
		promotionListTemp = queryResult.Data.([]*model.Promotion)

	} else {
		queryResult := model.PromotionDB.Query(
			bson.M{
				"promotion_id":   promotionID,
				"status":         enum.PromotionStatus.ACTIVE,
				"promotion_type": enum.PromotionType.VOUCHERCODE,
			},
			0,
			1000,
			nil,
		)
		if queryResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.NotFound,
				Message:   "Không có chương trình khuyến mãi nào.",
				ErrorCode: "PROMOTION_NOT_FOUND",
			}
		}
		promotionListTemp = queryResult.Data.([]*model.Promotion)
	}

	promotionList := []*model.Promotion{}

	for _, promotion := range promotionListTemp {
		getVoucherResult := model.VoucherDB.Query(bson.M{
			"promotion_id": promotion.PromotionID,
			"type":         enum.VoucherType.PUBLIC,
			"status":       enum.VoucherStatus.ACTIVE,
			"end_time": bson.M{
				"$gt": time.Now(),
			},
			"public_time": bson.M{
				"$lt": time.Now(),
			},
			"$or": bson.A{
				bson.M{
					"applied_customers": bson.M{"$size": 0},
				},
				bson.M{
					"applied_customers": nil,
				},
				bson.M{
					"applied_customers": customerInfo.CustomerID,
				},
			},
		}, 0, 100000, nil)

		if getVoucherResult.Status == common.APIStatus.Ok {
			vouchers := getVoucherResult.Data.([]*model.Voucher)
			var voucherCodes []*string

			for _, voucher := range vouchers {
				voucherCodes = append(voucherCodes, &voucher.Code)
			}

			getUserPromotionResult := model.UserPromotionDB.Query(bson.M{
				"voucher_code": bson.M{
					"$in": voucherCodes,
				},
				"customer_id": customerInfo.CustomerID,
			}, 0, 100000, nil)

			if getUserPromotionResult.Status == common.APIStatus.Ok {
				userPromotionMap := make(map[string]*model.UserPromotion)
				userPromotions := getUserPromotionResult.Data.([]*model.UserPromotion)
				for _, userPromotion := range userPromotions {
					userPromotionMap[userPromotion.VoucherCode] = userPromotion
				}
				for _, voucher := range vouchers {
					voucher.UserPromotion = userPromotionMap[voucher.Code]
				}
			}
			for _, voucher := range vouchers {
				promotion.VoucherCodes = append(promotion.VoucherCodes, voucher)
			}
		}

		promotionList = append(promotionList, promotion)
	}

	if len(promotionList) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không có chương trình khuyến mãi nào.",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Danh sách chương trình khuyến mãi.",
		Data:    promotionList,
	}
}
*/
// GetPromotionList get promotion list
func GetPromotionList(query *model.Promotion, ListStatus []string, ListIDs []int64, offset int64, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	if query.Status != nil && *query.Status != "" {
		filter["status"] = *query.Status
	}

	if ListStatus != nil && len(ListStatus) > 0 {
		filter["status"] = bson.M{
			"$in": ListStatus,
		}
	}

	if ListIDs != nil && len(ListIDs) > 0 {
		filter["promotion_id"] = bson.M{
			"$in": ListIDs,
		}
	}

	if query.CampaignID > 0 {
		filter["campaign_id"] = query.CampaignID
	}

	if query.PromotionType != nil && *query.Status != "" {
		filter["promotion_type"] = *query.PromotionType
	}

	if query.PromotionName != "" {
		query.PromotionName = strings.TrimSpace(query.PromotionName)
		or := bson.A{}
		or = append(or, bson.M{
			"promotion_name": bson.M{
				"$regex": query.PromotionName,
			},
		})
		or = append(or, bson.M{
			"slug": bson.M{
				"$regex": strings.ToLower(helper.ConvertToRawText(query.PromotionName)),
			},
		})
		if promotionId, err := strconv.Atoi(query.PromotionName); err == nil && promotionId > 0 {
			or = append(or, bson.M{
				"promotion_id": promotionId,
			})
		}
		filter["$or"] = or
	}

	// searchBytes, _ := json.Marshal(filter)

	// log.Println("search promotion :", string(searchBytes))

	result := model.PromotionDB.Query(
		filter,
		offset,
		limit,
		&primitive.M{"_id": -1})
	if getTotal {
		countResult := model.PromotionDB.Count(filter)
		result.Total = countResult.Total
	}
	return result
}

// UpdatePromotionStatus update a promotion status
func UpdatePromotionStatus(input *model.Promotion, AccountID int64) *common.APIResponse {
	// Verify input data
	if input.PromotionID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "promotionId không được để trống.",
			ErrorCode: "INVALID_PROMOTION_ID_INPUT",
		}
	}

	if input.Status == nil || !isPromotionStatusValid(*input.Status) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái không hợp lệ.",
			ErrorCode: "STATUS_INVALID",
		}
	}

	// Get Promotion
	promotionResult := model.PromotionDB.QueryOne(
		bson.M{
			"promotion_id": input.PromotionID,
		},
	)

	if promotionResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chương trình khuyến mãi không tồn tại.",
			ErrorCode: "INVALID_PROMOTION_ID_INPUT",
		}
	}

	existedPromotion := promotionResult.Data.([]*model.Promotion)[0]

	// Check status hiên tại của promotion có được cập nhật không ?
	canUpdate := false
	switch *existedPromotion.Status {
	case enum.PromotionStatus.WAITING:
		canUpdate = true
		break
	case enum.PromotionStatus.ACTIVE:
		canUpdate = true
		break
	case enum.PromotionStatus.HIDE:
		canUpdate = true
		break
	}

	if !canUpdate {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chương trình khuyến mãi đã hết hạn. Không cho phép cập nhật trạng thái. Xin cảm ơn.",
			ErrorCode: "INVALID_PROMOTION_STATUS",
		}
	}

	now := time.Now()
	currentVersionNo := existedPromotion.VersionNo
	existedPromotion.UpdatedBy = AccountID
	existedPromotion.LastUpdatedTime = &now
	existedPromotion.VersionNo = uuid.New().String()
	existedPromotion.Status = input.Status

	afterOption := options.After
	updateResult := model.PromotionDB.UpdateOne(
		bson.M{
			"_id":        existedPromotion.ID,
			"version_no": currentVersionNo,
		},
		existedPromotion,
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		},
	)

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật trạng thái khuyến mãi thất bại.",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật trạng thái khuyến mãi thành công.",
		Data:    updateResult.Data.([]*model.Promotion),
	}
}

func isPromotionStatusValid(promotionStatus enum.PromotionStatusValue) bool {
	switch promotionStatus {
	case enum.PromotionStatus.ACTIVE:
		return true
	case enum.PromotionStatus.FULL:
		return true
	case enum.PromotionStatus.WAITING:
		return true
	case enum.PromotionStatus.DELETED:
		return true
	case enum.PromotionStatus.EXPIRED:
		return true
	case enum.PromotionStatus.HIDE:
		return true
	default:
		return false
	}
}

/*
GetListPromotion is func to get list promotion
@author: tuanv.tran
*/
func GetListPromotion(query *model.Promotion, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.PromotionDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.PromotionDB.Count(query).Total
	}
	return result
}

/*
PromotionCopy is func to copy promotion
*/
func PromotionCopy(accountID int64, promotionId int64) *common.APIResponse {
	promotionResult := model.PromotionDB.QueryOne(
		model.Promotion{PromotionID: promotionId},
	)

	if promotionResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   promotionResult.Message,
			ErrorCode: "NOT_FOUND",
		}
	}

	promotionData := promotionResult.Data.([]*model.Promotion)
	startTime := time.Now()
	endTime := startTime.AddDate(0, 1, 0)
	publicTime := startTime.AddDate(0, 0, 1)
	promotionData[0].ID = primitive.NilObjectID
	promotionData[0].Status = &enum.PromotionStatus.WAITING
	promotionData[0].PromotionID = model.GenId("PROMOTION_ID")
	promotionData[0].VersionNo = uuid.New().String()
	promotionData[0].LastUpdatedTime = nil
	promotionData[0].CreatedTime = nil
	promotionData[0].StartTime = &publicTime
	promotionData[0].EndTime = &endTime
	promotionData[0].PublicTime = &publicTime
	promotionData[0].CreatedBy = accountID

	return model.PromotionDB.Create(promotionData[0])
}

/*
MigrateHashTagPromotion is func to update hash tag
*/
func MigrateHashTagPromotion() *common.APIResponse {
	promotionRes := model.PromotionDB.Query(model.Promotion{}, 0, 0, nil)
	if promotionRes.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không tìm thấy chương trình khuyến mãi",
		}
	}

	promotions := promotionRes.Data.([]*model.Promotion)
	for _, promotion := range promotions {
		normNameStr := strings.Replace(utils.NormalizeString(promotion.PromotionName), " ", "-", -1)
		hashTag := fmt.Sprintf("%d-%s", promotion.PromotionID, normNameStr)

		updateResult := model.PromotionDB.UpdateOne(&model.Promotion{
			PromotionID: promotion.PromotionID,
		}, &model.Promotion{
			HashTag: hashTag,
		})

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Cập nhật khuyến mãi thất bại.",
			}
		}

		promotion.HashTag = hashTag
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật khuyến mãi thành công.",
		Data:    promotions,
	}
}
