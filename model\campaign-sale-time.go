package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CampaignSaleTime struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	StartTime    *time.Time                   `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime      *time.Time                   `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Code         string                       `json:"code,omitempty" bson:"code,omitempty"`
	NumDay       int                          `json:"numDay,omitempty" bson:"num_day,omitempty"`
	Kind         string                       `json:"kind,omitempty" bson:"kind,omitempty"`
	CampaignID   int64                        `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	CampaignCode string                       `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	ProductID    *int64                       `json:"productID,omitempty" bson:"product_id,omitempty"`
	SaleTime     []*CampaignFlashSaleTimeItem `json:"saleTime,omitempty" bson:"sale_time,omitempty"`
	Version      string                       `json:"version,omitempty" bson:"version,omitempty"`
	HashTag      string                       `json:"-" bson:"hash_tag,omitempty"`
}

var CampaignSaleTimeDB = &db.Instance{
	ColName:        "campaign_sale_time",
	TemplateObject: &CampaignSaleTime{},
}

// InitCampaignSaleTimeModel is func init model
func InitCampaignSaleTimeModel(dbInst *db.Instance, s *mongo.Database) {
	dbInst.ApplyDatabase(s)

	// t := true
	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
