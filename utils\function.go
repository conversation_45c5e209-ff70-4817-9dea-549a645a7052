package utils

import (
	"context"
	"sync"

	"github.com/robertkrimen/otto"
	"github.com/speps/go-hashids"
)

// Task ...
type Task func(ctx context.Context) error

// FetchTasks ...
func FetchTasks(tasks ...Task) error {
	errors := make([]error, len(tasks))
	wg := sync.WaitGroup{}
	wg.Add(len(tasks))

	ctx := context.Background()
	for i, task := range tasks {
		go func(i int, task func(ctx context.Context) error) {
			errors[i] = task(ctx)
			wg.Done()
		}(i, task)
	}
	wg.Wait()
	return collectErrors(errors)
}

func collectErrors(errors []error) error {
	if len(errors) == 0 {
		return nil
	}
	if len(errors) == 1 {
		return errors[0]
	}
	for _, err := range errors {
		if err != nil {
			return err
		}
	}
	return nil
}

// GenHashID ...
func GenHashID(numbers ...int) string {
	hd := hashids.NewData()
	hd.Salt = "thuocsi v2"
	hd.MinLength = 10
	h, _ := hashids.NewWithData(hd)
	e, _ := h.Encode(numbers)
	return e
}

func ConvertToRawText(text string) (raw string) {
	vm := otto.New()
	vm.Set("text", text)
	vm.Run(`
      text = text.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
      text = text.replace(/´/g, "");
      text = text.replace(/́/g, "");
      text = text.replace(/̣/g, "");
      text = text.replace(/̀/g, "");
      text = text.replace(/̉/g, "");
      text = text.replace(/̃/g, "");
   text = text.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
      text = text.replace(/ì|í|ị|ỉ|ĩ/g, "i");
      text = text.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
   text = text.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
      text = text.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
      text = text.replace(/đ/g, "d");
      text = text.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
   text = text.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
      text = text.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
      text = text.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
      text = text.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
      text = text.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
      text = text.replace(/Đ/g, "D");
   text = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // huyền, sắc, hỏi, ngã, nặng 
      text = str.replace(/\u02C6|\u0306|\u031B/g, ""); 
 `)
	if value, err := vm.Get("text"); err == nil {
		raw, _ = value.ToString()
	}
	return
}

func RoundPrice(val int) int {
	y := val % 100.0
	if y > 49 {
		return val - y + 100
	}
	return val - y
}

func RoundDownPrice(val int) int {
	y := val % 100.0
	return val - y
}
