package action

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils/generic"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const BATCH_SIZE = 3

type errRes struct {
	ErrorMessage      string                        `json:"errorMessage,omitempty"`
	ErrorCode         enum.VoucherErrorCodeType     `json:"errorCode,omitempty"`
	VoucherCode       string                        `json:"voucherCode,omitempty"`
	GroupCode         string                        `json:"groupCode,omitempty"`
	Discount          int                           `json:"discountValue,omitempty"`
	Gifts             []model.Gift                  `json:"gifts,omitempty"`
	CanUse            bool                          `json:"canUse,omitempty"`
	AutoApply         bool                          `json:"autoApply,omitempty"`
	MatchSeller       string                        `json:"matchSeller,omitempty"`
	MatchProducts     []model.ProductConditionField `json:"matchProducts,omitempty"`
	DiscountInfos     []model.DiscountInfo          `json:"discountInfos,omitempty"`
	Priority          *int                          `json:"-"`
	SellerCode        string                        `json:"sellerCode,omitempty"`
	SellerCodes       []string                      `json:"sellerCodes,omitempty"`
	ApplySkus         []string                      `json:"applySkus,omitempty"`
	NotApplySkus      []string                      `json:"notApplySkus,omitempty"`
	StoreCode         *string                       `json:"storeCode,omitempty"`
	PaymentMethod     string                        `json:"paymentMethod,omitempty"`
	VoucherName       string                        `json:"voucherName,omitempty"`
	Point             int                           `json:"point,omitempty"`
	NumberOfAutoApply int                           `json:"numberOfAutoApply,omitempty"`
	ChargeFee         string                        `json:"chargeFee,omitempty"`

	suggestInfo   suggestInfo
	hideDisplay   bool
	mapSkuMessage map[string]string
}

func removeVoucherFromList(vouchers []*model.VoucherViewWebOnly, voucher *model.VoucherViewWebOnly) []*model.VoucherViewWebOnly {
	newList := make([]*model.VoucherViewWebOnly, 0)
	for _, v := range vouchers {
		if v.Code != voucher.Code {
			newList = append(newList, v)
		}
	}
	return newList
}

func removeInvalidAutoApplyVouchersFromOrigin(inUses []*errRes, voucherGroupConnectionCases map[string]bool, setting *model.Setting, mapVoucherInCart map[string]bool) []*errRes {

	autoVoucherInUses := getAutoVoucherInUses(inUses, mapVoucherInCart)

	sortedAutoVouchers := sortAutoVouchersByPriorityDesc(autoVoucherInUses)

	checkedAutoVouchers := make([]*errRes, 0)
	numVouchersOfGroupMap := make(map[string]int)

	for _, inUse := range sortedAutoVouchers {
		if isVoucherFitInCheckedList(
			voucherGroupConnectionCases,
			inUse,
			checkedAutoVouchers,
			numVouchersOfGroupMap,
		) {
			checkedAutoVouchers = append(checkedAutoVouchers, inUse)
			numVouchersOfGroupMap[inUse.GroupCode+"."+inUse.SellerCode]++
		}
	}
	updatedInUses := make([]*errRes, 0)
	nAuto := setting.NumberOfVoucherManual
	nAutoSeller := setting.NumberOfVoucherManualPerSeller
	for _, v := range inUses {
		if _, ok := mapVoucherInCart[v.VoucherCode]; ok {
			if v.SellerCode == "" {
				nAuto = nAuto - 1
			} else {
				nAutoSeller = nAutoSeller - 1
			}
			updatedInUses = append(updatedInUses, v)
		}
	}
	setting.NumberOfVoucherAuto = nAuto
	setting.NumberOfVoucherAutoPerSeller = nAutoSeller
	validAutoVoucherMap := makeAutoVoucherMap(checkedAutoVouchers, setting)

	for _, inUse := range inUses {
		if validAutoVoucherMap[inUse.VoucherCode] && inUse.CanUse {
			updatedInUses = append(updatedInUses, inUse)
		}
	}

	return updatedInUses
}

func isVoucherFitInCheckedList(
	connectionCases map[string]bool,
	checking *errRes,
	checkedAutoVouchers []*errRes,
	numVouchersOfGroupMap map[string]int,
) bool {
	for _, voucher := range checkedAutoVouchers {
		if checking.SellerCode != voucher.SellerCode {
			continue
		}
		// 2 different groups, num vouchers equal to existing vouchers + 1.
		checkingCount := numVouchersOfGroupMap[checking.GroupCode+"."+checking.SellerCode] + 1

		// Same group, checking 1 with ammount of existing vouchers.
		if checking.GroupCode+"."+checking.SellerCode == voucher.GroupCode+"."+checking.SellerCode {
			checkingCount = 1
		}
		key := fmt.Sprintf(
			model.VGC_CASE_FORMAT,
			checking.GroupCode, checkingCount,
			voucher.GroupCode, numVouchersOfGroupMap[voucher.GroupCode+"."+checking.SellerCode],
		)
		key2 := fmt.Sprintf(
			model.VGC_CASE_FORMAT,
			voucher.GroupCode, numVouchersOfGroupMap[voucher.GroupCode+"."+checking.SellerCode],
			checking.GroupCode, checkingCount,
		)

		if !(connectionCases[key] || connectionCases[key2]) {
			return false
		}

	}

	return true
}

func makeAutoVoucherMap(autoVouchers []*errRes, setting *model.Setting) map[string]bool {
	validAutoVoucherMap := make(map[string]bool)
	nAuto := 1
	nAutoSeller := 0
	if setting != nil {
		nAuto = setting.NumberOfVoucherAuto
		nAutoSeller = setting.NumberOfVoucherAutoPerSeller
	}
	cAuto := 0
	cAutoSeller := 0
	for _, voucher := range autoVouchers {
		if voucher.SellerCode == "" && cAuto >= nAuto {
			continue
		}
		if voucher.SellerCode != "" && cAutoSeller >= nAutoSeller {
			continue
		}
		if voucher.AutoApply && voucher.CanUse {
			if voucher.SellerCode == "" {
				cAuto++
			} else {
				cAutoSeller++
			}
		}
		validAutoVoucherMap[voucher.VoucherCode] = true
	}
	return validAutoVoucherMap
}

func sortAutoVouchersByPriorityDesc(autoVoucherInUses []*errRes) []*errRes {
	sort.Slice(autoVoucherInUses, func(i, j int) bool {
		if autoVoucherInUses[i].Priority == nil {
			return false
		}
		if autoVoucherInUses[j].Priority == nil {
			return true
		}
		return *autoVoucherInUses[i].Priority > *autoVoucherInUses[j].Priority
	})
	return autoVoucherInUses
}

func getAutoVoucherInUses(inUses []*errRes, mapVoucherInCart map[string]bool) []*errRes {
	autoVoucherInUses := make([]*errRes, 0)
	for _, inUse := range inUses {
		if inUse.AutoApply && !mapVoucherInCart[inUse.VoucherCode] {
			autoVoucherInUses = append(autoVoucherInUses, inUse)
		}
	}
	return autoVoucherInUses
}

// checkVoucherGroupMapInUseFromSetting validates inUses of voucher by voucherGroupConnectionCases,
// return updated InUses and nonUses.
// A nonUse voucher in this list is pointed to the same at the one in inUses.
func checkVoucherGroupMapInUseFromSetting(inUses []*errRes, voucherGroupConnectionCases map[string]bool) (_ []*errRes, nonUses []*errRes) {
	if voucherGroupConnectionCases == nil {
		voucherGroupConnectionCases = make(map[string]bool)
	}

	mapGroupVoucherInUse := make(map[string]int)
	keyFormat := model.VGC_CASE_FORMAT
	for _, inUse := range inUses {
		mapGroupVoucherInUse[inUse.GroupCode+"."+inUse.SellerCode] = mapGroupVoucherInUse[inUse.GroupCode+"."+inUse.SellerCode] + 1
	}

	for k, v := range mapGroupVoucherInUse {
		sellerCode := strings.Split(k, ".")[1]
		k = strings.Split(k, ".")[0]
		for _, nonUse := range inUses {
			if !nonUse.CanUse {
				continue
			}
			if nonUse.SellerCode != sellerCode {
				continue
			}
			key := fmt.Sprintf(keyFormat, k, v, nonUse.GroupCode, 1)
			key2 := fmt.Sprintf(keyFormat, nonUse.GroupCode, 1, k, v)
			if nonUse.GroupCode == k {
				if v-1 == 0 {
					continue
				}
				key = fmt.Sprintf(keyFormat, k, v-1, nonUse.GroupCode, 1)
				key2 = fmt.Sprintf(keyFormat, nonUse.GroupCode, 1, k, v-1)
			}

			if !(voucherGroupConnectionCases[key] || voucherGroupConnectionCases[key2]) {
				nonUses = append(nonUses, nonUse)
			}
		}
	}

	return inUses, nonUses
}

var blackListViewVoucherDiscountMap = map[int64]bool{
	//233324: true,
	//213150: true,
}

func PromotionUpdate(acc *model.Account, update *model.Promotion) *common.APIResponse {
	if update.PromotionID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing id",
			ErrorCode: "MISSING_ID",
		}
	}

	if update.IsReuseOnOrderCancel != nil && *update.IsReuseOnOrderCancel && (update.MaxUsagePerCustomer == nil || *update.MaxUsagePerCustomer == 0) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số lần sử dụng tối đa trên mỗi khách hàng không được để trống hoặc bằng 0 khi chọn điều kiện hoàn lại mã đơn.",
			ErrorCode: "MAX_USAGE_PER_CUSTOMER_REQUIRE_WHEN_REUSE_ON_ORDER_CANCEL",
		}
	}

	query := model.Promotion{
		PromotionID: update.PromotionID,
	}
	qPromotion := model.PromotionDB.QueryOne(query)
	if qPromotion.Status != common.APIStatus.Ok {
		return qPromotion
	}
	promotion := qPromotion.Data.([]*model.Promotion)[0]
	if update.PromotionName == "" {
		update.PromotionName = promotion.PromotionName
	}
	if update.Description == "" {
		update.Description = promotion.Description
	}
	update.UpdatedBy = acc.AccountID
	normNameStr := strings.Replace(utils.NormalizeString(update.PromotionName), " ", "-", -1)
	normDesStr := strings.Replace(utils.NormalizeString(update.Description), " ", "-", -1)
	normShortNameStr := strings.Replace(utils.NormalizeString(update.ShortName), " ", "-", -1)
	update.HashTag = fmt.Sprintf("%d-%s-%s-%s", update.PromotionID, normNameStr, normDesStr, normShortNameStr)
	res := model.PromotionDB.UpdateOne(query, update)
	return res
}

func PromotionCreate(acc *model.Account, promotion *model.Promotion) *common.APIResponse {
	if promotion.IsReuseOnOrderCancel != nil && *promotion.IsReuseOnOrderCancel && (promotion.MaxUsagePerCustomer == nil || *promotion.MaxUsagePerCustomer == 0) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số lần sử dụng tối đa trên mỗi khách hàng không được để trống hoặc bằng 0 khi chọn điều kiện hoàn lại mã đơn.",
			ErrorCode: "MAX_USAGE_PER_CUSTOMER_REQUIRE_WHEN_REUSE_ON_ORDER_CANCEL",
		}
	}

	promotion.CreatedBy = acc.AccountID
	normNameStr := strings.Replace(utils.NormalizeString(promotion.PromotionName), " ", "-", -1)
	normDescriptionStr := strings.Replace(utils.NormalizeString(promotion.Description), " ", "-", -1)
	normShortNameStr := strings.Replace(utils.NormalizeString(promotion.ShortName), " ", "-", -1)
	promotion.HashTag = fmt.Sprintf("%d-%s-%s-%s", promotion.PromotionID, normNameStr, normDescriptionStr, normShortNameStr)
	promotion.PromotionID = model.GenId("PROMOTION_ID")
	res := model.PromotionDB.Create(promotion)
	return res
}

func VoucherActiveList(query *model.Voucher, input *model.MeVoucherListRequest) *common.APIResponse {
	var (
		setting            *model.Setting
		customer           *model.Customer
		customerExperience *model.CustomerExperience
		lastOrder          *model.Order
		errGetCustomer, _  error

		mapUsed              = make(map[string]*model.UserPromotion)
		voucherCodes         = make([]string, 0)
		inactiveVoucherCodes = make([]string, 0)
		vouchersInUse        []*model.Voucher

		wg              sync.WaitGroup
		muVouchersInUse sync.Mutex
		customerChan    = make(chan *model.Customer, 1)
	)

	// Init setting
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		qSetting := model.SettingDB.QueryOne(model.Setting{SystemDisplay: query.SystemDisplay})
		if qSetting.Status == common.APIStatus.Ok {
			setting = qSetting.Data.([]*model.Setting)[0]
		}
	})

	// Init customer
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()
		defer close(customerChan)

		customer, errGetCustomer = client.Services.Customer.GetCustomerByAccountID(input.AccountID)
		customerChan <- customer
	})

	// Init customer experience
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		customerExperience, _ = client.Services.DataHarvest.GetCustomerExperience(0, input.AccountID)
	})

	// Init last order
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		lastOrder, _ = client.Services.Order.GetLastOrderSuccessByAccountID(input.AccountID)
	})

	// Init used vouchers
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		customerID := input.Cart.CustomerID
		if customerID == 0 {
			customerID = (<-customerChan).CustomerID
		}

		queryUsed := model.UserPromotion{
			CustomerID: customerID,
			ComplexQuery: []*bson.M{
				{"voucher_status": enum.VoucherStatus.ACTIVE},
				{"$or": []bson.M{
					{"customer_apply_type": enum.CustomerApplyType.ALL},
					{"$and": []bson.M{
						{"customer_apply_type": enum.CustomerApplyType.MANY},
						{"status": bson.M{"$ne": enum.CodeStatus.DELETED}},
					}},
				}},
			},
		}
		if len(input.Codes) > 0 {
			queryUsed.ComplexQuery = append(queryUsed.ComplexQuery, &bson.M{
				"voucher_code": bson.M{"$in": input.Codes},
			})
		}

		limit := int64(0)
		opt := &options.FindOptions{Limit: &limit}

		res := model.UserPromotionCacheReadDB.QueryWithOptions(queryUsed, opt)

		if res.Status == common.APIStatus.Ok {
			for _, used := range res.Data.([]*model.UserPromotion) {
				if used.Status != nil && *used.Status == enum.CodeStatus.INACTIVE {
					inactiveVoucherCodes = append(inactiveVoucherCodes, used.VoucherCode)
				} else {
					voucherCodes = append(voucherCodes, used.VoucherCode)
					mapUsed[used.VoucherCode] = used
				}
			}
		}
	})

	// Init vouchers being used in cart, from cache
	if len(input.Cart.RedeemCode) > 0 {
		wg.Add(1)
		go sdk.Execute(func() {
			defer wg.Done()

			codesBatch := generic.ChunkSlice(input.Cart.RedeemCode, BATCH_SIZE)
			sema := utils.NewSemaphore(10, len(codesBatch))
			for _, codes := range codesBatch {
				func(codes []string) {
					sema.Acquire()
					go sdk.Execute(func() {
						defer sema.Release()

						query := bson.M{"code": bson.M{"$in": codes}}

						limit := int64(0)
						opt := &options.FindOptions{Limit: &limit}

						qVoucherInUse := model.VoucherCacheReadDB.QueryWithOptions(query, opt)
						if qVoucherInUse.Status == common.APIStatus.Ok {
							muVouchersInUse.Lock()
							vouchersInUse = append(vouchersInUse, qVoucherInUse.Data.([]*model.Voucher)...)
							muVouchersInUse.Unlock()
						}
					})
				}(codes)
			}
			sema.Wait()
		})
	}

	wg.Wait()

	if errGetCustomer != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   errGetCustomer.Error(),
			ErrorCode: "GET_CUSTOMER_ERROR",
		}
	}
	if lastOrder != nil {
		customer.OrderCount = lastOrder.CustomerOrderIndex
		customer.LastOrderTime = lastOrder.CreatedTime
	} else {
		customer.OrderCount = 0
		customer.LastOrderTime = nil
	}

	isSkipVoucherApplyAll := false
	if setting != nil && setting.SkipUseVoucher != nil {
		isSkipVoucherApplyAll = isSkipByCustomer(customer, *setting.SkipUseVoucher)
	}

	if input.Cart != nil {
		input.Cart.CustomerLevel = utils.ParseStringToPointer(customer.Level)
		if input.Cart.ProvinceCode == "" {
			input.Cart.ProvinceCode = customer.ProvinceCode
		}
		if input.Cart.CustomerScope == "" {
			input.Cart.CustomerScope = customer.Scope
		}
		if input.Cart.RedeemCode == nil {
			input.Cart.RedeemCode = make([]string, 0)
		}
	}

	// Get vouchers from cache
	mapVoucher, queryMapVoucher, voucherDatas, qVoucher := getVouchersDataFromCache(query, input, customer, inactiveVoucherCodes, voucherCodes)

	if len(voucherDatas) == 0 {
		return &common.APIResponse{
			Status: common.APIStatus.Ok,
			Data:   []*model.VoucherViewWebOnly{},
			Total:  0,
		}
	}

	if len(vouchersInUse) > 0 {
		for _, voucher := range vouchersInUse {
			if _, ok := mapVoucher[voucher.Code]; !ok {
				mapVoucher[voucher.Code] = voucher
				voucherDatas = append(voucherDatas, voucher)
			}
		}
	}

	dataResponse := make([]*model.VoucherViewWebOnly, 0)
	if input.GetValidate {
		for voucherCode, voucher := range mapVoucher {
			value := isValidVoucher(voucherCode, voucher, mapUsed[voucherCode], input.Cart, customer, setting, customerExperience)
			if value.hideDisplay {
				delete(mapVoucher, voucherCode)
			}
			voucher.CanUse = true
			voucher.ErrorMessage = value.ErrorMessage
			voucher.ErrorCode = string(value.ErrorCode)
			if value.ErrorMessage != "" {
				voucher.CanUse = false
			}
			if input.Cart.IsCartEmpty {
				voucher.ErrorMessage = ""
			}
			voucher.Gifts = value.Gifts
			voucher.Discount = value.Discount
		}
	}

	voucherInUseMap := make(map[string]bool)
	voucherInUses := make([]*model.VoucherViewWebOnly, 0)
	for _, voucher := range input.Cart.RedeemCode {
		voucherInUseMap[voucher] = true
	}
	for _, v := range voucherDatas {
		voucher := mapVoucher[v.Code]
		used := mapUsed[v.Code]
		if voucher == nil {
			continue
		}
		if used != nil && voucher.MaxUsagePerCustomer != nil && *voucher.MaxUsagePerCustomer != 0 && used.Amount != nil && *used.Amount >= *voucher.MaxUsagePerCustomer {
			continue
		}

		if input.GetMedxVoucherOnly && (voucher.StoreCode != nil && len(*voucher.StoreCode) > 0) {
			continue
		}

		if input.GetVoucherSpecific && (voucher.IsSpecific == nil || !*voucher.IsSpecific) {
			continue
		}

		if input.GetVoucherToCollect {
			isSkipVoucherCollect := false
			if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.NotInSkus != nil {
				for _, notInSku := range *voucher.ApplyDiscount.NotInSkus {
					if notInSku == input.Sku {
						isSkipVoucherCollect = true
						break
					}
				}
			}
			if isSkipVoucherCollect {
				continue
			}
		}
		if voucher.CustomerApplyType == enum.CustomerApplyType.ALL && isSkipVoucherApplyAll {
			continue
		}

		viewVoucher := setViewData(voucher, nil)
		if used == nil && blackListViewVoucherDiscountMap[customer.CustomerID] && len(viewVoucher.Gifts) == 0 {
			continue
		}
		if used != nil {
			viewVoucher.IsCollected = used.IsCollected
			if !viewVoucher.IsOwner && viewVoucher.IsCollected != nil && *viewVoucher.IsCollected {
				viewVoucher.CollectStatus = "COLLECTED"
			}
		}

		if data, ok := voucherInUseMap[v.Code]; data && ok {
			viewVoucher.ActionStatus = "INUSE"
			voucherInUses = append(voucherInUses, viewVoucher)
		} else {
			dataResponse = append(dataResponse, viewVoucher)
		}
		// update data for app old version
		if v.CustomerApplyType == enum.CustomerApplyType.MANY {
			viewVoucher.Voucher.AppliedCustomers = []int64{customer.CustomerID}
		}
		viewVoucher.Voucher.PromotionName = viewVoucher.Description
		viewVoucher.Voucher.StartTime = viewVoucher.StartTime
		viewVoucher.Voucher.EndTime = viewVoucher.EndTime
		viewVoucher.Voucher.Code = viewVoucher.Code
		viewVoucher.Voucher.Promotion.Description = viewVoucher.Description
	}

	voucherInUses, dataResponse = checkVoucherGroupMap(voucherInUses, dataResponse, input.Cart, setting)
	getDiscount := func(gifts []model.Gift) int {
		total := 0
		for _, gift := range gifts {
			total += int(gift.GiftValue)
		}
		return total
	}

	sort.SliceStable(dataResponse, func(i, j int) bool {
		if input.SortByDiscount != "" {
			input.KeepCurrentSort = true
			iDiscount := float64(dataResponse[i].Discount)
			if dataResponse[i].MinOrderValue > 0 {
				iDiscount = float64(dataResponse[i].Discount) / float64(dataResponse[i].MinOrderValue)
			}
			jDiscount := float64(dataResponse[j].Discount)
			if dataResponse[j].MinOrderValue > 0 {
				jDiscount = float64(dataResponse[j].Discount) / float64(dataResponse[j].MinOrderValue)
			}
			if input.SortByDiscount == "asc" {
				return iDiscount < jDiscount
			} else {
				return iDiscount > jDiscount
			}
		} else if input.GetVoucherToCollect {
			// sort by apply type
			input.KeepCurrentSort = true
			if dataResponse[i].IsOwner == dataResponse[j].IsOwner {
				iDiscount := dataResponse[i].Discount
				if iDiscount == 0 {
					iDiscount = getDiscount(dataResponse[i].Gifts)
				}
				jDiscount := dataResponse[j].Discount
				if jDiscount == 0 {
					jDiscount = getDiscount(dataResponse[j].Gifts)
				}
				if iDiscount > jDiscount {
					return true
				} else if iDiscount < jDiscount {
					return false
				} else {
					return dataResponse[i].Code < dataResponse[j].Code
				}
			}
			return !dataResponse[i].IsOwner && dataResponse[j].IsOwner
		} else {
			// default sort by can use & discount
			if dataResponse[i].CanUse == dataResponse[j].CanUse {
				if dataResponse[i].CanUse || input.Cart.IsCartEmpty {
					iDiscount := dataResponse[i].Discount
					if iDiscount == 0 {
						iDiscount = getDiscount(dataResponse[i].Gifts)
					}
					jDiscount := dataResponse[j].Discount
					if jDiscount == 0 {
						jDiscount = getDiscount(dataResponse[j].Gifts)
					}
					return iDiscount > jDiscount
				} else {
					iDiscount := int(dataResponse[i].MinOrderValue)
					jDiscount := int(dataResponse[j].MinOrderValue)
					return iDiscount < jDiscount
				}
			}
			if dataResponse[i].CanUse {
				return true
			}
		}
		return false
	})
	dataResponse = append(voucherInUses, dataResponse...)

	if len(input.Cart.RedeemCode) > 0 {
		dataResponse, qVoucher = removeInUsesVouchersThatNotMatchSearch(
			dataResponse, qVoucher, input.Cart.RedeemCode, queryMapVoucher)
	}

	// Auto apply vouchers should not display in the active list because Users can not apply them manually.
	if !input.GetVoucherAuto {
		dataResponse, qVoucher = removeAutoApplyVouchers(dataResponse, qVoucher)
	}
	sort.SliceStable(dataResponse, func(i, j int) bool {
		return dataResponse[i].SellerCode != "" && dataResponse[j].SellerCode == "" // i trước j
	})
	if !input.KeepCurrentSort {
		dataResponse = SortVoucher(dataResponse, customer, customerExperience, input.Cart, input.SortType)
	}

	validVouchers, total := filterValidVouchers(dataResponse, input)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    validVouchers,
		Message: qVoucher.Message,
		Total:   int64(total),
	}
}

func getVouchersDataFromCache(query *model.Voucher, input *model.MeVoucherListRequest, customer *model.Customer, inactiveVoucherCodes []string, voucherCodes []string) (mapVoucher map[string]*model.Voucher, queryMapVoucher map[string]*model.Voucher, voucherDatas []*model.Voucher, qVoucher *common.APIResponse) {

	now := time.Now()
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"public_time": bson.M{"$lte": now},
		"end_time":    bson.M{"$gte": now},
		"filter":      bson.M{"$all": []string{customer.ProvinceCode, customer.Level, customer.Scope}},
	})

	query.Status = &enum.VoucherStatus.ACTIVE

	if len(inactiveVoucherCodes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"code": bson.M{"$nin": inactiveVoucherCodes},
		})
	}

	switch input.Scope {
	case "me":
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"customer_apply_type": enum.CustomerApplyType.MANY,
			"code":                bson.M{"$in": voucherCodes},
		})
	case "other":
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"customer_apply_type": enum.CustomerApplyType.ALL,
		})
	case "seller":
		if input.GetBySellerInCart {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"seller_codes": bson.M{"$in": input.Cart.SellerCodes},
			})
		} else if input.SellerCode != "" {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"seller_codes": input.SellerCode,
			})
		} else if input.StoreCode != "" {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"store_code": input.StoreCode,
			})
		} else {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"promotion_organizer": bson.M{"$nin": []string{"", "MARKETING"}},
			})
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.MANY,
					"code":                bson.M{"$in": voucherCodes},
				},
				{
					"customer_apply_type": enum.CustomerApplyType.ALL,
				},
			},
		})
	case "marketplace":
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"promotion_organizer": bson.M{"$in": []string{"", "MARKETING"}},
			"$or": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.MANY,
					"code":                bson.M{"$in": voucherCodes},
				},
				{
					"customer_apply_type": enum.CustomerApplyType.ALL,
				},
			},
		})
	case "product-detail":
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.MANY,
					"code":                bson.M{"$in": voucherCodes},
				},
				{
					"customer_apply_type": enum.CustomerApplyType.ALL,
				},
			},
		})
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"seller_codes": input.SellerCode,
				},
				{
					"promotion_organizer": bson.M{"$in": []string{"", "MARKETING"}},
				},
			},
		})
	default:
		if len(input.Codes) > 0 {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"code": bson.M{"$in": input.Codes},
			})
		} else {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"$or": []*bson.M{
					{
						"customer_apply_type": enum.CustomerApplyType.MANY,
						"code":                bson.M{"$in": voucherCodes},
					},
					{
						"customer_apply_type": enum.CustomerApplyType.ALL,
					},
				},
			})
		}
	}

	// query all voucher
	mapVoucher = make(map[string]*model.Voucher)
	queryMapVoucher = make(map[string]*model.Voucher)
	voucherDatas = make([]*model.Voucher, 0)

	lastEndTime := time.Time{}
	for {
		if !lastEndTime.IsZero() {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"end_time": bson.M{"$gt": lastEndTime},
			})
		}

		limit := int64(0)
		opt := &options.FindOptions{
			Limit: &limit,
			Sort:  bson.M{"end_time": 1},
		}
		qVoucher = model.VoucherCacheReadDB.QueryWithOptions(query, opt)

		if qVoucher.Status != common.APIStatus.Ok {
			break
		}

		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			lastEndTime = *voucher.EndTime
			mapVoucher[voucher.Code] = voucher
			queryMapVoucher[voucher.Code] = voucher
			voucherDatas = append(voucherDatas, voucher)
		}
		if len(voucherDatas) < 1000 {
			break
		}
	}

	return
}

// filterValidVouchers filters a list of VoucherViewWebOnly based on the criteria specified in the MeVoucherListRequest input.
// It returns a slice of valid VoucherViewWebOnly and the total number of vouchers after filtering.
//
// Parameters:
// - viewWebOnlyVouchers: A slice of pointers to VoucherViewWebOnly that need to be filtered.
// - input: A pointer to MeVoucherListRequest containing the filtering criteria.
//
// Returns:
// - A slice of pointers to VoucherViewWebOnly that meet the filtering criteria.
// - An integer representing the total number of vouchers after filtering.
//
// Filtering criteria include:
// - Scope: If "collected", only include vouchers that are collected.
// - SkipVoucherCollected: If true, exclude vouchers that are collected.
// - SkipVoucherOwner: If true, exclude vouchers that are owned by the user.
// - SkipVoucherGift: If true, exclude vouchers that have gifts.
// - SkipVoucherByProduct: If true, exclude vouchers that are by product.
// - GetVoucherProductOnly: If true, only include vouchers that are by product.
// - SkipVoucherByPayment: If true, exclude vouchers that have a payment method.
// - GetVoucherOrderOnly: If true, only include vouchers that are by order.
// - RemoveVoucherDuplicateProduct: If true, remove duplicate vouchers based on product SKU or tags.
// - Offset: Skip the first N vouchers.
// - Limit: Include only up to N vouchers after the offset.
func filterValidVouchers(viewWebOnlyVouchers []*model.VoucherViewWebOnly, input *model.MeVoucherListRequest) ([]*model.VoucherViewWebOnly, int) {
	validVouchers := make([]*model.VoucherViewWebOnly, 0)
	total := len(viewWebOnlyVouchers)
	count := 0
	mapRefSku := make(map[string]bool)
	mapRefTag := make(map[string]bool)
	for _, viewVoucher := range viewWebOnlyVouchers {
		if input.Scope == "collected" {
			if viewVoucher.IsCollected == nil || !*viewVoucher.IsCollected {
				total--
				continue
			}
		}
		if input.SkipVoucherCollected {
			if viewVoucher.IsCollected != nil && *viewVoucher.IsCollected {
				total--
				continue
			}
		}
		if input.SkipVoucherOwner {
			if viewVoucher.IsOwner {
				total--
				continue
			}
		}
		if input.SkipVoucherGift {
			if len(viewVoucher.Gifts) > 0 {
				total--
				continue
			}
		}
		if input.SkipVoucherByProduct {
			if viewVoucher.IsVoucherByProduct {
				total--
				continue
			}
		}
		if input.GetVoucherProductOnly {
			if !viewVoucher.IsVoucherByProduct {
				total--
				continue
			}
		}
		if input.SkipVoucherByPayment {
			if viewVoucher.PaymentMethod != "" {
				total--
				continue
			}
		}
		if input.GetVoucherOrderOnly {
			if !viewVoucher.IsVoucherByOrder {
				total--
				continue
			}
		}
		if input.RemoveVoucherDuplicateProduct {
			skuCode := ""
			if len(viewVoucher.RefSkus) == 1 && len(viewVoucher.OrTags) == 0 && len(viewVoucher.AndTags) == 0 {
				skuCode = viewVoucher.RefSkus[0]
				if _, ok := mapRefSku[skuCode]; ok {
					total--
					continue
				} else {
					mapRefSku[skuCode] = true
				}
			}
			if len(viewVoucher.OrTags) == 1 && len(viewVoucher.RefSkus) == 0 && len(viewVoucher.AndTags) == 0 {
				tag := ""
				for tagC := range viewVoucher.OrTags {
					tag = tagC
					break
				}
				if _, ok := mapRefTag[tag]; ok {
					total--
					continue
				} else {
					mapRefTag[tag] = true
				}
			}
			if len(viewVoucher.AndTags) == 1 && len(viewVoucher.RefSkus) == 0 && len(viewVoucher.OrTags) == 0 {
				tag := ""
				for tagC := range viewVoucher.AndTags {
					tag = tagC
					break
				}
				if _, ok := mapRefTag[tag]; ok {
					total--
					continue
				} else {
					mapRefTag[tag] = true
				}
			}
		}
		count++
		// skip with offset, limit
		//if groupBy != "seller" {
		if input.Offset > 0 && int64(count) <= input.Offset {
			continue
		}
		if input.Limit > 0 && int64(count) > input.Offset+input.Limit {
			break
		}
		//}

		validVouchers = append(validVouchers, viewVoucher)
	}

	return validVouchers, total
}

func removeAutoApplyVouchers(
	dataResponse []*model.VoucherViewWebOnly,
	qVoucher *common.APIResponse) ([]*model.VoucherViewWebOnly, *common.APIResponse) {
	updatedDataRes := make([]*model.VoucherViewWebOnly, 0)

	for _, voucher := range dataResponse {
		if voucher.ApplyType == enum.ApplyType.AUTO {
			qVoucher.Total--
			continue
		}

		updatedDataRes = append(updatedDataRes, voucher)
	}

	return updatedDataRes, qVoucher
}

func removeInUsesVouchersThatNotMatchSearch(dataResponse []*model.VoucherViewWebOnly, qVoucher *common.APIResponse, redeemCode []string, queryMapVoucher map[string]*model.Voucher) ([]*model.VoucherViewWebOnly, *common.APIResponse) {
	voucherCodeMaptoViewVoucher := make(map[string]*model.VoucherViewWebOnly)
	for _, viewVoucher := range dataResponse {
		voucherCodeMaptoViewVoucher[viewVoucher.Code] = viewVoucher
	}

	for _, voucherCode := range redeemCode {
		if queryMapVoucher[voucherCode] == nil {
			delete(voucherCodeMaptoViewVoucher, voucherCode)
		}
	}

	newDataResponse := make([]*model.VoucherViewWebOnly, 0)
	for _, voucher := range dataResponse {
		if voucherCodeMaptoViewVoucher[voucher.Code] == nil {
			continue
		}
		newDataResponse = append(newDataResponse, voucher)
	}
	qVoucher.Total = int64(len(newDataResponse))

	return newDataResponse, qVoucher
}

func SelfVoucherActiveList(accountID int64, query *model.Voucher, offset, limit int64, getTotal bool, scope string, sourceDetail *model.OrderSourceDetail) *common.APIResponse {

	if query.SystemDisplay == "" {
		query.SystemDisplay = "BUYMED"
	} else if query.SystemDisplay == "ALL" {
		query.SystemDisplay = ""
	}

	customer, err := client.Services.Customer.GetCustomerByAccountID(accountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "GET_CUSTOMER_ERROR",
		}
	}
	queryUsed := model.UserPromotion{
		CustomerID: customer.CustomerID,
		ComplexQuery: []*bson.M{
			{
				//"status":         bson.M{"$nin": []string{string(enum.CodeStatus.INACTIVE)}},
				"voucher_status": enum.VoucherStatus.ACTIVE,
			},
			{
				"$or": []bson.M{
					{
						"customer_apply_type": enum.CustomerApplyType.ALL,
					},
					{
						"$and": []bson.M{
							{
								"customer_apply_type": enum.CustomerApplyType.MANY,
							},
							{
								"status": bson.M{"$ne": enum.CodeStatus.DELETED},
							},
						},
					},
				},
			},
		},
	}
	qUsed := model.UserPromotionCacheDB.Query(queryUsed, 0, 0, nil)
	mapUsed := make(map[string]*model.UserPromotion)
	voucherCodes := make([]string, 0)
	inactiveVoucherCodes := make([]string, 0)
	if qUsed.Status == common.APIStatus.Ok {
		for _, used := range qUsed.Data.([]*model.UserPromotion) {
			if used.Status != nil && *used.Status == enum.CodeStatus.INACTIVE {
				inactiveVoucherCodes = append(inactiveVoucherCodes, used.VoucherCode)
			} else {
				voucherCodes = append(voucherCodes, used.VoucherCode)
				mapUsed[used.VoucherCode] = used
			}
		}
	}
	now := time.Now()
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"public_time": bson.M{"$lte": now},
		"end_time":    bson.M{"$gte": now},
		"status":      "ACTIVE",
		"filter":      bson.M{"$all": []string{customer.ProvinceCode, customer.Level, customer.Scope}},
		"apply_type":  bson.M{"$ne": enum.ApplyType.AUTO},
		"code":        bson.M{"$nin": inactiveVoucherCodes},
	})

	switch scope {
	case "me":
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$and": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.MANY,
					"code":                bson.M{"$in": voucherCodes},
				},
			},
		})
	case "other":
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.ALL,
				},
			},
		})
	default:
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"customer_apply_type": enum.CustomerApplyType.MANY,
					"code":                bson.M{"$in": voucherCodes},
				},
				{
					"customer_apply_type": enum.CustomerApplyType.ALL,
				},
			},
		})
	}

	mapVoucher := make(map[string]*model.Voucher)
	qVoucher := model.VoucherCacheReadDB.Query(query, offset, limit, &primitive.M{"end_time": 1})
	if qVoucher.Status == common.APIStatus.Ok {
		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			mapVoucher[voucher.Code] = voucher
		}
	} else {
		return qVoucher
	}

	if getTotal {
		qVoucher.Total = model.VoucherCacheDB.Count(query).Total
	}
	dataResponse := make([]*model.VoucherViewWebOnly, 0)

	for _, v := range qVoucher.Data.([]*model.Voucher) {
		voucher := mapVoucher[v.Code]
		used := mapUsed[v.Code]
		if used != nil && voucher.MaxUsagePerCustomer != nil && *voucher.MaxUsagePerCustomer != 0 && used.Amount != nil && *used.Amount >= *voucher.MaxUsagePerCustomer {
			continue
		}
		viewVoucher := setViewData(voucher, nil)
		if used == nil && blackListViewVoucherDiscountMap[customer.CustomerID] && len(viewVoucher.Gifts) == 0 {
			continue
		}
		dataResponse = append(dataResponse, viewVoucher)
		// update data for app old version
		if v.CustomerApplyType == enum.CustomerApplyType.MANY {
			viewVoucher.Voucher.AppliedCustomers = []int64{customer.CustomerID}
		}
		viewVoucher.Voucher.PromotionName = viewVoucher.Description
		viewVoucher.Voucher.StartTime = viewVoucher.StartTime
		viewVoucher.Voucher.EndTime = viewVoucher.EndTime
		viewVoucher.Voucher.Code = viewVoucher.Code
		viewVoucher.Voucher.Promotion.Description = viewVoucher.Description
	}
	getDiscount := func(gifts []model.Gift) int {
		total := 0
		for _, gift := range gifts {
			total += int(gift.GiftValue)
		}
		return total
	}
	sort.Slice(dataResponse, func(i, j int) bool {
		if dataResponse[i].CanUse == dataResponse[j].CanUse {
			iDiscount := dataResponse[i].Discount
			if iDiscount == 0 {
				iDiscount = getDiscount(dataResponse[i].Gifts)
			}
			jDiscount := dataResponse[j].Discount
			if jDiscount == 0 {
				jDiscount = getDiscount(dataResponse[j].Gifts)
			}
			if iDiscount > jDiscount {
				return true
			} else if iDiscount < jDiscount {
				return false
			}
		}
		if dataResponse[i].CanUse {
			return true
		}
		return false
	})
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    dataResponse,
		Message: qVoucher.Message,
		Total:   qVoucher.Total,
	}
}

// * PRIVATE FUNC //
func isValidVoucherScope(cart model.Cart, scopes []model.Scope) (isValid bool, errCode enum.VoucherErrorCodeType, msg string) {
	isCheckArea := len(cart.RegionCodes) > 0
	cart.RegionCodes = append(cart.RegionCodes, cart.ProvinceCode)
	for _, scope := range scopes {
		isValid = false
		if scope.Type == nil || (scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.ALL) {
			isValid, errCode, msg = checkAppVersion(&cart, scope.MinClientVersion)
			if !isValid {
				return
			}
			isValid = true
			continue
		}
		switch *scope.Type {
		case enum.ScopeType.AREA:
			if isCheckArea {
				mapAreaCode := make(map[string]bool)
				for _, v := range scope.AreaCodes {
					mapAreaCode[v] = true
				}
				for _, v := range cart.RegionCodes {
					if data, ok := mapAreaCode[v]; ok && data {
						isValid = true
					}
				}
				if isValid {
					continue
				}
				isValid = false
				msg = "Bạn không đủ điều kiện sử dụng mã"
				return
			}
		case enum.ScopeType.CUSTOMER_LEVEL:
			for _, v := range scope.CustomerLevelCodes {
				if cart.CustomerLevel != nil && v == *cart.CustomerLevel {
					isValid = true
				}
			}
			if isValid {
				continue
			}
			isValid = false
			msg = "Bạn không đủ điều kiện sử dụng mã"
			return
		case enum.ScopeType.CUSTOMER_SCOPE:
			for _, v := range scope.CustomerScopes {
				if v == cart.CustomerScope {
					isValid = true
				}
			}
			if isValid {
				continue
			}
			isValid = false
			msg = "Bạn không đủ điều kiện sử dụng mã"
			return
		case enum.ScopeType.DISPLAY_PLATFORM:
			isValid, errCode, msg = validateVoucherByPlatforms(cart, scope.DisplayPlatforms, scope.MinClientVersion)
			return
		default:
			isValid = true
			continue
		}
	}
	return
}

func checkAppVersion(cart *model.Cart, minVersion *string) (bool, enum.VoucherErrorCodeType, string) {
	if cart.SourceDetail != nil && cart.SourceDetail.Platform == string(enum.Platform.MOBILE_APP) {
		if minVersion != nil && *minVersion != "" && cart.SourceDetail.BrowserVersion != "" &&
			utils.CompareVersion(cart.SourceDetail.BrowserVersion, *minVersion) < 0 {
			return false, enum.VoucherErrorCode.INVALID_APP_VERSION, "Cập nhật ứng dụng để sử dụng mã"
		}
	}
	return true, "", ""
}

func validateVoucherByPlatforms(cart model.Cart, displayPlatforms []enum.PlatformType, minVersion *string) (bool, enum.VoucherErrorCodeType, string) {
	if cart.SourceDetail == nil || cart.SourceDetail.Platform == "" {
		return true, "", ""
	}

	isValid, errCodeDefault, msg := false, enum.VoucherErrorCode.INVALID_APP_VERSION, "Áp dụng cho thiết bị khác theo điều kiện"

	if len(displayPlatforms) == 0 {
		isMobileValid, errCode, mobileMsg := checkAppVersion(&cart, minVersion)

		if !isMobileValid {
			return isMobileValid, errCode, mobileMsg
		}

		return true, "", ""
	} else {
		for _, p := range displayPlatforms {
			if p == enum.Platform.MOBILE_APP {
				isMobileValid, errCode, mobileMsg := checkAppVersion(&cart, minVersion)
				if !isMobileValid {
					return isMobileValid, errCode, mobileMsg
				}
			}

			if p == enum.PlatformType(cart.SourceDetail.Platform) {
				return true, "", ""
			}
		}
	}

	return isValid, errCodeDefault, msg
}

// output: count of reward, is valid, error code, message, point, suggest info, hide display, map sku message
func isValidCondition(in result) (int, bool, enum.VoucherErrorCodeType, string, int, suggestInfo, bool, map[string]string) {
	customerCondition := &customerCondition{}
	productCondition := &productCondition{}
	orderCondition := &orderCondition{}
	productTagCondition := &productTagCondition{}
	productBlackListCondition := &productBlackListCondition{}
	paymentCondition := &paymentCondition{}

	productBlackListCondition.setNext(paymentCondition)
	orderCondition.setNext(productBlackListCondition)
	productTagCondition.setNext(orderCondition)
	productCondition.setNext(productTagCondition)
	customerCondition.setNext(productCondition)
	customerCondition.execute(&in)
	return in.rewardCount, in.isValid, in.errorCode, in.message, in.point, in.suggestInfo, in.hideDisplay, in.mapSkuMessage
}

func isValidVoucherBase(voucher *model.Voucher, used *model.UserPromotion) (bool, enum.VoucherErrorCodeType, string) {
	now := time.Now()
	if voucher.Status != nil && *voucher.Status != enum.VoucherStatus.ACTIVE {
		return false, enum.VoucherErrorCode.VOUCHER_NOT_ACTIVE, "Mã giảm giá này hiện không hoạt động"
	}
	if voucher.StartTime != nil && voucher.StartTime.After(now) {
		return false, enum.VoucherErrorCode.VOUCHER_NOT_STARTED, "Mã giảm giá chưa đến thời gian sử dụng"
	}
	if voucher.EndTime != nil && voucher.EndTime.Before(now) {
		return false, enum.VoucherErrorCode.VOUCHER_EXPIRED, "Mã giảm giá đã hết hạn sử dụng"
	}

	if voucher.CustomerApplyType == enum.CustomerApplyType.MANY && (used == nil || *used.Status == enum.CodeStatus.DELETED) {
		return false, enum.VoucherErrorCode.VOUCHER_INVALID_USER, "Bạn không đủ điều kiện sử dụng mã"
	}
	if voucher.UsageTotal != nil && voucher.MaxUsage != nil && *voucher.MaxUsage != 0 && *voucher.UsageTotal >= *voucher.MaxUsage {
		return false, enum.VoucherErrorCode.VOUCHER_OUT_OF_STOCK, "Mã giảm giá đã hết lượt sử dụng"
	}

	if used != nil && *used.Status == enum.CodeStatus.INACTIVE {
		return false, enum.VoucherErrorCode.VOUCHER_INVALID_USER, "Bạn không đủ điều kiện sử dụng mã"
	}

	if used != nil && voucher.MaxUsagePerCustomer != nil && *voucher.MaxUsagePerCustomer != 0 && used.Amount != nil && *used.Amount >= *voucher.MaxUsagePerCustomer {
		return false, enum.VoucherErrorCode.VOUCHER_USED, "Mã giảm giá đã hết lượt sử dụng"
	}
	return true, "", ""
}

func isValidVoucher(voucherCode string, voucher *model.Voucher, used *model.UserPromotion, cart *model.Cart, customer *model.Customer, setting *model.Setting, customerExperience *model.CustomerExperience) errRes {
	rewardCount, _, errCode, message, point, suggestInfo, hideDisplay, skuMessage := validateVoucher(voucher, used, cart, customer, setting, customerExperience)
	discount, gifts, sellerCode, isAvailableGift, rewardCount := getReward(voucher, used, cart, rewardCount)
	if rewardCount == 0 {
		rewardCount = 1
	}

	if discount > 0 {
		setDiscountOnEachSku(discount, voucher, cart)
		suggestInfo.suggestDiscount = discount
	} else if len(gifts) == 0 {
		// TODO: If cart already have the errCode and message, skip this work.
		if message == "" {
			errCode, message = enum.VoucherErrorCode.INVALID_APPLY_DISCOUNT, "Mua thêm sản phẩm theo điều kiện"
		}
	}

	if message == "" && !isAvailableGift {
		message = "Số lượng quà tặng đã hết"
	}

	products := make([]model.ProductConditionField, 0)
	if len(gifts) > 0 {
		products = getProductConditions(voucher)
	}

	return errRes{
		ErrorMessage: message,
		ErrorCode:    errCode,
		VoucherCode:  voucherCode,
		Discount:     discount,
		Gifts:        gifts,
		GroupCode: func() string {
			if voucher != nil && voucher.VoucherGroupCode != nil {
				return *voucher.VoucherGroupCode
			}
			return ""
		}(),
		AutoApply:     voucher != nil && voucher.ApplyType != "" && voucher.ApplyType == enum.ApplyType.AUTO,
		MatchSeller:   sellerCode,
		MatchProducts: products,
		ApplySkus: func() []string {
			if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.Skus != nil {
				return *voucher.ApplyDiscount.Skus
			}
			return []string{}
		}(),
		NotApplySkus: func() []string {
			if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.NotInSkus != nil {
				return *voucher.ApplyDiscount.NotInSkus
			}
			return []string{}
		}(),
		Priority:      voucher.Priority,
		DiscountInfos: voucher.DiscountInfos,
		SellerCode:    getVoucherSellerCode(voucher),
		SellerCodes: func() []string {
			if voucher.SellerCodes != nil && len(*voucher.SellerCodes) > 0 {
				return *voucher.SellerCodes
			}
			return []string{}
		}(),
		StoreCode: voucher.StoreCode,
		PaymentMethod: func() string {
			if ok, paymentMethods, _ := isExistConditionPaymentMethod(voucher); ok && len(paymentMethods) > 0 {
				return paymentMethods[0]
			}
			return ""
		}(),
		VoucherName: voucher.DisplayName,
		Point:       point,
		suggestInfo: suggestInfo,
		hideDisplay: hideDisplay,
		NumberOfAutoApply: func(rewardCount int) int {
			if voucher.MaxAutoApplyCount != nil && *voucher.MaxAutoApplyCount < int64(rewardCount) {
				rewardCount = int(*voucher.MaxAutoApplyCount)
			}
			return rewardCount
		}(rewardCount),
		ChargeFee: func() string {
			if voucher.ChargeFee != nil {
				return string(*voucher.ChargeFee)
			}
			return ""
		}(),
		mapSkuMessage: skuMessage,
	}
}

func getVoucherSellerCode(voucher *model.Voucher) string {
	if voucher.PromotionOrganizer != nil && *voucher.PromotionOrganizer == enum.PromotionOrganizer.INTERNAL_SELLER {
		return "INTERNAL_SELLER"
	}
	if voucher.SellerCodes != nil && len(*voucher.SellerCodes) > 0 {
		sellerCode := (*voucher.SellerCodes)[0]
		return sellerCode
	}
	if voucher.SellerCode != nil {
		return *voucher.SellerCode
	}
	return ""
}

func isValidNumberVoucher(voucher *model.Voucher, setting *model.Setting, cart *model.Cart) (bool, enum.VoucherErrorCodeType, string) {
	mapNumberVoucher := make(map[string]int)  // key = SELLER_CODE _ APPLY_TYPE
	mapVoucherInCart := make(map[string]bool) // key = voucherCode
	if cart.NumberOfVoucherMap != nil {
		mapNumberVoucher = cart.NumberOfVoucherMap
		mapVoucherInCart = cart.VoucherInCartMap
	} else {
		for _, item := range cart.RedeemApplyResult {
			//if item.AutoApply {
			//	continue
			//}
			if item.StoreCode != "" {
				mapNumberVoucher[item.StoreCode] += 1
			} else {
				mapNumberVoucher[item.SellerCode] += 1
			}
			mapVoucherInCart[item.Code] = true
		}
		cart.NumberOfVoucherMap = mapNumberVoucher
		cart.VoucherInCartMap = mapVoucherInCart
	}
	sellerCode := getVoucherSellerCode(voucher)
	if !isNilOrDefaultValue(voucher.StoreCode) {
		sellerCode = *voucher.StoreCode
	}
	if n, ok := mapNumberVoucher[sellerCode]; ok {
		numberToCheck := setting.NumberOfVoucherManual
		if sellerCode != "" {
			numberToCheck = setting.NumberOfVoucherManualPerSeller
		}
		if _, ok := mapVoucherInCart[voucher.Code]; ok {
			n--
		}
		if n >= numberToCheck {
			messageError := "Chỉ áp dụng được tối đa " + strconv.Itoa(numberToCheck) + " mã giảm giá từ thuocsi"
			if !isNilOrDefaultValue(voucher.StoreCode) && !isNilOrDefaultValue(voucher.StoreName) {
				messageError = "Chỉ áp dụng được tối đa " + strconv.Itoa(numberToCheck) + " mã giảm giá từ " + *voucher.StoreName
			} else {
				if sellerCode != "" && voucher.SellerName != nil {
					messageError = "Chỉ áp dụng được tối đa " + strconv.Itoa(numberToCheck) + " mã giảm giá từ " + *voucher.SellerName
				}
			}
			return false, enum.VoucherErrorCode.INVALID_NUMBER_APPLY, messageError
		}
	}
	return true, "", ""
}

func validateVoucher(voucher *model.Voucher, used *model.UserPromotion, cart *model.Cart, customer *model.Customer, setting *model.Setting, customerExperience *model.CustomerExperience) (int, bool, enum.VoucherErrorCodeType, string, int, suggestInfo, bool, map[string]string) {
	if voucher == nil {
		return 0, false, enum.VoucherErrorCode.VOUCHER_NOT_FOUND, "Mã giảm giá này hiện không hoạt động", 0, suggestInfo{}, false, nil
	}

	//if used == nil {
	//	qUsedVoucher := model.UserPromotionDB.QueryOne(&model.UserPromotion{VoucherCode: voucher.Code, CustomerID: customer.CustomerID})
	//	if qUsedVoucher.Status == common.APIStatus.Ok {
	//		used = qUsedVoucher.Data.([]*model.UserPromotion)[0]
	//	}
	//}
	if isValid, errorCode, message := isValidVoucherBase(voucher, used); !isValid && !cart.ValidateOrder {
		return 0, isValid, errorCode, message, 0, suggestInfo{}, false, nil
	}
	if cart.CustomerLevel == nil {
		cart.CustomerLevel = &customer.Level
	}
	if cart.CustomerScope == "" {
		cart.CustomerScope = customer.Scope
	}
	if isValid, errorCode, message := isValidVoucherScope(*cart, voucher.Scopes); !isValid && !cart.ValidateOrder {
		return 0, isValid, errorCode, message, 0, suggestInfo{}, false, nil
	}

	if setting != nil {
		if isValid, errorCode, message := isValidNumberVoucher(voucher, setting, cart); !isValid && !cart.ValidateOrder {
			return 0, isValid, errorCode, message, 0, suggestInfo{}, false, nil
		}
	}
	// valid condition
	rewardCountRes := 0
	suggestInfoRes := suggestInfo{
		isValidVoucherBase:   true,
		isValidVoucherScope:  true,
		isValidNumberVoucher: true,
	}
	if setting != nil && setting.ApplyDiscountVoucher != nil {
		isMatchDiscountType := false
		isMatchOrg := false
		settingApplyDiscount := setting.ApplyDiscountVoucher
		rewardType := ""
		org := string(enum.PromotionOrganizer.MARKETING)
		if voucher.PromotionOrganizer != nil {
			org = string(*voucher.PromotionOrganizer)
		}
		if len(voucher.Rewards) > 0 {
			if voucher.Rewards[0].Type != nil {
				rewardType = string(*voucher.Rewards[0].Type)
			}
		}
		if settingApplyDiscount.ApplyDiscountType == nil || settingApplyDiscount.ApplyDiscountType["ALL"] || settingApplyDiscount.ApplyDiscountType[rewardType] {
			isMatchDiscountType = true
		}
		if settingApplyDiscount.ApplyOrganization == nil || settingApplyDiscount.ApplyOrganization["ALL"] || settingApplyDiscount.ApplyOrganization[org] {
			isMatchOrg = true
		}

		if isMatchDiscountType && isMatchOrg {
			// mix apply discount not in skus btw voucher and setting
			if voucher.ApplyDiscount == nil {
				voucher.ApplyDiscount = setting.ApplyDiscountVoucher
			} else {
				if voucher.ApplyDiscount.Skus == nil {
					voucher.ApplyDiscount.Skus = setting.ApplyDiscountVoucher.Skus
				}
				if voucher.ApplyDiscount.Skus != nil {
					if setting.ApplyDiscountVoucher.Skus != nil {
						// check exist sku in setting but not in voucher
						newSkus := *voucher.ApplyDiscount.Skus
						for _, sku := range *setting.ApplyDiscountVoucher.Skus {
							if !utils.IsContainsString(*voucher.ApplyDiscount.Skus, sku) {
								newSkus = append(newSkus, sku)
							}
						}
						voucher.ApplyDiscount.Skus = &newSkus
					}
				}
				if voucher.ApplyDiscount.NotInSkus == nil {
					voucher.ApplyDiscount.NotInSkus = setting.ApplyDiscountVoucher.NotInSkus
				}
				if voucher.ApplyDiscount.NotInSkus != nil {
					if setting.ApplyDiscountVoucher.NotInSkus != nil {
						// check exist sku in setting but not in voucher
						newNotInSkus := *voucher.ApplyDiscount.NotInSkus
						for _, sku := range *setting.ApplyDiscountVoucher.NotInSkus {
							if !utils.IsContainsString(*voucher.ApplyDiscount.NotInSkus, sku) {
								newNotInSkus = append(newNotInSkus, sku)
							}
						}
						voucher.ApplyDiscount.NotInSkus = &newNotInSkus
					}
				}
			}
		}
		if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.SkuName == nil {
			voucher.ApplyDiscount.SkuName = setting.ApplyDiscountVoucher.SkuName
		}
	}
	if voucher.AndConditions != nil {
		rewardCount, isValid, errCode, message, point, suggestInfo, hideDisplay, mapSkuMessage := isValidCondition(result{
			cart:           cart,
			promoCondition: voucher.AndConditions,
			customer:       customer,
			operator:       "AND",
			applyDiscount:  voucher.ApplyDiscount,
			organization: func() enum.PromotionOrganizerValue {
				if voucher.PromotionOrganizer != nil {
					return *voucher.PromotionOrganizer
				}
				return enum.PromotionOrganizer.MARKETING
			}(),
			sellerCodes: func() []string {
				if voucher.SellerCodes != nil {
					return *voucher.SellerCodes
				}
				return []string{}
			}(),
			storeCode: voucher.StoreCode,
			sellerName: func() string {
				if !isNilOrDefaultValue(voucher.StoreName) {
					return *voucher.StoreName
				}
				if !isNilOrDefaultValue(voucher.SellerName) {
					return *voucher.SellerName
				}
				return ""
			}(),
			customerExperience: customerExperience,
		})
		rewardCountRes = rewardCount
		suggestInfoRes = suggestInfo
		suggestInfoRes.isValidNumberVoucher = true
		suggestInfoRes.isValidVoucherBase = true
		suggestInfoRes.isValidVoucherScope = true
		if !isValid {
			return rewardCount, isValid, errCode, message, point, suggestInfoRes, hideDisplay, mapSkuMessage
		}
	}

	if voucher.OrConditions != nil {
		rewardCount, isValid, errCode, message, point, suggestInfo, hideDisplay, mapSkuMessage := isValidCondition(result{
			cart:           cart,
			promoCondition: voucher.OrConditions,
			customer:       customer,
			operator:       "OR",
			applyDiscount:  voucher.ApplyDiscount,
			organization: func() enum.PromotionOrganizerValue {
				if voucher.PromotionOrganizer != nil {
					return *voucher.PromotionOrganizer
				}
				return enum.PromotionOrganizer.MARKETING
			}(),
			sellerCodes: func() []string {
				if voucher.SellerCodes != nil {
					return *voucher.SellerCodes
				}
				return []string{}
			}(),
			storeCode: voucher.StoreCode,
			sellerName: func() string {
				if !isNilOrDefaultValue(voucher.StoreName) {
					return *voucher.StoreName
				}
				if !isNilOrDefaultValue(voucher.SellerName) {
					return *voucher.SellerName
				}
				return ""
			}(),
			customerExperience: customerExperience,
		})
		rewardCountRes = rewardCount
		suggestInfoRes = suggestInfo
		suggestInfoRes.isValidNumberVoucher = true
		suggestInfoRes.isValidVoucherBase = true
		suggestInfoRes.isValidVoucherScope = true
		if !isValid {
			return rewardCount, isValid, errCode, message, point, suggestInfoRes, hideDisplay, mapSkuMessage
		}
	}

	return rewardCountRes, true, "", "", 0, suggestInfoRes, false, nil
}

func getReward(voucher *model.Voucher, used *model.UserPromotion, cart *model.Cart, rewardCount int) (int, []model.Gift, string, bool, int) {
	if rewardCount == 0 {
		rewardCount = 1
	}
	if voucher.MaxAutoApplyCount == nil {
		voucher.MaxAutoApplyCount = utils.ParseInt64ToPointer(1)
	}
	if voucher.MaxAutoApplyCount != nil && *voucher.MaxAutoApplyCount < int64(rewardCount) {
		rewardCount = int(*voucher.MaxAutoApplyCount)
	}
	priceToCal := cart.Price
	if voucher.PromotionOrganizer != nil && (*voucher.PromotionOrganizer == enum.PromotionOrganizer.INTERNAL_SELLER || *voucher.PromotionOrganizer == enum.PromotionOrganizer.SELLER_CENTER) {
		priceToCal = 0
		if !isNilOrDefaultValue(voucher.StoreCode) {
			if cart.StoreValue == nil {
				makeMapItem(cart, cart.CartItems)
			}
			if data, ok := cart.StoreValue[*voucher.StoreCode]; ok {
				priceToCal = data.Total
			}
		} else {
			if cart.SellerValue == nil {
				makeMapItem(cart, cart.CartItems)
			}
			if voucher.SellerCodes != nil && len(*voucher.SellerCodes) != 0 {
				for _, seller := range *voucher.SellerCodes {
					if data, ok := cart.SellerValue[seller]; ok {
						priceToCal += data.Total
					}
				}
			}
		}
	}
	if voucher.ApplyDiscount != nil {
		skuMap := make(map[string]bool)
		//notInSkuMap := make(map[string]bool)
		if voucher.ApplyDiscount.Skus != nil && len(*voucher.ApplyDiscount.Skus) != 0 {
			for _, sku := range *voucher.ApplyDiscount.Skus {
				skuMap[sku] = true
			}
		}
		//if voucher.ApplyDiscount.NotInSkus != nil && len(*voucher.ApplyDiscount.NotInSkus) != 0 {
		//	for _, sku := range *voucher.ApplyDiscount.NotInSkus {
		//		notInSkuMap[sku] = true
		//	}
		//}
		if len(skuMap) != 0 {
			priceToCal = 0
			for _, item := range cart.CartItems {
				if _, ok := skuMap[item.ProductSKU]; ok {
					priceToCal += item.Total
				} else {
					if voucher.PromotionOrganizer != nil && voucher.SellerCodes != nil && (*voucher.PromotionOrganizer == enum.PromotionOrganizer.INTERNAL_SELLER || *voucher.PromotionOrganizer == enum.PromotionOrganizer.SELLER_CENTER) {
						isMatchSeller := false
						for _, seller := range *voucher.SellerCodes {
							if item.SellerCode == seller {
								isMatchSeller = true
								break
							}
						}
						if !isNilOrDefaultValue(voucher.StoreCode) && item.StoreCode != *voucher.StoreCode {
							isMatchSeller = false
						}
						if isMatchSeller {
							msg := "Mã " + voucher.Code + " không áp dụng cho sản phẩm này"
							//if voucher.ApplyType == enum.ApplyType.AUTO {
							//	msg = "Mã giảm giá không áp dụng cho sản phẩm này"
							//}
							voucher.DiscountInfos = append(voucher.DiscountInfos, model.DiscountInfo{
								Sku:       item.ProductSKU,
								IsApply:   false,
								Message:   msg,
								StoreCode: voucher.StoreCode,
								SellerCodes: func() []string {
									if voucher.SellerCodes != nil {
										return *voucher.SellerCodes
									}
									return []string{}
								}(),
							})
						}
					}
				}
			}
		}

		if voucher.ApplyDiscount.NotInSkus != nil && len(*voucher.ApplyDiscount.NotInSkus) != 0 && (voucher.ApplyDiscount.Skus == nil || len(*voucher.ApplyDiscount.Skus) == 0) {
			if cart.SkuValue == nil {
				makeMapItem(cart, cart.CartItems)
			}
			for _, sku := range *voucher.ApplyDiscount.NotInSkus {
				if _, ok := cart.SkuValue[sku]; !ok {
					continue
				}
				priceToCal -= cart.SkuValue[sku].Total
				msg := "Mã " + voucher.Code + " không áp dụng cho sản phẩm này"
				//if voucher.ApplyType == enum.ApplyType.AUTO {
				//	msg = "Mã giảm giá không áp dụng cho sản phẩm này"
				//}
				voucher.DiscountInfos = append(voucher.DiscountInfos, model.DiscountInfo{
					Sku:       sku,
					IsApply:   false,
					Message:   msg,
					StoreCode: voucher.StoreCode,
					SellerCodes: func() []string {
						if voucher.SellerCodes != nil {
							return *voucher.SellerCodes
						}
						return []string{}
					}(),
				})
			}
		}
	}
	voucher.PriceToCalDiscount = priceToCal
	if voucher != nil && len(voucher.Rewards) > 0 {
		reward := voucher.Rewards[0]
		if reward.Type == nil {
			return 0, nil, "", true, rewardCount
		}
		switch *reward.Type {
		case enum.RewardType.ABSOLUTE:
			if voucher.ApplyType == enum.ApplyType.AUTO && rewardCount > 1 {
				reward.AbsoluteDiscount = reward.AbsoluteDiscount * int64(rewardCount)
			}
			if reward.AbsoluteDiscount > priceToCal && priceToCal != 0 {
				return utils.RoundPrice(int(priceToCal)), nil, "", true, rewardCount
			}
			if cart.IsCartEmpty {
				return int(reward.AbsoluteDiscount), nil, "", true, rewardCount
			}
			if priceToCal == 0 {
				return 0, nil, "", true, rewardCount
			}
			return utils.RoundPrice(int(reward.AbsoluteDiscount)), nil, "", true, rewardCount
		case enum.RewardType.GIFT:
			isAvailableGift := true
			sellerCode := ""
			if len(reward.Gifts) > 0 {
				gift := reward.Gifts[0]
				tmp := strings.Split(gift.Sku, ".")
				sellerCode = tmp[0]
				if sellerCode == "MARKETING" {
					sellerCode = ""
				}
			}
			if voucher.ApplyType == enum.ApplyType.AUTO && rewardCount > 1 {
				newGifts := make([]model.Gift, 0)
				for _, gift := range reward.Gifts {
					v := model.Gift{
						Sku:              gift.Sku,
						Quantity:         gift.Quantity * int64(rewardCount),
						QuantityPerApply: gift.Quantity,
						MaxQuantityApply: gift.MaxQuantityApply,
					}
					newGifts = append(newGifts, v)
				}
				reward.Gifts = newGifts
			} else {
				for i := range reward.Gifts {
					reward.Gifts[i].QuantityPerApply = reward.Gifts[i].Quantity
				}
			}
			if len(reward.Gifts) > 0 {
				// check max gift quantity
				newGifts := make([]model.Gift, 0)
				for _, gift := range reward.Gifts {
					rewardedGift := 0
					if used != nil && used.GiftCount != nil {
						rewardedGift = used.GiftCount[gift.Sku]
					}
					if gift.MaxQuantityApply > 0 && gift.QuantityPerApply*int64(rewardCount)+int64(rewardedGift) >= gift.MaxQuantityApply {
						// decrease rewardCount and set gift quantity
						rewardCount = int((gift.MaxQuantityApply - int64(rewardedGift)) / gift.QuantityPerApply)
						if rewardCount <= 0 {
							isAvailableGift = false
						}
					}
				}
				for _, gift := range reward.Gifts {
					gift.Quantity = gift.QuantityPerApply * int64(rewardCount)
					newGifts = append(newGifts, gift)
				}
				reward.Gifts = newGifts
			}
			return 0, reward.Gifts, sellerCode, isAvailableGift, rewardCount
		case enum.RewardType.PERCENTAGE:
			if voucher.ApplyType == enum.ApplyType.AUTO && rewardCount > 1 {
				reward.PercentageDiscount = reward.PercentageDiscount * float64(rewardCount)
			}
			if cart.IsCartEmpty {
				return int(reward.MaxDiscount), nil, "", true, rewardCount
			}
			if priceToCal == 0 {
				return 0, nil, "", true, rewardCount
			}
			discount := int64((float64(priceToCal) * reward.PercentageDiscount) / 100)
			if discount > reward.MaxDiscount {
				discount = reward.MaxDiscount
			}
			return utils.RoundPrice(int(discount)), nil, "", true, rewardCount
		}
	}
	return 0, nil, "", true, rewardCount
}

func setDiscountOnEachSku(discount int, voucher *model.Voucher, cart *model.Cart) {
	totalDiscount := 0
	mapNotApplySku := make(map[string]bool)
	if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.NotInSkus != nil && len(*voucher.ApplyDiscount.NotInSkus) != 0 {
		for _, sku := range *voucher.ApplyDiscount.NotInSkus {
			mapNotApplySku[sku] = true
		}
	}
	if voucher.ApplyDiscount != nil && voucher.ApplyDiscount.Skus != nil && len(*voucher.ApplyDiscount.Skus) != 0 {
		if cart.SkuValue == nil {
			makeMapItem(cart, cart.CartItems)
		}
		skuMap := make(map[string]bool)
		for _, sku := range *voucher.ApplyDiscount.Skus {
			skuMap[sku] = true
		}

		for _, item := range cart.CartItems {
			if _, ok := skuMap[item.ProductSKU]; ok {
				discountInfo := model.DiscountInfo{
					Sku:       item.ProductSKU,
					IsApply:   true,
					Discount:  utils.RoundDownPrice(int(float64(item.Total) / float64(voucher.PriceToCalDiscount) * float64(discount))),
					StoreCode: voucher.StoreCode,
					SellerCodes: func() []string {
						if voucher.SellerCodes != nil {
							return *voucher.SellerCodes
						}
						return []string{}
					}(),
				}
				voucher.DiscountInfos = append(voucher.DiscountInfos, discountInfo)
				totalDiscount += discountInfo.Discount
			}
		}

	} else if voucher.PromotionOrganizer != nil && (*voucher.PromotionOrganizer == enum.PromotionOrganizer.INTERNAL_SELLER || *voucher.PromotionOrganizer == enum.PromotionOrganizer.SELLER_CENTER) {
		if !isNilOrDefaultValue(voucher.StoreCode) {
			for _, item := range cart.CartItems {
				if item.StoreCode != *voucher.StoreCode {
					continue
				}
				if _, ok := mapNotApplySku[item.ProductSKU]; ok {
					continue
				}
				discountInfo := model.DiscountInfo{
					Sku:       item.ProductSKU,
					IsApply:   true,
					Discount:  utils.RoundDownPrice(int(float64(item.Total) / float64(voucher.PriceToCalDiscount) * float64(discount))),
					StoreCode: voucher.StoreCode,
					SellerCodes: func() []string {
						if voucher.SellerCodes != nil {
							return *voucher.SellerCodes
						}
						return []string{}
					}(),
				}
				voucher.DiscountInfos = append(voucher.DiscountInfos, discountInfo)
				totalDiscount += discountInfo.Discount
			}
		} else {
			if voucher.SellerCodes != nil && len(*voucher.SellerCodes) != 0 {
				for _, seller := range *voucher.SellerCodes {
					for _, item := range cart.CartItems {
						if _, ok := mapNotApplySku[item.ProductSKU]; ok {
							continue
						}
						if item.SellerCode == seller {
							discountInfo := model.DiscountInfo{
								Sku:       item.ProductSKU,
								IsApply:   true,
								Discount:  utils.RoundDownPrice(int(float64(item.Total) / float64(voucher.PriceToCalDiscount) * float64(discount))),
								StoreCode: voucher.StoreCode,
								SellerCodes: func() []string {
									if voucher.SellerCodes != nil {
										return *voucher.SellerCodes
									}
									return []string{}
								}(),
							}
							voucher.DiscountInfos = append(voucher.DiscountInfos, discountInfo)
							totalDiscount += discountInfo.Discount
						}
					}
				}
			}
		}

	} else {
		for _, item := range cart.CartItems {
			if _, ok := mapNotApplySku[item.ProductSKU]; ok {
				continue
			}
			discountInfo := model.DiscountInfo{
				Sku:       item.ProductSKU,
				IsApply:   true,
				Discount:  utils.RoundDownPrice(int(float64(item.Total) / float64(voucher.PriceToCalDiscount) * float64(discount))),
				StoreCode: voucher.StoreCode,
				SellerCodes: func() []string {
					if voucher.SellerCodes != nil {
						return *voucher.SellerCodes
					}
					return []string{}
				}(),
			}
			voucher.DiscountInfos = append(voucher.DiscountInfos, discountInfo)
			totalDiscount += discountInfo.Discount
		}
	}
	if len(voucher.DiscountInfos) > 0 {
		//if totalDiscount != discount {
		lastItemDiscount := voucher.DiscountInfos[len(voucher.DiscountInfos)-1].Discount
		voucher.DiscountInfos[len(voucher.DiscountInfos)-1].Discount = lastItemDiscount + (discount - totalDiscount)
		//}
	}
}

func getProductConditions(voucher *model.Voucher) []model.ProductConditionField {
	products := make([]model.ProductConditionField, 0)
	if len(voucher.AndConditions[enum.ConditionType.PRODUCT].AndConditions) > 0 {
		for _, productCondition := range voucher.AndConditions[enum.ConditionType.PRODUCT].AndConditions {
			products = append(products, productCondition.ProductConditionField)
		}
	}

	return products
}

func setViewData(voucher *model.Voucher, cart *model.Cart) *model.VoucherViewWebOnly {
	view := &model.VoucherViewWebOnly{
		DisplayName:          voucher.DisplayName,
		VoucherID:            voucher.VoucherID,
		Code:                 voucher.Code,
		StartTime:            voucher.StartTime,
		EndTime:              voucher.EndTime,
		PublicTime:           voucher.PublicTime,
		ConditionDescription: voucher.ConditionDescription,
		CanUse:               voucher.CanUse,
		ErrorMessage:         voucher.ErrorMessage,
		Discount: func() int {
			if voucher.Discount != 0 {
				return voucher.Discount
			}
			if len(voucher.Rewards) > 0 {
				reward := voucher.Rewards[0]
				if reward.Type != nil && *reward.Type == enum.RewardType.ABSOLUTE {
					return int(reward.AbsoluteDiscount)
				}
				//if reward.Type != nil && *reward.Type == enum.RewardType.PERCENTAGE {
				//	return int(reward.MaxDiscount)
				//}
			}
			return 0
		}(),
		MaxUsage:   voucher.MaxUsage,
		UsageTotal: voucher.UsageTotal,
		ApplyType:  voucher.ApplyType,
		Tag:        voucher.Tag,
		Priority:   voucher.Priority,
		ShortName: func() string {
			if voucher.ShortName != "" {
				if len(voucher.Rewards) > 0 {
					reward := voucher.Rewards[0]
					if reward.Type != nil && *reward.Type == enum.RewardType.PERCENTAGE {
						if strings.Contains(voucher.ShortName, ".00") {
							voucher.ShortName = strings.Replace(voucher.ShortName, ".00", "", -1)
						}
					}
				}
				return voucher.ShortName
			}
			if len(voucher.Rewards) > 0 {
				reward := voucher.Rewards[0]
				if reward.Type != nil && *reward.Type == enum.RewardType.ABSOLUTE {
					return "GIẢM " + utils.FormatVNDCurrency(fmt.Sprintf("%d", reward.AbsoluteDiscount)) + "đ"
				}
				if reward.Type != nil && *reward.Type == enum.RewardType.PERCENTAGE {
					textDiscountPercent := fmt.Sprintf("%.2f", reward.PercentageDiscount)
					// if textDiscountPercent is integer, remove .0
					if strings.Contains(textDiscountPercent, ".00") {
						textDiscountPercent = strings.Replace(textDiscountPercent, ".00", "", -1)
					}
					return "GIẢM " + textDiscountPercent + "%"
				}
				return "QUÀ TẶNG"
			}
			return ""
		}(),
		ActionStatus: func() string {
			if voucher.CanUse == true {
				return "AVAILABLE"
			}
			if voucher.ErrorCode == string(enum.VoucherErrorCode.INVALID_NUMBER_APPLY) {
				return "DISABLED"
			}
			return "INVALID"
		}(),
		ActionLink: func() string {
			if voucher.CanUse == true {
				return ""
			}
			if voucher.LinkToPage != nil && *voucher.LinkToPage != "" {
				return *voucher.LinkToPage
			}
			return "/quick-order"
		}(),
		VoucherImage: func() string {
			if voucher.VoucherImage != nil {
				return *voucher.VoucherImage
			}
			return ""
		}(),
		GroupCode: func() string {
			if voucher.VoucherGroupCode != nil {
				return *voucher.VoucherGroupCode
			}
			return ""
		}(),
		Gifts: func() []model.Gift {
			if len(voucher.Gifts) > 0 {
				return voucher.Gifts
			}
			if len(voucher.Rewards) > 0 && *voucher.Rewards[0].Type == enum.RewardType.GIFT {
				return voucher.Rewards[0].Gifts
			}
			return nil
		}(),
		PromotionName: func() string {
			if voucher.DisplayName != "" {
				return voucher.DisplayName
			}
			return voucher.PromotionName
		}(),
		Description: func() string {
			if voucher.DisplayName != "" {
				return voucher.DisplayName
			}
			return voucher.PromotionName
		}(),
		DiscountInfos: voucher.DiscountInfos,
		PromotionOrganizer: func() enum.PromotionOrganizerValue {
			if voucher.PromotionOrganizer != nil {
				return *voucher.PromotionOrganizer
			}
			return enum.PromotionOrganizer.MARKETING
		}(),
		SellerCode: func() string {
			if voucher.PromotionOrganizer != nil && *voucher.PromotionOrganizer == enum.PromotionOrganizer.INTERNAL_SELLER {
				if !isNilOrDefaultValue(voucher.StoreCode) {
					return *voucher.StoreCode
				}
				return "INTERNAL_SELLER"
			}
			if voucher.SellerCodes != nil && len(*voucher.SellerCodes) > 0 {
				sellerCode := (*voucher.SellerCodes)[0]
				return sellerCode
			}
			if voucher.SellerCode != nil {
				return *voucher.SellerCode
			}
			return ""
		}(),
		SellerCodes: func() []string {
			if voucher.SellerCodes != nil && len(*voucher.SellerCodes) > 0 {
				return *voucher.SellerCodes
			}
			return []string{}
		}(),
		MinOrderValue: func() int64 {
			if voucher.AndConditions != nil && len(voucher.AndConditions[enum.ConditionType.ORDER_VALUE].AndConditions) > 0 {
				minPrice := voucher.AndConditions[enum.ConditionType.ORDER_VALUE].AndConditions[0].OrderConditionField.MinTotalPrice
				if minPrice != nil && *minPrice > 0 {
					return int64(*minPrice)
				}
			}
			if voucher.AndConditions != nil && len(voucher.AndConditions[enum.ConditionType.ORDER_VALUE].OrConditions) > 0 {
				minPrice := voucher.AndConditions[enum.ConditionType.ORDER_VALUE].OrConditions[0].OrderConditionField.MinTotalPrice
				if minPrice != nil && *minPrice > 0 {
					return int64(*minPrice)
				}
			}
			return 0
		}(),
		CustomerApplyType: voucher.CustomerApplyType,
		CollectStatus: func() string {
			if voucher.CustomerApplyType == enum.CustomerApplyType.MANY {
				return "OWNERSHIP"
			}
			return "AVAILABLE"
		}(),
		IsOwner:     voucher.CustomerApplyType == enum.CustomerApplyType.MANY,
		LinkToStore: voucher.LinkToStore,
		SellerName: func() *string {
			if voucher.StoreName != nil {
				return voucher.StoreName
			}
			return voucher.SellerName
		}(),
		IsVoucherByProduct: func() bool {
			if ok, _ := setIsExistConditionTag(voucher); ok {
				return true
			}
			if ok, _, _ := setIsExistConditionProduct(voucher); ok {
				return true
			}
			return false
		}(),
		PaymentMethod: func() string {
			if ok, paymentMethods, _ := isExistConditionPaymentMethod(voucher); ok && len(paymentMethods) > 0 {
				return paymentMethods[0]
			}
			return ""
		}(),
		PaymentMethodName: func() string {
			if ok, _, names := isExistConditionPaymentMethod(voucher); ok && len(names) > 0 {
				return names[0]
			}
			return ""
		}(),
		DiscountPercent: func() float64 {
			if voucher.Rewards != nil && len(voucher.Rewards) > 0 {
				reward := voucher.Rewards[0]
				if reward.Type != nil && *reward.Type == enum.RewardType.PERCENTAGE {
					return reward.PercentageDiscount
				}
			}
			return 0
		}(),
		MaxDiscount: func() float64 {
			if voucher.Rewards != nil && len(voucher.Rewards) > 0 {
				reward := voucher.Rewards[0]
				if reward.Type != nil && *reward.Type == enum.RewardType.PERCENTAGE {
					return float64(reward.MaxDiscount)
				}
			}
			return 0
		}(),
		IsVoucherByOrder: func() bool {
			if ok := setIsOnlyConditionOrder(voucher); ok {
				return true
			}
			return false
		}(),
		SkipCheckVoucherGroup: voucher.SkipCheckVoucherGroup,
	}

	filterProduct := &model.FilterProduct{}
	productCodes, tagCodes, skuCodes, sellers, skusNotIn, tagCodesIn := getProductInfoByCondition(voucher.OrConditions, voucher.AndConditions, voucher.ApplyDiscount, voucher.SellerCodes, voucher.StoreCode)
	if len(tagCodes) > 0 {
		filterProduct.Tags = &tagCodes
	}
	if len(skuCodes) > 0 {
		filterProduct.Skus = &skuCodes
	}
	if len(sellers) > 0 {
		filterProduct.Sellers = &sellers
	} else if len(productCodes) > 0 {
		filterProduct.ProductCodes = &productCodes
	}
	if len(skusNotIn) > 0 {
		filterProduct.SkuNotIn = &skusNotIn
	}
	if len(tagCodesIn) > 0 {
		filterProduct.TagIn = &tagCodesIn
	}
	view.FilterProduct = filterProduct
	view.RefSkus = skuCodes

	if voucher.OrConditions != nil && len(voucher.OrConditions) == 1 {
		// orTags
		if len(voucher.OrConditions[enum.ConditionType.PRODUCT_TAG].OrConditions) > 0 {
			tags := make(map[string]model.QuantityAndAmount)
			for _, c := range voucher.OrConditions[enum.ConditionType.PRODUCT_TAG].OrConditions {
				tag := c.ProductTagConditionField.TagCode
				if c.ProductTagConditionField.SellerCode != nil {
					tag = fmt.Sprintf("%s_%s", *c.ProductTagConditionField.SellerCode, c.ProductTagConditionField.TagCode)
				}
				value := model.QuantityAndAmount{}
				if c.ProductTagConditionField.MinQuantity != nil {
					value.Quantity = *c.ProductTagConditionField.MinQuantity
				}
				if c.ProductTagConditionField.MinTotalPrice != nil {
					value.MinPrice = *c.ProductTagConditionField.MinTotalPrice
				}
				tags[tag] = value
			}
			view.OrTags = tags
		}
		// andTags
		if len(voucher.OrConditions[enum.ConditionType.PRODUCT_TAG].AndConditions) > 0 {
			tags := make(map[string]model.QuantityAndAmount)
			for _, c := range voucher.OrConditions[enum.ConditionType.PRODUCT_TAG].AndConditions {
				tag := c.ProductTagConditionField.TagCode
				if c.ProductTagConditionField.SellerCode != nil {
					tag = fmt.Sprintf("%s_%s", *c.ProductTagConditionField.SellerCode, c.ProductTagConditionField.TagCode)
				}
				value := model.QuantityAndAmount{}
				if c.ProductTagConditionField.MinQuantity != nil {
					value.Quantity = *c.ProductTagConditionField.MinQuantity
				}
				if c.ProductTagConditionField.MinTotalPrice != nil {
					value.MinPrice = *c.ProductTagConditionField.MinTotalPrice
				}
				tags[tag] = value
			}
			view.AndTags = tags
		}
		// orSkus
		if len(voucher.OrConditions[enum.ConditionType.PRODUCT].OrConditions) > 0 {
			skus := make(map[string]model.QuantityAndAmount)
			for _, c := range voucher.OrConditions[enum.ConditionType.PRODUCT].OrConditions {
				sku := c.ProductConditionField.ProductCode
				if c.ProductConditionField.SellerCode != nil {
					sku = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
				}
				value := model.QuantityAndAmount{}
				if c.ProductConditionField.MinQuantity != nil {
					value.Quantity = *c.ProductConditionField.MinQuantity
				}
				if c.ProductConditionField.MinTotalPrice != nil {
					value.MinPrice = *c.ProductConditionField.MinTotalPrice
				}
				skus[sku] = value
			}
			view.OrSkus = skus
		}
		// andSkus
		if len(voucher.OrConditions[enum.ConditionType.PRODUCT].AndConditions) > 0 {
			skus := make(map[string]model.QuantityAndAmount)
			for _, c := range voucher.OrConditions[enum.ConditionType.PRODUCT].AndConditions {
				sku := c.ProductConditionField.ProductCode
				if c.ProductConditionField.SellerCode != nil {
					sku = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
				}
				value := model.QuantityAndAmount{}
				if c.ProductConditionField.MinQuantity != nil {
					value.Quantity = *c.ProductConditionField.MinQuantity
				}
				if c.ProductConditionField.MinTotalPrice != nil {
					value.MinPrice = *c.ProductConditionField.MinTotalPrice
				}
				skus[sku] = value
			}
			view.AndSkus = skus
		}
	}
	if voucher.AndConditions != nil && len(voucher.AndConditions) == 1 {
		// orTags
		if len(voucher.AndConditions[enum.ConditionType.PRODUCT_TAG].OrConditions) > 0 {
			tags := make(map[string]model.QuantityAndAmount)
			for _, c := range voucher.AndConditions[enum.ConditionType.PRODUCT_TAG].OrConditions {
				tag := c.ProductTagConditionField.TagCode
				if c.ProductTagConditionField.SellerCode != nil {
					tag = fmt.Sprintf("%s_%s", *c.ProductTagConditionField.SellerCode, c.ProductTagConditionField.TagCode)
				}
				value := model.QuantityAndAmount{}
				if c.ProductTagConditionField.MinQuantity != nil {
					value.Quantity = *c.ProductTagConditionField.MinQuantity
				}
				if c.ProductTagConditionField.MinTotalPrice != nil {
					value.MinPrice = *c.ProductTagConditionField.MinTotalPrice
				}
				tags[tag] = value
			}
			view.OrTags = tags
		}
		// andTags
		if len(voucher.AndConditions[enum.ConditionType.PRODUCT_TAG].AndConditions) > 0 {
			tags := make(map[string]model.QuantityAndAmount)
			for _, c := range voucher.AndConditions[enum.ConditionType.PRODUCT_TAG].AndConditions {
				tag := c.ProductTagConditionField.TagCode
				if c.ProductTagConditionField.SellerCode != nil {
					tag = fmt.Sprintf("%s_%s", *c.ProductTagConditionField.SellerCode, c.ProductTagConditionField.TagCode)
				}
				value := model.QuantityAndAmount{}
				if c.ProductTagConditionField.MinQuantity != nil {
					value.Quantity = *c.ProductTagConditionField.MinQuantity
				}
				if c.ProductTagConditionField.MinTotalPrice != nil {
					value.MinPrice = *c.ProductTagConditionField.MinTotalPrice
				}
				tags[tag] = value
			}
			view.AndTags = tags
		}
		// orSkus
		if len(voucher.AndConditions[enum.ConditionType.PRODUCT].OrConditions) > 0 {
			skus := make(map[string]model.QuantityAndAmount)
			for _, c := range voucher.AndConditions[enum.ConditionType.PRODUCT].OrConditions {
				sku := c.ProductConditionField.ProductCode
				if c.ProductConditionField.SellerCode != nil {
					sku = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
				}
				value := model.QuantityAndAmount{}
				if c.ProductConditionField.MinQuantity != nil {
					value.Quantity = *c.ProductConditionField.MinQuantity
				}
				if c.ProductConditionField.MinTotalPrice != nil {
					value.MinPrice = *c.ProductConditionField.MinTotalPrice
				}
				skus[sku] = value
			}
			view.OrSkus = skus
		}
		// andSkus
		if len(voucher.AndConditions[enum.ConditionType.PRODUCT].AndConditions) > 0 {
			skus := make(map[string]model.QuantityAndAmount)
			for _, c := range voucher.AndConditions[enum.ConditionType.PRODUCT].AndConditions {
				sku := c.ProductConditionField.ProductCode
				if c.ProductConditionField.SellerCode != nil {
					sku = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
				}
				value := model.QuantityAndAmount{}
				if c.ProductConditionField.MinQuantity != nil {
					value.Quantity = *c.ProductConditionField.MinQuantity
				}
				if c.ProductConditionField.MinTotalPrice != nil {
					value.MinPrice = *c.ProductConditionField.MinTotalPrice
				}
				skus[sku] = value
			}
			view.AndSkus = skus
		}
	}

	// if voucher is percentage discount, re calculate discount: discount = % of minPrice, if minPrice is not exist, discount = maxDiscount, get min value of discount and maxDiscount
	if voucher.Rewards != nil && len(voucher.Rewards) > 0 && (voucher.Discount == 0 || !view.CanUse) {
		reward := voucher.Rewards[0]
		if reward.Type != nil && *reward.Type == enum.RewardType.PERCENTAGE {
			discount := int64(0)
			if view.MinOrderValue != 0 {
				discount = int64((float64(view.MinOrderValue) * reward.PercentageDiscount) / 100)
				if discount > reward.MaxDiscount {
					discount = reward.MaxDiscount
				}
			} else {
				discount = reward.MaxDiscount
			}
			view.Discount = int(discount)
		}
	}

	return view
}

func isValidVoucherCondition(voucher *model.Voucher, cart *model.Cart, customer *model.Customer, customerExperience *model.CustomerExperience) (ConditionMessages, *float64) {
	conditionMessages, percentageCondition := isValidMessageCondition(voucherConditionResult{
		cart:          cart,
		orCondition:   voucher.OrConditions,
		andConditions: voucher.AndConditions,
		customer:      customer,
		applyDiscount: voucher.ApplyDiscount,
		organization: func() enum.PromotionOrganizerValue {
			if voucher.PromotionOrganizer != nil {
				return *voucher.PromotionOrganizer
			}
			return enum.PromotionOrganizer.MARKETING
		}(),
		sellerCodes: func() []string {
			if voucher.SellerCodes != nil {
				return *voucher.SellerCodes
			}
			return []string{}
		}(),
		storeCode: voucher.StoreCode,
		sellerName: func() string {
			if !isNilOrDefaultValue(voucher.StoreName) {
				return *voucher.StoreName
			}
			if !isNilOrDefaultValue(voucher.SellerName) {
				return *voucher.SellerName
			}
			return ""
		}(),
		customerExperience: customerExperience,
	})

	return conditionMessages, percentageCondition
}

func VoucherConditionMessageList(
	accountID int64,
	query *model.Voucher,
	cart *model.Cart,
	codes []string,
) *common.APIResponse {
	if query.SystemDisplay == "" {
		query.SystemDisplay = "BUYMED"
	} else if query.SystemDisplay == "ALL" {
		query.SystemDisplay = ""
	}

	var (
		customer           *model.Customer
		customerExperience *model.CustomerExperience
		errGetCustomer, _  error

		wg sync.WaitGroup
	)

	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		customer, errGetCustomer = client.Services.Customer.GetCustomerByAccountID(accountID)
	})

	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		customerExperience, _ = client.Services.DataHarvest.GetCustomerExperience(0, accountID)
	})

	wg.Wait()

	if errGetCustomer != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   errGetCustomer.Error(),
			ErrorCode: "GET_CUSTOMER_ERROR",
		}
	}

	if cart != nil {
		cart.CustomerLevel = utils.ParseStringToPointer(customer.Level)
		if cart.ProvinceCode == "" {
			cart.ProvinceCode = customer.ProvinceCode
		}
		if cart.CustomerScope == "" {
			cart.CustomerScope = customer.Scope
		}
		if cart.RedeemCode == nil {
			cart.RedeemCode = make([]string, 0)
		}
	}

	if len(codes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"code": bson.M{"$in": codes},
		})
	}

	mapVoucher := make(map[string]*model.Voucher)
	qVoucher := model.VoucherCacheReadDB.Query(query, 0, 0, &primitive.M{"end_time": 1})
	if qVoucher.Status == common.APIStatus.Ok {
		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			// map productName and tagName to condition
			productCodes, tagCodes, _, _, _, tagCodesIn := getProductInfoByCondition(voucher.OrConditions, voucher.AndConditions, voucher.ApplyDiscount, voucher.SellerCodes, voucher.StoreCode)
			productMap := make(map[string]*model.Product)
			tagMap := make(map[string]*model.Tag)
			if len(productCodes) > 0 || len(tagCodes) > 0 || len(tagCodesIn) > 0 {
				products, _ := client.Services.Product.GetProducts(productCodes)
				for _, product := range products {
					productMap[product.Code] = product
				}

				combineTagCodes := append(tagCodes, tagCodesIn...) // combine tagCode & tagCodeIn
				tags, _ := client.Services.Product.GetTags(combineTagCodes)
				for _, tag := range tags {
					tagMap[tag.Code] = tag
				}
				// set product name in conditions.
				setDataNameToCondition(voucher.OrConditions, voucher.AndConditions, productMap, tagMap)
			}
			mapVoucher[voucher.Code] = voucher
		}
	} else {
		return qVoucher
	}

	conditionMessages := make(ConditionMessages, 0)
	percentageConditionValid := 0.0
	finalResult := make([]model.ConditionMessageResponse, 0)

	if len(mapVoucher) > 0 {
		for _, voucher := range mapVoucher {
			validatedConditionMessages, percentageConditionMessages := isValidVoucherCondition(voucher, cart, customer, customerExperience)
			if validatedConditionMessages != nil && percentageConditionMessages != nil {
				conditionMessages = append(conditionMessages, validatedConditionMessages...)
				percentageConditionValid = *percentageConditionMessages
			}
		}
		finalResult = append(finalResult, model.ConditionMessageResponse{
			ConditionMessages:      conditionMessages,
			PercentageAllCondition: &percentageConditionValid,
		})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    finalResult,
		Message: "Success",
	}
}
