package enum

type CheckinItemRewardType string

type checkinItemReward struct {
	VOUCHER        CheckinItemRewardType
	POINTS         CheckinItemRewardType
	TURNS          CheckinItemRewardType
	TICKET_PATTERN CheckinItemRewardType
	OTHER          CheckinItemRewardType
}

var CheckinItemReward = &checkinItemReward{
	VOUCHER:        "VOUCHER",
	POINTS:         "POINTS",
	TURNS:          "TURNS",
	TICKET_PATTERN: "TICKET_PATTERN",
	OTHER:          "OTHER",
}
