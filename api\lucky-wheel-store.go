package api

import (
	"encoding/json"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

// CreateLKStore is func create lucky wheel store
func CreateLKStore(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload model.LuckyWheelStore
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}
	return resp.Respond(action.CreateLKStore(getActionSource(req), &payload))
}

// UpdateLKStore is func update lucky wheel store
func UpdateLKStore(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload model.LuckyWheelStore
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}
	return resp.Respond(action.UpdateLKStore(getActionSource(req), &payload))
}

// CreateLKStoreItem is func create lucky wheel store item
func CreateLKStoreItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload model.LuckyWheelStoreItem
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}
	return resp.Respond(action.CreateLKStoreItem(getActionSource(req), &payload))
}

// UpdateLKStoreItem is func update lucky wheel store item
func UpdateLKStoreItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload model.LuckyWheelStoreItem
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}
	return resp.Respond(action.UpdateLKStoreItem(getActionSource(req), &payload))
}

// GetLKStore is func get lucky wheel store
func GetLKStore(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")
	query := model.LuckyWheelStore{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}
	return resp.Respond(action.GetLKStore(&query))
}

// GetLKStoreList is func get lucky wheel store list
func GetLKStoreList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	query := model.LuckyWheelStore{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}
	return resp.Respond(action.GetLKStoreList(&query, offset, limit, getTotal))
}

// GetLKStoreItemList is func get lucky wheel store item list
func GetLKStoreItemList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	query := model.LuckyWheelStoreItem{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}
	return resp.Respond(action.GetLKStoreItemList(&query, offset, limit, getTotal))
}

// GetLKStoreLogList is func get lucky wheel store log list
func GetLKStoreLogList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	query := model.LuckyWheelStoreLog{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}
	return resp.Respond(action.GetLKStoreLogList(&query, offset, limit, getTotal))
}

// GetSelfLKStore is func get self lucky wheel store
func GetSelfLKStore(req sdk.APIRequest, resp sdk.APIResponder) error {
	var luckyWheelCode = req.GetParam("luckyWheelCode")
	return resp.Respond(action.GetSelfLKStore(getActionSource(req), luckyWheelCode))
}

// ExChangeLKStoreItem is func exchange lucky wheel store item
func ExChangeLKStoreItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload struct {
		LuckyWheelCode string `json:"luckyWheelCode"`
		StoreItemCode  string `json:"storeItemCode"`
	}
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}
	return resp.Respond(action.ExchangeLKStoreItem(getActionSource(req), payload.LuckyWheelCode, payload.StoreItemCode))
}
