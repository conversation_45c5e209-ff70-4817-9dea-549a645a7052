package api

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

// PromotionGet get promotion list
func PromotionGet(req sdk.APIRequest, resp sdk.APIResponder) error {
	str := req.GetParam("promotionId")
	if str != "" {
		promotionId := sdk.ParseInt64(str, 0)
		if promotionId > 0 {

			return resp.Respond(action.GetPromotionById(promotionId))
		}
	}

	type myRequest struct {
		model.Promotion
		ListStatus []string `json:"listStatus"`
		ListIDs    []int64  `json:"listIds"`
	}
	var input myRequest
	str = req.GetParam("q")
	if str == "" {
		str = "{}"
	}

	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 20))
	var getTotal = req.GetParam("getTotal") == "true"
	return resp.Respond(action.GetPromotionList(&input.Promotion, input.ListStatus, input.ListIDs, offset, limit, getTotal))
}

/*
// ActivePromotionWithVoucherGet get active promotion with voucher
func ActivePromotionWithVoucherGet(req sdk.APIRequest, resp sdk.APIResponder) error {
	promotionID := sdk.ParseInt64(req.GetParam("promotionId"), 0)
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetActivePromotionWithVoucher(acc, promotionID))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}*/

// PromotionCreate create a new promotion
func PromotionCreate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Promotion
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.PromotionCreate(UserInfo.Account, &input))
}

// PromotionUpdate update a promotion details
func PromotionUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Promotion
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.PromotionUpdate(UserInfo.Account, &input))
}

// PromotionStatusUpdate update a promotion status
func PromotionStatusUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Promotion
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.UpdatePromotionStatus(&input, UserInfo.Account.AccountID))
}

// PromotionGetList ...
func PromotionGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
		ids      = req.GetParam("promotionIds")
		search   = req.GetParam("search")
	)
	var query = model.Promotion{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if len(ids) > 0 {
		idsArr := strings.Split(ids, ",")
		listIDs := []int64{}
		for _, id := range idsArr {
			intID, _ := strconv.Atoi(id)
			if intID > 0 {
				listIDs = append(listIDs, int64(intID))
			}
		}
		query.ComplexQuery = []*bson.M{
			{
				"promotion_id": &bson.M{
					"$in": listIDs,
				},
			},
		}
	}

	if len(search) > 0 {
		searchItem := parserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", searchItem), Options: ""},
		})
	}

	if query.SellerFilter != "" {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"seller_codes": query.SellerFilter,
		})

	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListPromotion(&query, offset, limit, getTotal))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func PromotionCopy(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PromotionCopyRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.PromotionCopy(acc.AccountID, input.PromotionId))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// MigrateHashTagPromotion update hash tag for promotion data
func MigrateHashTagPromotion(req sdk.APIRequest, resp sdk.APIResponder) error {
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.MigrateHashTagPromotion())
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}
