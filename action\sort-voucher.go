package action

import (
	"fmt"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"math"
	"sort"
	"time"
)

// SortVoucher - Sort Voucher
// @param vouchers []*model.VoucherViewWebOnly
// @param customerExperience *model.CustomerExperience
// @return []*model.VoucherViewWebOnly
func SortVoucher(vouchers []*model.VoucherViewWebOnly, customer *model.Customer, customerExperience *model.CustomerExperience, cart *model.Cart, sortType string) []*model.VoucherViewWebOnly {
	sortConfig := SortVoucherConfig
	sortFormula := model.SortVoucherFormula{}
	if sortType == "" {
		if sortConfig.DefaultSortType == "" {
			return vouchers
		}
		sortType = sortConfig.DefaultSortType
	}
	if formula, ok := sortConfig.FormulaList[sortType]; ok {
		isApply := formula.ApplyProvinceCodeMap[customer.ProvinceCode] || formula.ApplyProvinceCodeMap["ALL"]
		if !isApply {
			isApply = formula.ApplyCustomerIDMap[customer.CustomerID]
		}

		if !isApply {
			return vouchers
		}
		sortFormula = formula
	} else {
		return vouchers
	}
	for _, voucher := range vouchers {
		voucher.Score = getVoucherScore(voucher, customer, customerExperience, &sortFormula, cart)
	}
	// sort voucher by score
	sort.Slice(vouchers, func(i, j int) bool {
		return vouchers[i].Score > vouchers[j].Score
	})
	return vouchers
}

func getVoucherScore(voucher *model.VoucherViewWebOnly, customer *model.Customer, customerExperience *model.CustomerExperience, sortConfig *model.SortVoucherFormula, cart *model.Cart) float64 {
	parameters := make(map[string]interface{})
	// fill params
	if voucher.Discount == 0 {
		parameters[enum.SortParam.Discount] = "-"
	} else {
		parameters[enum.SortParam.Discount] = voucher.Discount
	}
	parameters[enum.SortParam.MinOrder] = voucher.MinOrderValue

	voucherSkuMap := make(map[string]bool)
	if voucher.RefSkus != nil {
		for _, sku := range voucher.RefSkus {
			voucherSkuMap[sku] = true
		}
	}

	if customerExperience != nil {
		if skus, ok := customerExperience.SkusData["purchased"]; ok {
			for _, sku := range skus {
				if _, ok := voucherSkuMap[sku]; ok {
					parameters[enum.SortParam.RefPurchase] = 1
				}
			}
		}
		if skus, ok := customerExperience.SkusData["viewed"]; ok {
			for _, sku := range skus {
				if _, ok := voucherSkuMap[sku]; ok {
					parameters[enum.SortParam.RefRecentView] = 1
					break
				}
			}
		}
		if skus, ok := customerExperience.SkusData["wishlist"]; ok {
			for _, sku := range skus {
				if _, ok := voucherSkuMap[sku]; ok {
					parameters[enum.SortParam.RefWishlist] = 1
					break
				}
			}
		}
	}

	if cart != nil {
		for _, item := range cart.AllCartItems {
			if _, ok := voucherSkuMap[item.ProductSKU]; ok {
				parameters[enum.SortParam.RefCart] = 1
				break
			}
		}
	}
	// fill voucher params
	if voucher.CustomerApplyType == enum.CustomerApplyType.MANY {
		parameters[enum.SortParam.ScopeMe] = 1
	}
	if voucher.PromotionOrganizer == enum.PromotionOrganizer.MARKETING || voucher.PromotionOrganizer == "" {
		parameters[enum.SortParam.MarketplaceOrg] = 1
	}
	if voucher.PromotionOrganizer == enum.PromotionOrganizer.SELLER_CENTER {
		parameters[enum.SortParam.SellerOrg] = 1
	}
	if voucher.PromotionOrganizer == enum.PromotionOrganizer.INTERNAL_SELLER {
		parameters[enum.SortParam.VendorOrg] = 1
	}
	if voucher.SellerCode != "" && voucher.SellerCode != string(enum.PromotionOrganizer.INTERNAL_SELLER) {
		parameters["SELLER_"+voucher.SellerCode] = 1
	}
	parameters["CUSTOMER_"+fmt.Sprint(customer.CustomerID)] = 1
	parameters["VOUCHER_"+voucher.Code] = 1

	// get remaining & total day from start & end date
	if voucher.StartTime != nil && voucher.EndTime != nil {
		now := time.Now()
		parameters[enum.SortParam.RemainDay] = int(voucher.EndTime.Sub(now).Hours())
		parameters[enum.SortParam.TotalDay] = int(voucher.EndTime.Sub(*voucher.StartTime).Hours())
	}

	mapSubFormulaResult := make(map[string]interface{})

	for k, subFormula := range sortConfig.SubFormula {
		result, err := utils.ParseFormulaToInterface(subFormula, parameters)
		if err != nil {
			continue
		}
		val, ok := result.(float64)
		if !ok {
			continue
		}
		if !math.IsInf(val, 0) && !math.IsNaN(val) {
			mapSubFormulaResult[k] = val
		}
	}
	// mapSubFormulaResult is params for formula, sent default value if not found key
	for k, v := range sortConfig.DefaultValue {
		if _, ok := mapSubFormulaResult[k]; !ok {
			mapSubFormulaResult[k] = v
		}
	}
	// calculate formula
	result, err := utils.ParseFormulaToInterface(sortConfig.Formula, mapSubFormulaResult)
	if err != nil {
		return 0
	}
	val, ok := result.(float64)
	if !ok {
		return 0
	}
	if math.IsInf(val, 0) || math.IsNaN(val) {
		return 0
	}
	voucher.SortParameters = parameters
	return val
}
