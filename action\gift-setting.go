package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
)

// CreateGiftSetting is func to create gift setting
func CreateGiftSetting(username string, in *model.GiftSetting) *common.APIResponse {
	return model.GiftSettingDB.Upsert(bson.M{}, in)
}

// GetGiftSetting is func to get gift setting
func GetGiftSetting() *common.APIResponse {
	return model.GiftSettingDB.QueryOne(bson.M{})
}

// GetSelfGiftInfo is func ...
func GetSelfGiftInfo(acc *model.Account) *common.APIResponse {
	res := model.GiftSettingDB.QueryOne(bson.M{})
	if res.Status != common.APIStatus.Ok {
		return res
	}
	res.Data.([]*model.GiftSetting)[0].NewbieGift = nil
	res.Data.([]*model.GiftSetting)[0].FriendGift = nil
	res.Data.([]*model.GiftSetting)[0].CreatedBy = ""
	return res
}
