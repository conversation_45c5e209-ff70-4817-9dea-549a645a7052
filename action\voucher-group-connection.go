package action

import (
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetVoucherGroupConnectionsList(q *model.VoucherGroupConnectionQuery) *common.APIResponse {
	result := model.VoucherGroupConnectionDB.Query(
		q,
		q.Offset,
		q.Limit,
		&primitive.M{"_id": -1})

	if q.GetTotal {
		countResult := model.VoucherGroupConnectionDB.Count(q)
		result.Total = countResult.Total
	}

	return result
}

func GetVoucherGroupConnectionsListByGroupCode(q *model.VoucherGroupConnectionQuery) *common.APIResponse {
	q.ComplexOrQueries = []*bson.M{
		{
			"first_code": q.VoucherGroupCode,
		},
		{
			"second_code": q.VoucherGroupCode,
		},
	}

	return GetVoucherGroupConnectionsList(q)
}

func GetVoucherGroupConnection(q *model.VoucherGroupConnectionQuery) *common.APIResponse {

	result := model.VoucherGroupConnectionDB.QueryOne(q)

	return result
}

func CreateVoucherGroupConnection(data *model.VoucherGroupConnection) *common.APIResponse {
	existsResp, _ := checkIfVoucherGroupConnectionExists(data.FirstCode, data.SecondCode)

	if existsResp != nil {
		return existsResp
	}

	data.ConnectionId, data.ConnectionCode = model.GenVoucherGroupConnectionID()
	result := model.VoucherGroupConnectionDB.Create(data)

	if result.Status == common.APIStatus.Ok {
		go func() {
			UpdateVoucherGroupConnectionCasesDBFromConnectionsDB()
		}()
	}

	return result
}

func checkIfVoucherGroupConnectionExists(firstCode string, secondCode string) (*common.APIResponse, int64) {
	if exists, connectionId := connectionExists(firstCode, secondCode); exists {
		return &common.APIResponse{
			Status:    common.APIStatus.Existed,
			Message:   "Voucher group connection already exists",
			ErrorCode: "VOUCHER_GROUP_CONNECTION_ALREADY_EXISTS",
		}, connectionId
	}

	return nil, 0
}

func connectionExists(firstCode string, secondCode string) (bool, int64) {
	if firstCode == "" || secondCode == "" {
		return false, 0
	}

	q := &model.VoucherGroupConnectionQuery{
		FirstCode:  firstCode,
		SecondCode: secondCode,
	}

	resp := model.VoucherGroupConnectionDB.QueryOne(q)

	q1 := &model.VoucherGroupConnectionQuery{
		FirstCode:  secondCode,
		SecondCode: firstCode,
	}

	resp1 := model.VoucherGroupConnectionDB.QueryOne(q1)

	if resp.Status == common.APIStatus.Ok &&
		len(resp.Data.([]*model.VoucherGroupConnection)) > 0 {
		return true, resp.Data.([]*model.VoucherGroupConnection)[0].ConnectionId
	}

	if resp1.Status == common.APIStatus.Ok &&
		len(resp1.Data.([]*model.VoucherGroupConnection)) > 0 {
		return true, resp1.Data.([]*model.VoucherGroupConnection)[0].ConnectionId
	}

	return false, 0
}

func UpdateVoucherGroupConnection(
	q *model.VoucherGroupConnectionQuery,
	data *model.VoucherGroupConnection,
) *common.APIResponse {

	currentConnections := model.VoucherGroupConnectionDB.QueryOne(q)

	if currentConnections.Status != common.APIStatus.Ok ||
		len(currentConnections.Data.([]*model.VoucherGroupConnection)) == 0 {
		// connection not found
		return currentConnections
	}

	currentConnection := currentConnections.Data.([]*model.VoucherGroupConnection)[0]

	if data.FirstCode == "" {
		data.FirstCode = currentConnection.FirstCode
	}

	if data.SecondCode == "" {
		data.SecondCode = currentConnection.SecondCode
	}

	existsResp, existedConnectionId := checkIfVoucherGroupConnectionExists(data.FirstCode, data.SecondCode)

	if existsResp != nil && existedConnectionId != q.ConnectionId {
		return existsResp
	}

	updateResp := model.VoucherGroupConnectionDB.UpdateOne(q, data)

	if updateResp.Status == common.APIStatus.Ok {
		go func() {
			UpdateVoucherGroupConnectionCasesDBFromConnectionsDB()
		}()
	}

	return updateResp
}

func UpdateVoucherGroupConnectionCasesDBFromConnectionsDB() {
	q := &model.VoucherGroupConnectionQuery{}

	connections := model.VoucherGroupConnectionDB.Query(q, 0, 0, &primitive.M{"_id": -1})

	if connections.Status == common.APIStatus.Ok {
		voucherGroupConnections := connections.Data.([]*model.VoucherGroupConnection)
		updateVoucherGroupConnectionCases(voucherGroupConnections)
	}
}

func updateVoucherGroupConnectionCases(connections []*model.VoucherGroupConnection) {
	connectionCases := make(map[string]bool, 0)
	splitSign := model.VGC_SPIT_SIGN

	for _, connection := range connections {
		if connection == nil || connection.FirstQuantity == nil || connection.SecondQuantity == nil {
			continue
		}
		if connection.IsActive == nil || !*connection.IsActive {
			continue
		}
		var connectionCase string

		if connection.FirstCode == connection.SecondCode {
			for i := 1; i <= *connection.FirstQuantity+*connection.SecondQuantity-1; i++ {
				connectionCase = connection.FirstCode + splitSign + "1" + splitSign +
					connection.SecondCode + splitSign + strconv.Itoa(i)

				connectionCases[connectionCase] = true
			}
			continue
		}

		// case: firstCode != secondCode
		for i := 1; i <= *connection.FirstQuantity; i++ {
			for j := 1; j <= *connection.SecondQuantity; j++ {
				connectionCase = connection.FirstCode + splitSign + strconv.Itoa(i) + splitSign +
					connection.SecondCode + splitSign + strconv.Itoa(j)

				connectionCases[connectionCase] = true
			}
		}
	}
	model.SettingDB.UpdateMany(model.Setting{}, &model.Setting{
		VoucherGroupConnectionCases: connectionCases,
	})
}
