package model

import (
	"time"

	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Voucher struct {
	ID                primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime       *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy         int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	CreatedByUserName *string            `json:"createdByUserName,omitempty" bson:"created_by_user_name,omitempty"`
	LastUpdatedTime   *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	UpdatedBy         int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	VersionNo         string             `json:"versionNo,omitempty" bson:"version_no,omitempty"`

	DisplayName          string                                    `json:"displayName,omitempty" bson:"display_name,omitempty"`
	ShortName            string                                    `json:"shortName,omitempty" bson:"short_name,omitempty"`
	VoucherID            int64                                     `json:"voucherId,omitempty" bson:"voucher_id,omitempty"`
	Code                 string                                    `json:"code,omitempty" bson:"code,omitempty"`
	PromotionID          int64                                     `json:"promotionId,omitempty" bson:"promotion_id,omitempty"`
	PromotionName        string                                    `json:"promotionName,omitempty" bson:"promotion_name,omitempty"`
	PromotionType        *enum.PromotionTypeValue                  `json:"promotionType,omitempty" bson:"promotion_type,omitempty"`
	ApplyType            enum.ApplyTypeValue                       `json:"applyType,omitempty" bson:"apply_type,omitempty"`
	StartTime            *time.Time                                `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime              *time.Time                                `json:"endTime,omitempty" bson:"end_time,omitempty"`
	PublicTime           *time.Time                                `json:"publicTime,omitempty" bson:"public_time,omitempty"`
	MaxUsage             *int64                                    `json:"maxUsage" bson:"max_usage,omitempty"`
	MaxUsagePerCustomer  *int64                                    `json:"maxUsagePerCustomer" bson:"max_usage_per_customer,omitempty"`
	MaxAutoApplyCount    *int64                                    `json:"maxAutoApplyCount,omitempty" bson:"max_auto_apply_count,omitempty"`
	UsageTotal           *int64                                    `json:"usageTotal" bson:"usage_total,omitempty"`
	VoucherType          *enum.VoucherTypeValue                    `json:"type,omitempty" bson:"type,omitempty"`
	VoucherGroupCode     *string                                   `json:"voucherGroupCode,omitempty" bson:"voucher_group_code,omitempty"`
	UsageType            *enum.UsageTypeValue                      `json:"usageType,omitempty" bson:"usage_type,omitempty"`
	AppliedCustomers     *[]int64                                  `json:"appliedCustomers" bson:"applied_customers,omitempty"`
	AppliedCustomerMap   map[int64]bool                            `json:"-" bson:"applied_customer_map,omitempty"`
	Status               *enum.VoucherStatusValue                  `json:"status,omitempty" bson:"status,omitempty"`
	Promotion            *Promotion                                `json:"promotion,omitempty" bson:"-"`
	UserPromotion        *UserPromotion                            `json:"userPromotion,omitempty" bson:"-"`
	HashTag              string                                    `json:"-" bson:"hash_tag,omitempty"`
	Usable               *bool                                     `json:"-" bson:"usable,omitempty"`
	Scopes               []Scope                                   `json:"scopes,omitempty" bson:"scopes,omitempty"`
	Rewards              []Reward                                  `json:"rewards,omitempty" bson:"rewards,omitempty"`
	OrConditions         map[enum.ConditionTypeValue]PromotionType `json:"orConditions,omitempty" bson:"or_conditions,omitempty"`
	AndConditions        map[enum.ConditionTypeValue]PromotionType `json:"andConditions,omitempty" bson:"and_conditions,omitempty"`
	ConditionDescription *string                                   `json:"conditionDescription,omitempty" bson:"condition_description,omitempty"`
	Priority             *int                                      `json:"priority,omitempty" bson:"priority,omitempty"`
	CustomerApplyType    enum.CustomerApplyTypeValue               `json:"customerApplyType,omitempty" bson:"customer_apply_type,omitempty"`
	IsSkipNextVoucher    *bool                                     `json:"isSkipNextVoucher,omitempty" bson:"is_skip_next_voucher,omitempty"`
	LevelScopeMap        map[string]bool                           `json:"-" bson:"level_scope_map,omitempty"`
	RegionScopeMap       map[string]bool                           `json:"-" bson:"region_scope_map,omitempty"`
	CustomerScopeMap     map[string]bool                           `json:"-" bson:"customer_scope_map,omitempty"`
	Filter               []string                                  `json:"-" bson:"filter,omitempty"`
	SystemNote           string                                    `json:"-" bson:"system_note,omitempty"`
	RefProduct           []string                                  `json:"-" bson:"ref_product,omitempty"`
	IsMigrateUserCode    bool                                      `json:"-" bson:"is_migrate_user_code,omitempty"`
	ExistRefProduct      *bool                                     `json:"-" bson:"exist_ref_product,omitempty"`
	VoucherImage         *string                                   `json:"voucherImage,omitempty" bson:"voucher_image,omitempty"`
	Tag                  *string                                   `json:"tag,omitempty" bson:"tag,omitempty"`
	SellerCode           *string                                   `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerCodes          *[]string                                 `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	SellerName           *string                                   `json:"sellerName,omitempty" bson:"seller_name,omitempty"`
	StoreCode            *string                                   `json:"storeCode,omitempty" bson:"store_code,omitempty"`
	StoreName            *string                                   `json:"storeName,omitempty" bson:"store_name,omitempty"`
	ConditionsVoucher    *[]string                                 `json:"conditionsVoucher,omitempty" bson:"conditions_voucher,omitempty"`

	ApplyDiscount *ApplyDiscountOptions `json:"applyDiscount,omitempty" bson:"apply_discount,omitempty"`

	SystemDisplay string  `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
	LinkToPage    *string `json:"linkToPage,omitempty" bson:"link_to_page,omitempty"`
	LinkToStore   *string `json:"linkToStore,omitempty" bson:"link_to_store,omitempty"`

	NeedCheck *bool `json:"_" bson:"need_check,omitempty"`

	PromotionOrganizer    *enum.PromotionOrganizerValue `json:"promotionOrganizer,omitempty" bson:"promotion_organizer,omitempty"`
	ChargeFee             *enum.ChargeFeeValue          `json:"chargeFee,omitempty" bson:"charge_fee,omitempty"`
	IsUsed                *bool                         `json:"isUsed,omitempty" bson:"is_used,omitempty"`
	FilterProduct         *FilterProduct                `json:"filterProduct,omitempty" bson:"filter_product,omitempty"`
	IsSpecific            *bool                         `json:"isSpecific,omitempty" bson:"is_specific,omitempty"`
	ConditionMessages     []*ConditionMessage           `json:"conditionMessages,omitempty" bson:"condition_messages,omitempty"`
	PercentageCondition   *float64                      `json:"percentageCondition,omitempty" bson:"percentage_condition,omitempty"`
	SkipCheckVoucherGroup *bool                         `json:"skipCheckVoucherGroup,omitempty" bson:"skip_check_voucher_group,omitempty"`

	// For voucher re-issuable on cancel order
	OldVoucherCode       string `json:"oldVoucherCode,omitempty" bson:"-"` // this field for save old voucher code
	IsReuseOnOrderCancel *bool  `json:"isReuseOnOrderCancel,omitempty" bson:"is_reuse_on_order_cancel,omitempty"`
	VoucherReuseDuration *int   `json:"voucherReuseDuration,omitempty" bson:"voucher_reuse_duration,omitempty"`

	// For filter
	TimeFrom        *time.Time `json:"timeFrom" bson:"-"`
	TimeTo          *time.Time `json:"timeTo" bson:"-"`
	CustomerID      *int64     `json:"customerId,omitempty" bson:"-"`
	ProductCode     string     `json:"productCode,omitempty" bson:"-"`
	SellerFilter    string     `json:"sellerFilter,omitempty" bson:"-"`
	ConditionFilter string     `json:"conditionFilter,omitempty" bson:"-"`
	ApplyTimeFrom   *time.Time `json:"applyTimeFrom" bson:"-"`
	ApplyTimeTo     *time.Time `json:"applyTimeTo" bson:"-"`
	StatusFilter    string     `json:"statusFilter,omitempty" bson:"-"`

	ComplexQuery       []*bson.M      `json:"-" bson:"$and,omitempty"`
	CanUse             bool           `json:"canUse,omitempty" bson:"-"`
	ActionStatus       string         `json:"actionStatus,omitempty" bson:"-"`
	SkipReturn         bool           `json:"-" bson:"-"`
	ErrorMessage       string         `json:"errorMessage,omitempty" bson:"-"`
	ErrorCode          string         `json:"errorCode,omitempty" bson:"-"`
	Discount           int            `json:"discount,omitempty" bson:"-"`
	MinOrderValue      int64          `json:"minOrderValue,omitempty" bson:"-"`
	Gifts              []Gift         `json:"gifts,omitempty" bson:"-"`
	TotalGiftValue     int64          `json:"totalGiftValue,omitempty" bson:"-"`
	DiscountInfos      []DiscountInfo `json:"discountInfos,omitempty" bson:"-"`
	PriceToCalDiscount int64          `json:"priceToCalDiscount,omitempty" bson:"-"`

	SettingType      *enum.SettingTypeValue `json:"settingType,omitempty" bson:"setting_type,omitempty"`
	SegmentationCode string                 `json:"segmentationCode,omitempty" bson:"segmentation_code,omitempty"`
	SegmentationName string                 `json:"segmentationName,omitempty" bson:"segmentation_name,omitempty"`
}

type RefProduct struct {
	SellerCode  string `json:"-" bson:"seller_code,omitempty"`
	ProductCode string `json:"-" bson:"product_code,omitempty"`
	Sku         string `json:"-" bson:"sku,omitempty"`
}

type ApplyDiscountOptions struct {
	Tags              *[]string         `json:"tags,omitempty" bson:"tags,omitempty"`
	NotInTags         *[]string         `json:"notInTags,omitempty" bson:"not_in_tags,omitempty"`
	Skus              *[]string         `json:"skus,omitempty" bson:"skus,omitempty"`
	NotInSkus         *[]string         `json:"notInSkus,omitempty" bson:"not_in_skus,omitempty"`
	SkuName           map[string]string `json:"skuName,omitempty" bson:"sku_name,omitempty"`
	ApplyDiscountType map[string]bool   `json:"applyDiscountType,omitempty" bson:"apply_discount_type,omitempty"` // key in [ABSOLUTE, PERCENTAGE, GIFT, ALL]
	ApplyOrganization map[string]bool   `json:"applyOrganization,omitempty" bson:"apply_organization,omitempty"`  // key in [MARKETING, INTERNAL_SELLER, SELLER_CENTER, ALL]
}

var VoucherDB = &db.Instance{
	ColName:        "voucher",
	TemplateObject: &Voucher{},
}

// InitVoucherModel is func init model
func InitVoucherModel(s *mongo.Database) {
	VoucherDB.ApplyDatabase(s)

	t := true
	VoucherDB.CreateIndex(bson.D{
		primitive.E{Key: "status", Value: 1},
		primitive.E{Key: "apply_type", Value: 1},
		primitive.E{Key: "system_display", Value: 1},
		primitive.E{Key: "_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	VoucherDB.CreateIndex(bson.D{
		primitive.E{Key: "promotion_id", Value: 1},
		primitive.E{Key: "_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}

type VoucherViewWebOnly struct {
	ShortName            string                       `json:"shortName,omitempty" bson:"-"`
	VoucherImage         string                       `json:"voucherImage,omitempty" bson:"-"`
	DisplayName          string                       `json:"displayName,omitempty" bson:"-"`
	VoucherID            int64                        `json:"voucherId,omitempty" bson:"-"`
	Code                 string                       `json:"code,omitempty" bson:"-"`
	PromotionName        string                       `json:"promotionName,omitempty" bson:"-"`
	Description          string                       `json:"description,omitempty" bson:"-"`
	StartTime            *time.Time                   `json:"startTime,omitempty" bson:"-"`
	EndTime              *time.Time                   `json:"endTime,omitempty" bson:"-"`
	PublicTime           *time.Time                   `json:"publicTime,omitempty" bson:"-"`
	ConditionDescription *string                      `json:"conditionDescription,omitempty" bson:"-"`
	CanUse               bool                         `json:"canUse,omitempty" bson:"-"`
	ErrorMessage         string                       `json:"errorMessage,omitempty" bson:"-"`
	ErrorCode            string                       `json:"errorCode,omitempty" bson:"-"`
	Discount             int                          `json:"discount,omitempty" bson:"-"`
	Gifts                []Gift                       `json:"gifts,omitempty" bson:"-"`
	MaxUsage             *int64                       `json:"maxUsage,omitempty" bson:"-"`
	UsageTotal           *int64                       `json:"usageTotal,omitempty" bson:"-"`
	GroupCode            string                       `json:"groupCode,omitempty"`
	Tag                  *string                      `json:"tag,omitempty" bson:"-"`
	ActionStatus         string                       `json:"actionStatus,omitempty" bson:"-"` // AVAILABLE, DISABLE, INUSE, INVALID
	ActionLink           string                       `json:"actionLink,omitempty" bson:"-"`   // quick order
	ApplyType            enum.ApplyTypeValue          `json:"applyType,omitempty" bson:"-"`
	CustomerApplyType    enum.CustomerApplyTypeValue  `json:"customerApplyType,omitempty" bson:"-"`
	IsOwner              bool                         `json:"-" bson:"-"`
	PromotionOrganizer   enum.PromotionOrganizerValue `json:"promotionOrganizer" bson:"-"`
	SellerCode           string                       `json:"sellerCode,omitempty" bson:"-"`
	SellerCodes          []string                     `json:"sellerCodes,omitempty" bson:"-"`
	CollectStatus        string                       `json:"collectStatus,omitempty" bson:"-"` // AVAILABLE, COLLECTED, OWNERSHIP
	SellerName           *string                      `json:"sellerName,omitempty" bson:"-"`

	Voucher struct { // app old version
		Code             string     `json:"code,omitempty"`
		AppliedCustomers []int64    `json:"appliedCustomers" bson:"-"`
		PromotionName    string     `json:"promotionName,omitempty" bson:"-"`
		StartTime        *time.Time `json:"startTime,omitempty" bson:"-"`
		EndTime          *time.Time `json:"endTime,omitempty" bson:"-"`
		Promotion        struct {
			Description string `json:"description,omitempty"`
		} `json:"promotion,omitempty"`
	} `json:"voucher,omitempty" bson:"-"`
	DiscountInfos         []DiscountInfo               `json:"discountInfos,omitempty" bson:"-"`
	MinOrderValue         int64                        `json:"minOrderValue,omitempty" bson:"-"`
	IsCollected           *bool                        `json:"-" bson:"-"`
	LinkToStore           *string                      `json:"linkToStore,omitempty" bson:"-"` // link redirect to store
	Priority              *int                         `json:"priority,omitempty" bson:"-"`
	IsVoucherByProduct    bool                         `json:"isVoucherByProduct,omitempty" bson:"-"`
	PaymentMethod         string                       `json:"paymentMethod,omitempty" bson:"-"`
	PaymentMethodName     string                       `json:"paymentMethodName,omitempty" bson:"-"`
	FilterProduct         *FilterProduct               `json:"filterProduct,omitempty" bson:"filter_product,omitempty"`
	DiscountPercent       float64                      `json:"discountPercent,omitempty" bson:"-"`
	MaxDiscount           float64                      `json:"maxDiscount,omitempty" bson:"-"`
	OrTags                map[string]QuantityAndAmount `json:"orTags,omitempty" bson:"-"`  // map tagCode  - quantity, amount
	OrSkus                map[string]QuantityAndAmount `json:"orSkus,omitempty" bson:"-"`  // map sku - quantity, amount
	AndTags               map[string]QuantityAndAmount `json:"andTags,omitempty" bson:"-"` // map tagCode  - quantity, amount
	AndSkus               map[string]QuantityAndAmount `json:"andSkus,omitempty" bson:"-"` // map sku - quantity, amount
	Score                 float64                      `json:"score,omitempty" bson:"-"`
	SortParameters        map[string]interface{}       `json:"sortParameters,omitempty" bson:"-"`
	RefSkus               []string                     `json:"refSkus,omitempty" bson:"-"`
	SkipCheckVoucherGroup *bool                        `json:"-" bson:"-"`
	MapSkuMessage         map[string]string            `json:"-" bson:"-"`
	IsVoucherByOrder      bool                         `json:"isVoucherByOrder,omitempty" bson:"-"`
}

type QuantityAndAmount struct {
	Quantity int `json:"quantity,omitempty" bson:"quantity,omitempty"`
	MinPrice int `json:"minPrice,omitempty" bson:"min_price,omitempty"`
}

type DiscountInfo struct {
	Discount    int      `json:"discount,omitempty"`
	Sku         string   `json:"sku,omitempty"`
	IsApply     bool     `json:"isApply,omitempty"`
	Message     string   `json:"message,omitempty"`
	SellerCodes []string `json:"sellerCodes,omitempty"`
	StoreCode   *string  `json:"storeCode,omitempty"`
}

type FilterProduct struct {
	Skus         *[]string `json:"skus,omitempty" bson:"skus,omitempty"`
	Tags         *[]string `json:"tags,omitempty" bson:"tags,omitempty"`
	Sellers      *[]string `json:"sellers,omitempty" bson:"sellers,omitempty"`
	ProductCodes *[]string `json:"productCodes,omitempty" bson:"productCodes,omitempty"`
	SkuNotIn     *[]string `json:"skuNotIn,omitempty" bson:"skuNotIn,omitempty"`
	TagIn        *[]string `json:"tagIn,omitempty" bson:"tagIn,omitempty"`
}

type UpdateSpecificVoucher struct {
	IsSpecific *bool  `json:"isSpecific,omitempty" bson:"is_specific,omitempty"`
	Code       string `json:"code,omitempty" bson:"code,omitempty"`
	VoucherID  int64  `json:"voucherId,omitempty" bson:"voucher_id,omitempty"`
}

type ConditionMessage struct {
	Message              string                    `json:"message,omitempty" bson:"-"`
	IsValid              *bool                     `json:"isValid,omitempty" bson:"-"`
	ErrorCode            enum.VoucherErrorCodeType `json:"errorCode,omitempty" bson:"-"`
	VoucherConditionType enum.ConditionTypeValue   `json:"voucherConditionType,omitempty" bson:"-"`
	ConditionType        string                    `json:"conditionType,omitempty" bson:"-"`
	Subs                 []*ConditionMessage       `json:"subs,omitempty" bson:"-"`
	FilterProduct        *FilterProduct            `json:"filterProduct,omitempty" bson:"-"`
	PercentageCondition  *float64                  `json:"percentageCondition,omitempty" bson:"-"`
	Current              *int                      `json:"current,omitempty" bson:"-"`
	Target               *int                      `json:"target,omitempty" bson:"-"`
	ProgressMessage      string                    `json:"progressMessage,omitempty" bson:"-"`
	GroupIndex           int                       `json:"groupIndex,omitempty" bson:"-"`
}

type ConditionMessageResponse struct {
	ConditionMessages      []*ConditionMessage `json:"conditionMessages,omitempty" bson:"conditionMessages,omitempty"`
	PercentageAllCondition *float64            `json:"percentageCondition,omitempty" bson:"percentageCondition,omitempty"`
}

var VoucherCacheDB = &db.Instance{
	ColName:        "voucher",
	TemplateObject: &Voucher{},
}

// InitVoucherCacheModel is func init model
func InitVoucherCacheModel(s *mongo.Database) {
	VoucherCacheDB.ApplyDatabase(s)
	// Add indexes in `promotion-worker` source code
}

var VoucherCacheReadDB = &db.Instance{
	ColName:        "voucher",
	TemplateObject: &Voucher{},
}

func InitVoucherCacheReadOnly(s *mongo.Database) {
	VoucherCacheReadDB.ApplyDatabase(s)
}
