package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Order ...
type Order struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// reference data
	OrderID   int64  `json:"orderId,omitempty" bson:"order_id,omitempty"` //
	OrderCode string `json:"orderCode" bson:"order_code,omitempty"`       //

	// customer info
	AccountID               int64   `json:"accountId,omitempty" bson:"account_id,omitempty"`                              // mã tài khoản
	CustomerID              int64   `json:"customerId,omitempty" bson:"customer_id,omitempty"`                            // mã khách hàng
	CustomerCode            string  `json:"customerCode,omitempty" bson:"customer_code,omitempty"`                        // mã khách hàng
	CustomerName            string  `json:"customerName,omitempty" bson:"customer_name,omitempty"`                        // tên người nhận
	CustomerPhone           string  `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`                      // điện thoại người nhận
	CustomerEmail           *string `json:"customerEmail,omitempty" bson:"customer_email,omitempty"`                      // email người nhận
	CustomerShippingAddress string  `json:"customerShippingAddress,omitempty" bson:"customer_shipping_address,omitempty"` // địa chỉ người nhận
	CustomerDistrictCode    string  `json:"customerDistrictCode,omitempty" bson:"customer_district_code,omitempty"`       // khu vực nhận
	CustomerWardCode        string  `json:"customerWardCode,omitempty" bson:"customer_ward_code,omitempty"`               //
	CustomerProvinceCode    string  `json:"customerProvinceCode,omitempty" bson:"customer_province_code,omitempty"`       //
	CustomerOrderIndex      int     `json:"customerOrderIndex,omitempty" bson:"customer_order_index,omitempty"`           //

	ProvinceCode string `json:"provinceCode,omitempty" bson:"province_code,omitempty"` //
	RegionCode   string `json:"regionCode,omitempty" bson:"region_code,omitempty"`     //

	// payment & delivery
	PaymentMethod           string     `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`                      // phương thức thanh toán cod/chuyển khoản
	PaymentMethodFee        *int64     `json:"paymentMethodFee,omitempty" bson:"payment_method_fee,omitempty"`               // phí phương thức thanh toán cod/chuyển khoản
	PaymentMethodPercentage *float64   `json:"paymentMethodPercentage,omitempty" bson:"payment_method_percentage,omitempty"` // phần trăm giảm giá cho hình thức thanh toán
	DeliveryMethod          string     `json:"deliveryMethod,omitempty" bson:"delivery_method,omitempty"`                    // hình thức giao hàng
	DeliveryMethodFee       *int64     `json:"deliveryMethodFee,omitempty" bson:"delivery_method_fee,omitempty"`             // phí hình thức giao hàng
	DeliveryStatus          string     `json:"deliveryStatus,omitempty" bson:"delivery_status,omitempty"`                    // trạng thái nhà vận chuyển: đang lấy,...
	DeliveryTrackingNumber  string     `json:"deliveryTrackingNumber,omitempty" bson:"delivery_tracking_number,omitempty"`   // mã tracking
	DeliveryDate            *time.Time `json:"deliveryDate,omitempty" bson:"delivery_date,omitempty"`                        // ngày giao mong muốn
	ExtraFee                *int64     `json:"extraFee,omitempty" bson:"extra_fee,omitempty"`                                // phụ phí

	// price
	ActualPrice   *int `json:"actualPrice,omitempty" bson:"actual_price,omitempty"`
	TotalPrice    *int `json:"totalPrice,omitempty" bson:"total_price,omitempty"`       // tổng tiền đơn hàng sau cùng
	Price         *int `json:"price,omitempty" bson:"price,omitempty"`                  // tổng tiển chưa trừ các khoản khác
	TotalDiscount *int `json:"totalDiscount,omitempty" bson:"total_discount,omitempty"` // tổng số tiền được giảm
	TotalFee      *int `json:"totalFee,omitempty" bson:"total_fee,omitempty"`           // tổng phí

	// order info
	ConfirmationDate *time.Time                  `json:"confirmationDate,omitempty" bson:"confirmation_date,omitempty"` // ngày xác nhận -- field cũ
	CompletedTime    *time.Time                  `json:"completedTime,omitempty" bson:"completed_time,omitempty"`       // thời gian hoàn tất đơn hàng -- status = completed
	Source           *string                     `json:"source,omitempty" bson:"source,omitempty"`                      // nguồn đơn hàng (web/mobile)
	Status           enum.OrderStateValue        `json:"status,omitempty" bson:"status,omitempty"`                      // trạng thái đơn hàng
	Note             *string                     `json:"note,omitempty" bson:"note,omitempty"`                          // ghi chú đơn hàng
	PrivateNote      string                      `json:"privateNote,omitempty" bson:"private_note,omitempty"`           // ghi chú nội bộ đơn hàng
	SaleOrderCode    string                      `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`      // so
	ConfirmType      *enum.OrderConfirmTypeValue `json:"confirmType,omitempty" bson:"confirm_type,omitempty"`           // xác nhận đơn hàng
	HasDeal          bool                        `json:"hasDeal,omitempty" bson:"has_deal,omitempty"`
	AutoConfirmNote  *string                     `json:"autoConfirmNote,omitempty" bson:"auto_confirm_note,omitempty"`
	// promotion
	RedeemCode *[]*string `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"` // mã giảm giá

	//statistic
	TotalItem     *int     `json:"totalItem,omitempty" bson:"total_item,omitempty"`
	TotalQuantity *int     `json:"totalQuantity,omitempty" bson:"total_quantity,omitempty"`
	Point         *float64 `json:"point,omitempty" bson:"point,omitempty"` // point = actual price / 100.000

	// items
	/// used only for query, not for save data
	PriceFrom       *int       `json:"priceFrom,omitempty" bson:"-"`
	PriceTo         *int       `json:"priceTo,omitempty" bson:"-"`
	DateFrom        *time.Time `json:"timeFrom,omitempty" bson:"-"`
	DateTo          *time.Time `json:"timeTo,omitempty" bson:"-"`
	StatusIn        []string   `json:"statusIn,omitempty" bson:"-"`
	SaleOrderCodeIn []string   `json:"saleOrderCodeIn,omitempty" bson:"-"`
}

// FeesApply ...
type FeesApply struct {
	Result     []*FeeValue            `json:"result,omitempty" bson:"result,omitempty"`
	Parameters map[string]interface{} `json:"parameters,omitempty" bson:"parameters"`
	Total      int                    `json:"total,omitempty" bson:"total"`
	Price      int                    `json:"price,omitempty" bson:"price"` // price : gia goc
}

// FeeValue ...
type FeeValue struct {
	FeeCode  string  `json:"feeCode,omitempty" bson:"fee_code,omitempty"`
	FeeValue float64 `json:"feeValue,omitempty" bson:"fee_value,omitempty"`
}

// OrderSourceDetail includes information about the OS, Platform used to place the order
type OrderSourceDetail struct {
	Os             string `json:"os,omitempty" bson:"os,omitempty"`
	OsVersion      string `json:"osVersion,omitempty" bson:"os_version,omitempty"`
	Browser        string `json:"browser,omitempty" bson:"browser,omitempty"`
	BrowserVersion string `json:"browserVersion,omitempty" bson:"browser_version,omitempty"`
	Platform       string `json:"platform,omitempty" bson:"platform,omitempty"`
}