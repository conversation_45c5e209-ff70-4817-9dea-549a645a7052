package action

import (
	"fmt"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UserPromotionList user code list
func UserPromotionList(query model.UserPromotion, offset, limit int64, getTotal bool) *common.APIResponse {
	filter := bson.M{}
	if query.Status != nil && *query.Status != "" {
		filter["status"] = *query.Status
	}

	if query.CustomerID > 0 {
		filter["customer_id"] = query.CustomerID
	}

	result := model.UserPromotionDB.Query(
		filter,
		offset,
		limit,
		&primitive.M{"_id": -1})
	if getTotal {
		countResult := model.PromotionDB.Count(filter)
		result.Total = countResult.Total
	}
	return result
}

// UseVoucher user use a voucher
func UseVoucher(accountID, orderID int64, voucherCodes []string, applyVoucherCount map[string]int) *common.APIResponse {
	if applyVoucherCount == nil {
		applyVoucherCount = make(map[string]int)
	}
	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(accountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Tài khoản không xác định",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}
	customerID := customerInfo.CustomerID

	qVoucher := model.VoucherDB.Query(model.Voucher{
		ComplexQuery: []*bson.M{
			{
				"code": bson.M{"$in": voucherCodes},
			},
		},
	}, 0, 0, nil)
	if qVoucher.Status != common.APIStatus.Ok {
		return qVoucher
	}
	qUsed := model.UserPromotionDB.Query(model.UserPromotion{
		ComplexQuery: []*bson.M{
			{
				"voucher_code": bson.M{"$in": voucherCodes},
			},
		},
		CustomerID: customerID,
	}, 0, 0, nil)
	mapUsed := make(map[string]*model.UserPromotion)
	if qUsed.Status == common.APIStatus.Ok {
		for _, used := range qUsed.Data.([]*model.UserPromotion) {
			mapUsed[used.VoucherCode] = used
		}
	}
	vouchers := qVoucher.Data.([]*model.Voucher)
	for _, voucher := range vouchers {
		now := time.Now()
		newVersionNo := uuid.New().String()
		userPromotion := mapUsed[voucher.Code]
		if userPromotion != nil {
			curOrderIds := make([]int64, 0)
			newOrderIds := make([]int64, 0)
			if userPromotion.OrderIDs != nil {
				curOrderIds = *userPromotion.OrderIDs
			}
			currentVersionNo := userPromotion.VersionNo
			newOrderIds = append(curOrderIds, orderID)
			userPromotion.OrderIDs = &newOrderIds
			newGiftCount := make(map[string]int)
			gifts := make([]model.Gift, 0)
			if voucher.Rewards != nil && len(voucher.Rewards) > 0 {
				reward := voucher.Rewards[0]
				if reward.Type != nil && *reward.Type == enum.RewardType.GIFT {
					gifts = reward.Gifts
				}
				for _, gift := range gifts {
					curQuantity := 0
					if userPromotion.GiftCount != nil {
						curQuantity = userPromotion.GiftCount[gift.Sku]
					}
					newGiftCount[gift.Sku] = int(gift.Quantity)*applyVoucherCount[voucher.Code] + curQuantity
				}
			}
			userPromotion.GiftCount = newGiftCount
			if userPromotion.Amount == nil {
				userPromotion.Amount = utils.ParseInt64ToPointer(1)
			} else {
				userPromotion.Amount = utils.ParseInt64ToPointer(*userPromotion.Amount + 1)
			}
			userPromotion.TotalRewardCount = userPromotion.TotalRewardCount + applyVoucherCount[voucher.Code]
			userPromotion.UsedTime = &now
			userPromotion.Status = &enum.CodeStatus.USED
			userPromotion.LastUpdatedTime = &now
			userPromotion.UpdatedBy = customerID
			userPromotion.VersionNo = newVersionNo

			result := model.UserPromotionDB.UpdateOne(&model.UserPromotion{
				ID:        userPromotion.ID,
				VersionNo: currentVersionNo,
			}, userPromotion)

			if result.Status != common.APIStatus.Ok {
				return result
			}

		} else {
			result := model.UserPromotionDB.Create(&model.UserPromotion{
				PromotionID:      voucher.PromotionID,
				VoucherCode:      voucher.Code,
				CustomerID:       customerID,
				OrderIDs:         &[]int64{orderID},
				Status:           &enum.CodeStatus.USED,
				CreatedTime:      &now,
				UsedTime:         &now,
				CreatedBy:        customerID,
				VersionNo:        newVersionNo,
				Amount:           utils.ParseInt64ToPointer(1),
				TotalRewardCount: applyVoucherCount[voucher.Code],
				GiftCount: func() map[string]int {
					m := make(map[string]int)
					gifts := make([]model.Gift, 0)
					if voucher.Rewards != nil && len(voucher.Rewards) > 0 {
						reward := voucher.Rewards[0]
						if reward.Type != nil && *reward.Type == enum.RewardType.GIFT {
							gifts = reward.Gifts
						}
						for _, gift := range gifts {
							m[gift.Sku] = int(gift.Quantity) * applyVoucherCount[voucher.Code]
						}
					}
					return m
				}(),
			})

			if result.Status != common.APIStatus.Ok {
				return result
			}
		}
		// update voucher status
		go func(voucher *model.Voucher) {
			updater := model.Voucher{
				UsageTotal: func() *int64 {
					if voucher.UsageTotal != nil {
						return utils.ParseInt64ToPointer(*voucher.UsageTotal + 1)
					}
					return utils.ParseInt64ToPointer(1)
				}(),
			}
			if voucher.MaxUsage != nil && *voucher.MaxUsage > 0 && *updater.UsageTotal == *voucher.MaxUsage {
				updater.Status = &enum.VoucherStatus.HIDE
			}
			if voucher.UsageTotal == nil || *voucher.UsageTotal == 0 {
				updater.IsUsed = utils.ParseBoolToPointer(true)
			}
			model.VoucherDB.UpdateOne(model.Voucher{Code: voucher.Code}, updater)
			WarmUpVoucherByCode(voucher.Code, nil, "UseVoucher")
			WarmUpUserPromotion(voucher.Code, customerID, nil)
			createVoucherHistory(&model.VoucherHistory{
				CustomerID: customerID,
				OrderID:    orderID,
				Usage:      1,
				Type:       enum.VoucherHistoryType.USE,
				Voucher:    voucher,
				Promotion:  nil,
			}, 0, 0, voucher.Code)
		}(voucher)
	}

	return qVoucher
}

// GetListUserVoucher is func to get list user voucher
func GetListUserVoucher(query *model.UserPromotion, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.UserPromotionDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.UserPromotionDB.Count(query)
		result.Total = countResult.Total
	}
	return result
}

// CreateUserVoucher is func to add customer to voucher
func CreateUserVoucher(acc *model.Account, userVoucher *model.UserPromotion) *common.APIResponse {
	qVoucher := model.VoucherDB.QueryOne(model.Voucher{
		Code: userVoucher.VoucherCode,
	})
	if qVoucher.Status != common.APIStatus.Ok {
		return qVoucher
	}
	defer func() {
		go func() {
			WarmUpVoucherByCode(userVoucher.VoucherCode, nil, "")
			WarmUpUserPromotion(userVoucher.VoucherCode, 0, nil)
		}()
	}()
	voucher := qVoucher.Data.([]*model.Voucher)[0]

	if userVoucher.Status == nil {
		userVoucher.Status = &enum.CodeStatus.ACTIVE
	}
	if len(userVoucher.CustomerIDs) > 0 {
		query := model.UserPromotion{
			VoucherCode: voucher.Code,
			ComplexQuery: []*bson.M{
				{
					"customer_id": bson.M{"$in": userVoucher.CustomerIDs},
				},
			},
		}

		existedCustomerIds := make([]int64, 0)
		qUserVoucher := model.UserPromotionDB.Query(query, 0, 0, nil)
		if qUserVoucher.Status == common.APIStatus.Ok {
			userVoucherList := qUserVoucher.Data.([]*model.UserPromotion)
			invalidUsers := make([]*model.UserPromotion, 0)
			ids := make([]int64, 0)
			usedList := make([]int64, 0)

			for _, userVoucherItem := range userVoucherList {
				existedCustomerIds = append(existedCustomerIds, userVoucherItem.CustomerID)
				if userVoucherItem.Status != nil && userVoucher.Status != nil {
					if *userVoucherItem.Status == *userVoucher.Status || (*userVoucherItem.Status == enum.CodeStatus.USED && *userVoucher.Status == enum.CodeStatus.ACTIVE) {
						invalidUsers = append(invalidUsers, userVoucherItem)
						continue
					}

					if *userVoucher.Status == enum.CodeStatus.ACTIVE &&
						(*userVoucherItem.Status == enum.CodeStatus.DELETED || *userVoucherItem.Status == enum.CodeStatus.INACTIVE) && userVoucherItem.Amount != nil && *userVoucherItem.Amount > 0 {
						usedList = append(usedList, userVoucherItem.CustomerID)
						continue
					}
				}
				ids = append(ids, userVoucherItem.CustomerID)
			}

			if len(invalidUsers) > 0 {
				return &common.APIResponse{
					Status:    common.APIStatus.Existed,
					Data:      invalidUsers,
					Message:   "Customer added voucher",
					ErrorCode: "EXISTED",
				}
			}

			if len(usedList) > 0 {
				_ = model.UserPromotionDB.UpdateMany(&model.UserPromotion{
					VoucherCode: voucher.Code,
					ComplexQuery: []*bson.M{
						{
							"customer_id": bson.M{"$in": usedList},
						},
					},
				}, &model.UserPromotion{
					Status: &enum.CodeStatus.USED,
				})
			}

			if len(ids) > 0 {
				_ = model.UserPromotionDB.UpdateMany(&model.UserPromotion{
					VoucherCode: voucher.Code,
					ComplexQuery: []*bson.M{
						{
							"customer_id": bson.M{"$in": ids},
						},
					},
				}, &model.UserPromotion{
					Status: userVoucher.Status,
				})
			}

			if len(existedCustomerIds) == len(userVoucher.CustomerIDs) {
				return &common.APIResponse{
					Status:  common.APIStatus.Ok,
					Message: "Ok",
				}
			}
		}

		userVouchers := make([]*model.UserPromotion, 0)
		for _, customerID := range userVoucher.CustomerIDs {
			isExisted := false
			for _, existedID := range existedCustomerIds {
				if existedID == customerID {
					isExisted = true
					break
				}
			}

			if !isExisted {
				userVouchers = append(userVouchers, &model.UserPromotion{
					CreatedBy:   acc.AccountID,
					CustomerID:  customerID,
					PromotionID: voucher.PromotionID,
					VoucherCode: voucher.Code,
					Status:      userVoucher.Status,
				})
			}
		}
		resp := model.UserPromotionDB.CreateMany(userVouchers)
		return resp
	} else if userVoucher.CustomerID != 0 {
		query := model.UserPromotion{
			VoucherCode: voucher.Code,
			CustomerID:  userVoucher.CustomerID,
		}
		qUserVoucher := model.UserPromotionDB.Query(query, 0, 0, nil)
		if qUserVoucher.Status == common.APIStatus.Ok {
			existedUser := qUserVoucher.Data.([]*model.UserPromotion)[0]
			if existedUser.Status != nil && userVoucher.Status != nil {
				if *existedUser.Status == *userVoucher.Status || (*existedUser.Status == enum.CodeStatus.USED && *userVoucher.Status == enum.CodeStatus.ACTIVE) {
					return &common.APIResponse{
						Status:    common.APIStatus.Existed,
						Data:      qUserVoucher.Data,
						Message:   "Customer added voucher",
						ErrorCode: "EXISTED",
					}
				}

				if *userVoucher.Status == enum.CodeStatus.ACTIVE &&
					(*existedUser.Status == enum.CodeStatus.DELETED || *existedUser.Status == enum.CodeStatus.INACTIVE) && existedUser.Amount != nil && *existedUser.Amount > 0 {
					return model.UserPromotionDB.UpdateOne(query, &model.UserPromotion{
						Status: &enum.CodeStatus.USED,
					})
				}

				return model.UserPromotionDB.UpdateOne(query, &model.UserPromotion{
					Status: userVoucher.Status,
				})
			}
		}
		resp := model.UserPromotionDB.Create(userVoucher)
		if resp.Status == common.APIStatus.Ok {

		}
		return resp
	} else {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing customer value",
			ErrorCode: "MISSING_CUSTOMER_VALUE",
		}
	}
}

// UpdateUserVoucher is func to update status user voucher
func UpdateUserVoucher(acc *model.Account, userVoucher *model.UserPromotion) *common.APIResponse {
	qVoucher := model.VoucherDB.QueryOne(model.Voucher{
		Code: userVoucher.VoucherCode,
	})
	if qVoucher.Status != common.APIStatus.Ok {
		return qVoucher
	}
	defer func() {
		go WarmUpVoucherByCode(userVoucher.VoucherCode, nil, "")
		go WarmUpUserPromotion(userVoucher.VoucherCode, 0, nil)
	}()
	voucher := qVoucher.Data.([]*model.Voucher)[0]

	if userVoucher.IsUpdateAll && userVoucher.PrevStatus != nil {
		go sdk.Execute(func() {
			var offset, limit int64 = 0, 200
			var _id primitive.ObjectID
			customerIds := []int64{}

			// Query all user voucher
			for {
				query := model.UserPromotion{
					VoucherCode: voucher.Code,
					Status:      userVoucher.PrevStatus,
				}

				if *userVoucher.PrevStatus == enum.CodeStatus.ACTIVE {
					query.ComplexQuery = []*bson.M{
						{
							"status": bson.M{"$in": []string{string(enum.CodeStatus.ACTIVE), string(enum.CodeStatus.USED)}},
						},
					}
					query.Status = nil
				}

				query.ComplexQuery = append(
					query.ComplexQuery,
					&primitive.M{
						"_id": bson.M{
							"$gt": _id,
						},
					},
				)

				userVoucherResp := model.UserPromotionDB.Query(query, offset, limit, &primitive.M{"_id": 1})

				if userVoucherResp.Status != common.APIStatus.Ok {
					break
				}

				for _, userVoucher := range userVoucherResp.Data.([]*model.UserPromotion) {
					customerIds = append(customerIds, userVoucher.CustomerID)
					_id = userVoucher.ID
				}
			}

			// Push job update user voucher
			if len(customerIds) > 0 {
				for _, customerID := range customerIds {
					errPushJob := model.UserForVoucherJob.Push(&model.UserVoucherRequest{
						CustomerID:  customerID,
						VoucherCode: userVoucher.VoucherCode,
						Status:      userVoucher.Status,
						PrevStatus:  userVoucher.PrevStatus,
						AccountID:   acc.AccountID,
						TypeAction:  "UPDATE",
					}, &job.JobItemMetadata{
						Topic: "default",
						Keys:  []string{"USER_VOUCHER_UPDATE", userVoucher.VoucherCode, fmt.Sprintf("%d", customerID)},
					})

					if errPushJob != nil {
						fmt.Println("Error push job update user voucher, ", errPushJob.Error())
					}
				}

			}
		})

		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Update user_promotion is processing",
		}
	}
	if len(userVoucher.CustomerIDs) > 0 {
		for _, customerID := range userVoucher.CustomerIDs {
			errPushJob := model.UserForVoucherJob.Push(&model.UserVoucherRequest{
				CustomerID:  customerID,
				VoucherCode: userVoucher.VoucherCode,
				Status:      userVoucher.Status,
				PrevStatus:  userVoucher.PrevStatus,
				AccountID:   acc.AccountID,
				TypeAction:  "UPDATE",
			}, &job.JobItemMetadata{
				Topic: "default",
				Keys:  []string{"USER_VOUCHER_UPDATE", userVoucher.VoucherCode, fmt.Sprintf("%d", customerID)},
			})

			if errPushJob != nil {
				fmt.Println("Error push job update user voucher, ", errPushJob.Error())
			}
		}

		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Update user_promotion is processing",
		}
	} else {
		return model.UserPromotionDB.UpdateOne(
			model.UserPromotion{
				VoucherCode: voucher.Code,
				CustomerID:  userVoucher.CustomerID,
			},
			userVoucher,
		)
	}
}

func DeleteUserPromotionByVoucherCode(voucherCode string) *common.APIResponse {
	if voucherCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing voucher code",
			ErrorCode: "MISSING_VOUCHER_CODE",
		}
	}
	return model.UserPromotionDB.Delete(model.UserPromotion{VoucherCode: voucherCode})
}

func CreateUserVoucherPromotion(acc *model.Account, userVoucher *model.UserPromotion) *common.APIResponse {
	if userVoucher == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing user voucher promotion",
			ErrorCode: "MISSING_USER_VOUCHER_PROMOTION",
		}
	}

	if userVoucher.VoucherCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing voucher code",
			ErrorCode: "MISSING_VOUCHER_CODE",
		}
	}

	if userVoucher.Status == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing status value",
			ErrorCode: "MISSING_STATUS_VALUE",
		}
	}

	if len(userVoucher.CustomerIDs) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing customerIDs value",
		}
	}

	if len(userVoucher.CustomerIDs) <= 20 {
		resp := CreateUserVoucher(acc, userVoucher)
		return resp
	}

	go sdk.Execute(func() {
		for _, customerID := range userVoucher.CustomerIDs {
			errPushJob := model.UserForVoucherJob.Push(&model.UserVoucherRequest{
				CustomerID:  customerID,
				VoucherCode: userVoucher.VoucherCode,
				Status:      userVoucher.Status,
				AccountID:   acc.AccountID,
				TypeAction:  "CREATE",
			}, &job.JobItemMetadata{
				Topic: "default",
				Keys:  []string{"USER_VOUCHER_CREATE", userVoucher.VoucherCode, fmt.Sprintf("%d", customerID)},
			})

			if errPushJob != nil {
				fmt.Println("Error push job create user voucher, ", errPushJob.Error())
			}
		}
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Create user voucher promotion is processing",
	}
}

// UpdatePersonalizationRuleUserVoucher is func to add customer to voucher
func UpdatePersonalizationRuleUserVoucher(acc *model.Account, userVoucher *model.UserPromotion) *common.APIResponse {
	if len(userVoucher.CustomerIDs) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing customer value",
		}
	}

	// need to clear wrong data before handling
	clearWrongData(userVoucher)

	oldCustomerIDs := make(map[int64]*model.UserPromotion)
	_id_offset := primitive.NilObjectID
	limit := int64(100)
	for {
		// query user voucher
		query := model.UserPromotion{
			VoucherCode: userVoucher.VoucherCode,
			ComplexQuery: []*bson.M{
				{"_id": bson.M{"$gt": _id_offset}},
			},
		}

		userPromotionResp := model.UserPromotionDB.Query(query, 0, limit, &bson.M{"_id": 1})
		if userPromotionResp.Status != common.APIStatus.Ok {
			break
		}

		userPromotions := userPromotionResp.Data.([]*model.UserPromotion)
		if len(userPromotions) == 0 {
			break
		}

		for _, userPromotion := range userPromotions {
			_id_offset = userPromotion.ID
			if userPromotion.Status == nil {
				continue
			}

			oldCustomerIDs[userPromotion.CustomerID] = userPromotion
		}
	}

	newCustomerIDs := make([]int64, 0)
	inputCustomerIDs := make(map[int64]struct{})
	for _, customerID := range userVoucher.CustomerIDs {
		if _, ok := oldCustomerIDs[customerID]; !ok {
			newCustomerIDs = append(newCustomerIDs, customerID)
		}
		inputCustomerIDs[customerID] = struct{}{}
	}

	// filter customerIDs
	dupCustomerIDs := make(map[int64]*model.UserPromotion)
	restCustomerIDs := make(map[int64]*model.UserPromotion)
	for customerID, userPromo := range oldCustomerIDs {
		if _, ok := inputCustomerIDs[customerID]; ok {
			dupCustomerIDs[customerID] = userPromo
		} else {
			restCustomerIDs[customerID] = userPromo
		}
	}

	if len(dupCustomerIDs) > 0 {
		customerIDs := make([]int, 0)
		for customerID, userPromo := range dupCustomerIDs {
			if *userPromo.Status == enum.CodeStatus.DELETED && userPromo.Amount != nil && *userPromo.Amount > 0 {
				customerIDs = append(customerIDs, int(customerID))
			}
		}
		if len(customerIDs) > 0 {
			queryQ := model.UserPromotion{
				VoucherCode: userVoucher.VoucherCode,
				ComplexQuery: []*bson.M{
					{"customer_id": bson.M{"$in": customerIDs}},
				},
			}
			res := model.UserPromotionDB.UpdateMany(queryQ, &model.UserPromotion{
				Status: &enum.CodeStatus.USED,
			})
			if res.Status != common.APIStatus.Ok {
				fmt.Printf("Error UpdatePersonalizationRuleUserVoucher > UserPromotionDB.UpdateMany dup: %s. res=%+v\n", utils.PrintValue(queryQ), res)
			}
		}
	}

	if len(restCustomerIDs) > 0 {
		activedCustomerIDs := make([]int64, 0)
		usedCustomerIDs := make([]int64, 0)
		for customerID, userPromo := range restCustomerIDs {
			if *userPromo.Status == enum.CodeStatus.ACTIVE {
				activedCustomerIDs = append(activedCustomerIDs, customerID)
			}
			if *userPromo.Status == enum.CodeStatus.USED {
				usedCustomerIDs = append(usedCustomerIDs, customerID)
			}
		}

		if len(activedCustomerIDs) > 0 {
			// Process in batches of 20
			batchSize := 20
			for i := 0; i < len(activedCustomerIDs); i += batchSize {
				end := i + batchSize
				if end > len(activedCustomerIDs) {
					end = len(activedCustomerIDs)
				}

				batchCustomerIDs := activedCustomerIDs[i:end]

				queryQ := model.UserPromotion{
					VoucherCode: userVoucher.VoucherCode,
					ComplexQuery: []*bson.M{
						{"customer_id": bson.M{"$in": batchCustomerIDs}},
					},
				}

				res := model.UserPromotionDB.Delete(queryQ)
				if res.Status != common.APIStatus.Ok {
					fmt.Printf("Error UpdatePersonalizationRuleUserVoucher > UserPromotionDB.Delete active: %s. res=%+v\n", utils.PrintValue(queryQ), res)
				}

				res = model.UserPromotionCacheDB.Delete(queryQ)
				if res.Status != common.APIStatus.Ok {
					fmt.Printf("Error UpdatePersonalizationRuleUserVoucher > UserPromotionCacheDB.Delete active: %s. res=%+v\n", utils.PrintValue(queryQ), res)
				}
			}
		}

		if len(usedCustomerIDs) > 0 {
			queryQ := model.UserPromotion{
				VoucherCode: userVoucher.VoucherCode,
				ComplexQuery: []*bson.M{
					{"customer_id": bson.M{"$in": usedCustomerIDs}},
				},
			}
			res := model.UserPromotionDB.UpdateMany(queryQ, &model.UserPromotion{
				Status: &enum.CodeStatus.DELETED,
			})
			if res.Status != common.APIStatus.Ok {
				fmt.Printf("Error UpdatePersonalizationRuleUserVoucher > UserPromotionDB.UpdateMany used: %s. res=%+v\n", utils.PrintValue(queryQ), res)
			}
		}
	}

	if userVoucher.Status == nil {
		userVoucher.Status = &enum.CodeStatus.ACTIVE
	}

	// create new user promotion
	if len(newCustomerIDs) > 0 {
		qVoucher := model.VoucherDB.QueryOne(model.Voucher{Code: userVoucher.VoucherCode})
		if qVoucher.Status != common.APIStatus.Ok {
			return qVoucher
		}
		voucher := qVoucher.Data.([]*model.Voucher)[0]

		for _, customerID := range newCustomerIDs {
			userVoucher := &model.UserPromotion{
				CreatedBy:   acc.AccountID,
				CustomerID:  customerID,
				PromotionID: voucher.PromotionID,
				VoucherCode: voucher.Code,
				Status:      userVoucher.Status,
			}
			res := model.UserPromotionDB.Create(userVoucher)
			if res.Status != common.APIStatus.Ok {
				fmt.Printf("Error UpdatePersonalizationRuleUserVoucher > UserPromotionDB.Create: %s. res=%+v\n", utils.PrintValue(userVoucher), res)
			}
		}
	}

	go WarmUpVoucherByCode(userVoucher.VoucherCode, nil, "")
	go WarmUpUserPromotion(userVoucher.VoucherCode, 0, nil)

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}

func clearWrongData(userVoucher *model.UserPromotion) {
	if userVoucher == nil {
		return
	}

	lastID := primitive.NilObjectID
	limit := int64(100)
	wrongCustomerIDs := make([]int64, 0)
	for {
		// query user voucher
		query := model.UserPromotion{
			VoucherCode: userVoucher.VoucherCode,
			ComplexQuery: []*bson.M{
				{"_id": bson.M{"$gt": lastID}},
			},
		}

		userPromotionResp := model.UserPromotionDB.Query(query, 0, limit, &bson.M{"_id": 1})
		if userPromotionResp.Status != common.APIStatus.Ok {
			break
		}

		userPromotions := userPromotionResp.Data.([]*model.UserPromotion)
		if len(userPromotions) == 0 {
			break
		}

		for _, userPromotion := range userPromotions {
			lastID = userPromotion.ID
			if userPromotion.Status == nil {
				continue
			}
			if *userPromotion.Status == enum.CodeStatus.DELETED && (userPromotion.Amount == nil || *userPromotion.Amount == 0) {
				wrongCustomerIDs = append(wrongCustomerIDs, userPromotion.CustomerID)
			}
		}
	}

	// delete wrong data
	if len(wrongCustomerIDs) > 0 {

		// REF_5 - Process in batches of 20
		batchSize := 20
		for i := 0; i < len(wrongCustomerIDs); i += batchSize {
			end := i + batchSize
			if end > len(wrongCustomerIDs) {
				end = len(wrongCustomerIDs)
			}

			batchCustomerIDs := wrongCustomerIDs[i:end]

			queryQ := model.UserPromotion{
				VoucherCode: userVoucher.VoucherCode,
				ComplexQuery: []*bson.M{
					{"customer_id": bson.M{"$in": batchCustomerIDs}},
				},
			}
			res := model.UserPromotionDB.Delete(queryQ)
			if res.Status != common.APIStatus.Ok {
				fmt.Printf("Error clearWrongData > UserPromotionDB.Delete: %s. res=%+v\n", utils.PrintValue(queryQ), res)
			}

			res = model.UserPromotionCacheDB.Delete(queryQ)
			if res.Status != common.APIStatus.Ok {
				fmt.Printf("Error clearWrongData > UserPromotionCacheDB.Delete: %s. res=%+v\n", utils.PrintValue(queryQ), res)
			}
		}
	}
}
