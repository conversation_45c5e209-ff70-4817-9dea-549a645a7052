package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// CreateLuckyWheelNotification is a function to create a new lucky wheel notification
func CreateLuckyWheelNotification(acc *model.Account, payload *model.LuckyWheelNotification) *common.APIResponse {
	payload.Code = model.GenCodeWithTime()
	payload.CreatedBy = acc.AccountID

	return model.LuckyWheelNotificationDB.Create(payload)
}

// UpdateLuckyWheelNotification is a function to update a lucky wheel notification
func UpdateLuckyWheelNotification(acc *model.Account, payload *model.LuckyWheelNotification) *common.APIResponse {
	// validate payload
	if payload.Code == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Code is required",
			ErrorCode: "CODE_MISSING",
		}
	}
	// check exist
	if qCheck := model.LuckyWheelNotificationDB.QueryOne(model.LuckyWheelNotification{Code: payload.Code}); qCheck.Status != common.APIStatus.Ok {
		return qCheck
	}
	return model.LuckyWheelNotificationDB.UpdateOne(model.LuckyWheelNotification{Code: payload.Code}, payload)
}

// GetLuckyWheelNotification is a function to get a lucky wheel notification
func GetLuckyWheelNotification(acc *model.Account, code string) *common.APIResponse {
	return model.LuckyWheelNotificationDB.QueryOne(model.LuckyWheelNotification{Code: code})
}

// GetLuckyWheelNotificationList is a function to get list lucky wheel notification
func GetLuckyWheelNotificationList(acc *model.Account, filter *model.LuckyWheelNotification, offset, limit int64, getTotal bool) *common.APIResponse {
	res := model.LuckyWheelNotificationDB.Query(filter, offset, limit, &primitive.M{"_id": -1})
	if res.Status != common.APIStatus.Ok {
		return res
	}
	if getTotal {
		res.Total = model.LuckyWheelNotificationDB.Count(filter).Total
	}
	return res
}

// GetSelfLuckyWheelNotificationList is a function to get list lucky wheel notification
func GetSelfLuckyWheelNotificationList(acc *model.Account, luckyWheelCode string) *common.APIResponse {
	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}
	now := time.Now()
	query := bson.M{
		"lucky_wheel_code": luckyWheelCode,
		"is_active":        true,
		"start_time": bson.M{
			"$lte": now,
		},
		"end_time": bson.M{
			"$gte": now,
		},
		"scope.province_codes": bson.M{"$in": []string{customer.ProvinceCode, "all"}},
	}
	qNotifications := model.LuckyWheelNotificationDB.Query(query, 0, 0, &primitive.M{"_id": -1})
	if qNotifications.Status != common.APIStatus.Ok {
		return qNotifications
	}
	notifications := qNotifications.Data.([]*model.LuckyWheelNotification)
	finalNotifications := make([]*model.LuckyWheelNotification, 0)
	for _, lk := range notifications {
		isMatchLevel := false
		isMatchScope := false
		//isMatchCustomer := false
		//if lk.Scope == nil || lk.Scope.CustomerApplyType == "ALL" || lk.Scope.CustomerApplyType == "" {
		//	isMatchCustomer = true
		//} else {
		//	isMatchCustomer = customerMap[lk.Code] != nil
		//}
		for _, level := range lk.Scope.CustomerLevels {
			if level == "all" || level == customer.Level {
				isMatchLevel = true
			}
		}
		for _, scope := range lk.Scope.CustomerScopes {
			if scope == "all" || scope == customer.Scope {
				isMatchScope = true
			}
		}
		if isMatchLevel && isMatchScope {
			finalNotifications = append(finalNotifications, lk)
		}
	}
	if len(finalNotifications) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Data:    finalNotifications,
			Message: "Get lucky wheel notification success",
		}
	}
	return &common.APIResponse{
		Status:    common.APIStatus.NotFound,
		Message:   "Not found lucky wheel notification",
		ErrorCode: "NOT_FOUND",
	}
}
