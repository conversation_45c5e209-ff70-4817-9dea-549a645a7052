package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// ListvoucherCodesRequest
type ListVoucherCodesRequest struct {
	VoucherCodes []string `json:"voucherCodes" validate:"required"`
}

type PromotionRefundRequest struct {
	VoucherCode       string         `json:"voucherCode" validate:"required"`
	VoucherCodes      []string       `json:"voucherCodes" validate:"required"`
	AccountID         int64          `json:"accountId" validate:"required"`
	OrderID           int64          `json:"orderId"`
	ApplyVoucherCount map[string]int `json:"applyVoucherCount"`
}

type PromotionCopyRequest struct {
	PromotionId int64 `json:"promotionId,omitempty" bson:"promotion_id,omitempty" validate:"required"`
}

// CampaignCreateRequest ...
type CampaignCreateRequest struct {
	Banner                string                     `json:"banner,omitempty" validate:"required"`
	CampaignName          string                     `json:"campaignName,omitempty" validate:"required"`
	Description           string                     `json:"description,omitempty" validate:"-"`
	CampaignType          enum.CampaignValueType     `json:"campaignType,omitempty" validate:"required"`
	RegistrationStartTime time.Time                  `json:"registrationStartTime,omitempty" validate:"required"`
	RegistrationEndTime   time.Time                  `json:"registrationEndTime,omitempty" validate:"required"`
	StartTime             time.Time                  `json:"startTime,omitempty" validate:"required"`
	EndTime               time.Time                  `json:"endTime,omitempty" validate:"required"`
	FlashSaleTimes        []*FlashSaleTime           `json:"flashSaleTimes,omitempty" validate:"-"`
	Reward                *CampaignReward            `json:"reward,omitempty" validate:"required"`
	SellerCodes           *[]string                  `json:"sellerCodes,omitempty"`
	CustomerScopes        *[]string                  `json:"customerScopes,omitempty"`
	Regions               *[]string                  `json:"regions,omitempty"`
	SaleType              enum.CampaignSaleValueType `json:"saleType,omitempty" validate:"required"`
	Fulfill               *float64                   `json:"fulfill,omitempty" validate:"required"`
	DisplayPriority       *int                       `json:"displayPriority,omitempty" bson:"display_priority,omitempty"`
	ChildCampaignCodes    *[]string                  `json:"childCampaignCodes,omitempty" bson:"child_campaign_codes,omitempty"`
	ViewType              *string                    `json:"viewType,omitempty" bson:"view_type,omitempty"`
	SystemDisplay         string                     `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
	ShortName             *string                    `json:"shortName,omitempty" bson:"short_name,omitempty"`
	DaysOfNewProduct      *int64                     `json:"daysOfNewProduct,omitempty" bson:"days_of_new_product,omitempty"`
	SubsidyType           string                     `json:"subsidyType,omitempty"`
	SubsidyValue          int64                      `json:"subsidyValue,omitempty"`
	Status                enum.CampaignStatusValue   `json:"status,omitempty" bson:"status,omitempty"`
} // @name CampaignCreateRequest

// CampaignUpdateRequest ...
type CampaignUpdateRequest struct {
	CampaignID            int64                        `json:"campaignID" bson:"-"`
	CampaignName          string                       `json:"campaignName,omitempty" bson:"campaign_name,omitempty"`
	Slug                  string                       `json:"slug,omitempty" bson:"slug,omitempty"`
	RegistrationStartTime time.Time                    `json:"registrationStartTime,omitempty" bson:"registration_start_time,omitempty"`
	RegistrationEndTime   time.Time                    `json:"registrationEndTime,omitempty" bson:"registration_end_time,omitempty"`
	StartTime             time.Time                    `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime               time.Time                    `json:"endTime,omitempty" bson:"end_time,omitempty"`
	FlashSaleTimes        []*FlashSaleTime             `json:"flashSaleTimes,omitempty" bson:"flash_sale_times,omitempty"`
	FlashSaleTimesView    []*CampaignFlashSaleTimeItem `json:"-" bson:"-"`
	Reward                *CampaignReward              `json:"reward,omitempty" bson:"reward,omitempty"`
	SellerCodes           *[]string                    `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	CustomerScopes        *[]string                    `json:"customerScopes,omitempty" bson:"customer_scopes,omitempty"`
	Regions               *[]string                    `json:"regions,omitempty" bson:"regions,omitempty"`
	Banner                *string                      `json:"banner,omitempty" bson:"banner,omitempty"`
	Description           *string                      `json:"description,omitempty" bson:"description,omitempty"`
	IsActive              *bool                        `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Fulfill               *float64                     `json:"fulfill,omitempty" bson:"fulfill,omitempty"`
	SaleType              enum.CampaignSaleValueType   `json:"saleType,omitempty" bson:"sale_type,omitempty"`
	HashTag               string                       `json:"-" bson:"hash_tag,omitempty"`
	NeedCheck             string                       `json:"-" bson:"need_check_sync,omitempty"`
	ChildCampaignCodes    *[]string                    `json:"childCampaignCodes,omitempty" bson:"child_campaign_codes,omitempty"`
	ViewType              *string                      `json:"viewType,omitempty" bson:"view_type,omitempty"`
	DisplayPriority       *int                         `json:"displayPriority,omitempty" bson:"display_priority,omitempty"`
	Status                enum.CampaignStatusValue     `json:"-" bson:"status,omitempty"`
	SystemDisplay         string                       `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
	ShortName             *string                      `json:"shortName,omitempty" bson:"short_name,omitempty"`
	DaysOfNewProduct      *int64                       `json:"daysOfNewProduct,omitempty" bson:"days_of_new_product,omitempty"`
	SubsidyType           string                       `json:"subsidyType,omitempty" bson:"subsidy_type,omitempty"`
	SubsidyValue          int64                        `json:"subsidyValue,omitempty" bson:"subsidy_value,omitempty"`
} // @name CampaignUpdateRequest

// TicketUpdateStatusRequest ...
type TicketUpdateStatusRequest struct {
	TicketID   int64                 `json:"ticketID" validate:"required"`
	SellerCode string                `json:"sellerCode" validate:"required"`
	Status     enum.TicketStateValue `json:"status" validate:"required"`
	Note       string                `json:"note"`
	IsActive   *bool                 `json:"isActive,omitempty" bson:"is_active,omitempty"`
} // @name TicketUpdateStatusRequest

type UpdateMultiTicketStatusRequest struct {
	Data   []*TicketUpdateStatusRequest `json:"data,omitempty" bson:"data,omitempty"`
	Source string                       `json:"source,omitempty" bson:"source,omitempty"`
}

type CreateMultiTicketRequest struct {
	Data []*SellerCampaignCreateRequest `json:"data,omitempty" bson:"data,omitempty"`
}

// CampaignActiveRequest ...
type CampaignActiveRequest struct {
	CampaignID            int64                  `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	CampaignCode          string                 `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	CampaignName          string                 `json:"campaignName,omitempty" bson:"campaign_name,omitempty" validate:"-"`
	CampaignType          enum.CampaignValueType `json:"campaignType,omitempty" bson:"campaign_type,omitempty" validate:"-"`
	RegistrationStartTime *time.Time             `json:"registrationStartTime,omitempty" bson:"-" validate:"-"`
	RegistrationEndTime   *time.Time             `json:"registrationEndTime,omitempty" bson:"-" validate:"-"`
	StartTime             *time.Time             `json:"startTime,omitempty" bson:"-" validate:"-"`
	EndTime               *time.Time             `json:"endTime,omitempty" bson:"-" validate:"-"`
	Status                string                 `json:"status,omitempty" bson:"status,omitempty"`
	SellerCodes           string                 `json:"-" bson:"seller_codes,omitempty"`
	IsActive              *bool                  `json:"isActive,omitempty" bson:"is_active,omitempty"`
	ComplexQuery          []*bson.M              `json:"-" bson:"$and,omitempty"`
	ComplexOrQuery        []*bson.M              `json:"-" bson:"$or,omitempty"`
	IsJoined              bool                   `json:"isJoined,omitempty" bson:"-"`

	SystemDisplay string `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
} // @name CampaignActiveRequest

// SellerCampaignCreateRequest ...
type SellerCampaignCreateRequest struct {
	CampaignID          int64                      `json:"campaignID" validate:"gt=0"`
	ProductID           int64                      `json:"productID,omitempty" validate:"required"`
	ProductCode         string                     `json:"productCode,omitempty" validate:"required"`
	Sku                 string                     `json:"sku" validate:"required"`
	SellerCode          string                     `json:"sellerCode,omitempty" validate:"required"`
	Price               int64                      `json:"price"` // remove validate for add prd sale camp on deal ticket
	PercentageDiscount  int64                      `json:"percentageDiscount" validate:"gte=0"`
	AbsoluteDiscount    int64                      `json:"absoluteDiscount" validate:"gte=0"`
	Quantity            int64                      `json:"quantity" validate:"required,gt=0"`
	MaxQuantityPerOrder int64                      `json:"maxQuantityPerOrder" validate:"gte=0"` // if zero, maxQuantityPerOrder default is 100,000
	MaxDiscount         int64                      `json:"maxDiscount,omitempty" validate:"gte=0"`
	FlashSaleTimes      []string                   `json:"flashSaleTimes"`     // ex: 9HUJABQ7_1, 9HUJABQ7_2
	SaleType            enum.CampaignSaleValueType `json:"saleType,omitempty"` // ABSOLUTE or PERCENTAGE
	TicketID            *int64                     `json:"ticketID,omitempty"`
	Source              string                     `json:"source,omitempty"`
	CampaignPrice       int64                      `json:"campaignPrice" validate:"gte=0"`

	MaxQuantityPerCustomer *int64 `json:"maxQuantityPerCustomer,omitempty" bson:"max_quantity_per_customer,omitempty" validate:"omitempty,gte=0,lte=1000000000"`
	IsActive               *bool  `json:"isActive,omitempty" bson:"is_active,omitempty"`
} // @name SellerCampaignCreateRequest

// SellerTicketUpdateRequest ...
type SellerTicketUpdateRequest struct {
	TicketID            int64                      `json:"ticketID,omitempty" bson:"-" validate:"gte=0"`
	SellerCode          string                     `json:"sellerCode,omitempty" bson:"-" validate:"required"`
	Price               int64                      `json:"price,omitempty" bson:"price,omitempty"`
	DealPrice           int64                      `json:"dealPrice,omitempty" bson:"deal_price,omitempty"`
	PercentageDiscount  *int64                     `json:"percentageDiscount,omitempty" bson:"percentage_discount,omitempty"`
	AbsoluteDiscount    *int64                     `json:"absoluteDiscount,omitempty" bson:"absolute_discount,omitempty"`
	Quantity            int64                      `json:"quantity,omitempty" bson:"quantity,omitempty"`
	MaxQuantityPerOrder *int64                     `json:"maxQuantityPerOrder,omitempty" bson:"max_quantity_per_order,omitempty"`
	Status              enum.TicketStateValue      `json:"status,omitempty" bson:"status,omitempty"`
	UpdatedBy           int64                      `json:"-" bson:"updated_by,omitempty"`
	CampaignPrice       *int64                     `json:"campaignPrice,omitempty" bson:"campaign_price,omitempty"`
	FlashSaleTimes      *[]string                  `json:"flashSaleTimes,omitempty" bson:"flash_sale_times,omitempty"`
	SaleType            enum.CampaignSaleValueType `json:"saleType,omitempty" bson:"sale_type,omitempty"`

	MaxQuantityPerCustomer *int64 `json:"maxQuantityPerCustomer,omitempty" bson:"max_quantity_per_customer,omitempty" validate:"omitempty,gte=0,lte=1000000000"`
	IsActive               *bool  `json:"isActive,omitempty" bson:"is_active,omitempty"`
} // @name SellerTicketUpdateRequest

type CampaignSearchRequest struct {
	CampaignIDs   []int64  `json:"campaignIDs" validate:"-"`
	CampaignCodes []string `json:"campaignCodes" validate:"-"`
}

// UpdateSkuCampaignRequest ...
type UpdateSkuCampaignRequest struct {
	CampaignCode string                   `json:"campaignCode"`
	ListSku      []*UpdateSkuCampaignItem `json:"listSku"`
	Page         int64                    `json:"page"`
} // @name UpdateSkuCampaignRequest

// UpdateSkuCampaignItem ...
type UpdateSkuCampaignItem struct {
	ProductID    int64  `json:"productID"`
	Quantity     *int64 `json:"quantity"`
	Sku          string `json:"sku"`
	SellerCode   string `json:"sellerCode"`
	SoldQuantity *int64 `json:"soldQuantity"`
} // @name UpdateSkuCampaignItem

// CampaignProductCreateRequest ...
type CampaignProductCreateRequest struct {
	CreatedBy           int64                          `json:"-" bson:"created_by,omitempty"`
	CampaignID          int64                          `json:"campaignID,omitempty" bson:"campaign_id,omitempty" validate:"required"`
	CampaignCode        string                         `json:"campaignCode,omitempty" bson:"campaign_code,omitempty" validate:"required"`
	CampaignType        enum.CampaignValueType         `json:"-" bson:"campaign_type,omitempty"`
	CampaignProductID   int64                          `json:"-" bson:"campaign_product_id,omitempty"`
	CampaignProductCode string                         `json:"-" bson:"campaign_product_code,omitempty"`
	ProductID           int64                          `json:"-" bson:"product_id,omitempty"`
	ProductCode         string                         `json:"-" bson:"product_code,omitempty"`
	Sku                 string                         `json:"sku,omitempty" bson:"sku,omitempty" validate:"required"`
	SellerCode          string                         `json:"-" bson:"seller_code,omitempty"`
	Price               int64                          `json:"-" bson:"price,omitempty"`
	SalePrice           int64                          `json:"salePrice,omitempty" bson:"sale_price,omitempty" validate:"omitempty,min=1,max=1000000000"`
	AbsoluteDiscount    *int64                         `json:"absoluteDiscount,omitempty" bson:"absolute_discount,omitempty" validate:"omitempty,min=1,max=1000000000"`
	PercentageDiscount  int64                          `json:"percentageDiscount,omitempty" bson:"percentage_discount,omitempty" validate:"omitempty,min=0,max=100"`
	Quantity            int64                          `json:"quantity,omitempty" bson:"quantity,omitempty" validate:"required,min=1,max=1000000000"`
	SoldQuantity        int64                          `json:"soldQuantity,omitempty" bson:"sold_quantity,omitempty" validate:"omitempty,min=1,max=1000000000"`
	MaxQuantityPerOrder int64                          `json:"maxQuantityPerOrder,omitempty" bson:"max_quantity_per_order,omitempty" validate:"omitempty,min=1,max=1000000000"`
	FlashSaleTime       []string                       `json:"flashSaleTimeRefs,omitempty" bson:"flash_sale_time_refs,omitempty"`
	FlashSaleTimes      []*CampaignFlashSaleTimeItem   `json:"-" bson:"flash_sale_time,omitempty"`
	IsActive            bool                           `json:"isActive,omitempty" bson:"is_active,omitempty"`
	SaleType            enum.CampaignSaleValueType     `json:"saleType,omitempty" bson:"sale_type,omitempty" validate:"required,oneof=ABSOLUTE PERCENTAGE PRICE"`
	Status              enum.CampaignProductStatusType `json:"status,omitempty" bson:"status,omitempty"`
	CancelReason        *string                        `json:"cancelReason,omitempty" bson:"cancel_reason,omitempty"`
	HashTag             string                         `json:"-" bson:"hash_tag,omitempty"`
	UniqueSkuActive     *string                        `json:"-" bson:"unique_sku_active,omitempty"`
	CampaignPrice       *int64                         `json:"campaignPrice,omitempty" bson:"campaign_price,omitempty"`
	ChargeFee           string                         `json:"chargeFee,omitempty" bson:"charge_fee,omitempty"` // MARKETING . SELLER
	SystemDisplay       string                         `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
	Priority            *int                           `json:"priority,omitempty" bson:"priority,omitempty"` // sort

	MaxQuantityPerCustomer *int64 `json:"maxQuantityPerCustomer,omitempty" bson:"max_quantity_per_customer,omitempty" validate:"omitempty,gte=0,lte=1000000000"`
}

// CampaignProductUpdateRequest ...
type CampaignProductUpdateRequest struct {
	UpdatedBy           int64                          `json:"-" bson:"updated_by,omitempty"`
	CampaignID          int64                          `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	CampaignCode        string                         `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	CampaignProductID   int64                          `json:"campaignProductID,omitempty" bson:"campaign_product_id,omitempty"`
	CampaignProductCode string                         `json:"campaignProductCode,omitempty" bson:"campaign_product_code,omitempty"`
	ProductID           int64                          `json:"-" bson:"product_id,omitempty"`
	ProductCode         string                         `json:"-" bson:"product_code,omitempty"`
	Sku                 string                         `json:"sku,omitempty" bson:"sku,omitempty"`
	SellerCode          string                         `json:"-" bson:"seller_code,omitempty"`
	Price               int64                          `json:"-" bson:"price,omitempty"`
	SalePrice           *int64                         `json:"salePrice,omitempty" bson:"sale_price,omitempty" validate:"omitempty,min=1,max=1000000000"`
	AbsoluteDiscount    *int64                         `json:"absoluteDiscount,omitempty" bson:"absolute_discount,omitempty" validate:"omitempty,min=1,max=1000000000"`
	PercentageDiscount  *int64                         `json:"percentageDiscount,omitempty" bson:"percentage_discount,omitempty" validate:"omitempty,min=0,max=100"`
	Quantity            *int64                         `json:"quantity,omitempty" bson:"quantity,omitempty" validate:"omitempty,min=1,max=1000000000"`
	SoldQuantity        *int64                         `json:"soldQuantity,omitempty" bson:"sold_quantity,omitempty" validate:"omitempty,min=1,max=1000000000"`
	MaxQuantityPerOrder *int64                         `json:"maxQuantityPerOrder,omitempty" bson:"max_quantity_per_order,omitempty" validate:"omitempty,min=1,max=1000000000"`
	FlashSaleTime       *[]string                      `json:"flashSaleTimeRefs,omitempty" bson:"flash_sale_time_refs,omitempty"`
	IsActive            *bool                          `json:"isActive,omitempty" bson:"is_active,omitempty"`
	FlashSaleTimes      []*CampaignFlashSaleTimeItem   `json:"-" bson:"flash_sale_time,omitempty"` // to check flash sale time for cart
	SaleType            enum.CampaignSaleValueType     `json:"saleType,omitempty" bson:"sale_type,omitempty"`
	Status              enum.CampaignProductStatusType `json:"status,omitempty" bson:"status,omitempty"`
	CancelReason        *string                        `json:"cancelReason,omitempty" bson:"cancel_reason,omitempty"`
	PrivateNote         string                         `json:"privateNote,omitempty" bson:"private_note,omitempty"`
	HashTag             string                         `json:"-" bson:"hash_tag,omitempty"`
	CampaignPrice       *int64                         `json:"campaignPrice,omitempty" bson:"campaign_price,omitempty"`
	ChargeFee           string                         `json:"chargeFee,omitempty" bson:"charge_fee,omitempty"` // MARKETING . SELLER
	SystemDisplay       string                         `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
	Priority            *int                           `json:"priority,omitempty" bson:"priority,omitempty"` // sort

	MaxQuantityPerCustomer *int64 `json:"maxQuantityPerCustomer,omitempty" bson:"max_quantity_per_customer,omitempty" validate:"omitempty,gte=0,lte=1000000000"`
}

type UpdateSoldQuantityFromOrderRequest struct {
	OrderId          int64  `json:"orderId,omitempty"`
	CustomerId       int64  `json:"customerId,omitempty"`
	CampaignCode     string `json:"campaignCode,omitempty"`
	Sku              string `json:"sku,omitempty"`
	Quantity         int64  `json:"quantity,omitempty"`
	CheckoutQuantity int64  `json:"checkoutQuantity,omitempty"`
}

type SellerCampaignCheck struct {
	ProductIds []int64 `json:"productIds" validate:"required"`
	SellerCode string  `json:"sellerCode" validate:"required"`
}

type OrderCountPoint struct {
	Point            int   `json:"point" bson:"point"`
	TotalActualPrice int   `json:"totalActualPrice" bson:"total_actual_price"`
	TotalPrice       int   `json:"totalPrice" bson:"total_price"`
	Orders           []int `json:"orders" bson:"orders"`
	OrderValues      []struct {
		OrderID          int    `json:"orderId,omitempty" bson:"order_id,omitempty"`
		Status           string `json:"status,omitempty" bson:"status,omitempty"`
		TotalPrice       int    `json:"totalPrice,omitempty" bson:"total_price,omitempty"`
		TotalActualPrice int    `json:"totalActualPrice,omitempty" bson:"total_actual_price,omitempty"`
	} `json:"orderValues,omitempty" bson:"order_values,omitempty"`
	CustomerId int `json:"customerId" bson:"_id"`
}

// RequestCountOrderPoint ...
type RequestCountOrderPoint struct {
	ConfirmFrom             *time.Time `json:"confirmFrom"`
	ConfirmTo               *time.Time `json:"confirmTo"`
	SellerCode              string     `json:"sellerCode"`
	LogGamificationSyncCode string     `json:"logSyncGamificationCode"`
	ValueType               string     `json:"valueType"`
	GamificationCode        string     `json:"gamificationCode"`
	SystemDisplay           string     `json:"systemDisplay"`
}

type ImportCampaignProductRequest struct {
	Data         []CampaignProductCreateRequest `json:"data"`
	CampaignCode string                         `json:"campaignCode"`
}

type DeleteAppliedCustomersRequest struct {
	VoucherID int64 `json:"voucherId,omitempty" bson:"voucher_id,omitempty" validate:"required"`
}

type ImportDataRequest struct {
	Request      *CampaignProductCreateRequest `json:"request"`
	ResultDetail *ImportResultDetail           `json:"resultDetail"`
}

type DealApplyRequest struct {
	SKU         string      `json:"sku,omitempty" bson:"sku,omitempty"`
	ItemCode    string      `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	Quantity    *int64      `json:"quantity" bson:"quantity,omitempty"`
	MaxQuantity *int64      `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`
	Type        string      `json:"type,omitempty" bson:"type,omitempty"`
	Data        interface{} `json:"data,omitempty" bson:"data,omitempty"`
	DealCode    string      `json:"dealCode,omitempty"`
}

type WarmupCampaignProductRequest struct {
	Sku string `json:"sku,omitempty" bson:"sku,omitempty"`
}

// UpdateVoucherRequest ...
type UpdateVoucherRequest struct {
	VersionNo string `json:"versionNo,omitempty" bson:"version_no,omitempty"`

	ShortName            string                                    `json:"shortName,omitempty" bson:"short_name,omitempty"`
	DisplayName          string                                    `json:"displayName,omitempty" bson:"display_name,omitempty"`
	VoucherID            int64                                     `json:"voucherId,omitempty" bson:"voucher_id,omitempty"`
	Code                 string                                    `json:"code,omitempty" bson:"code,omitempty"`
	PromotionID          *int64                                    `json:"promotionId,omitempty" bson:"promotion_id,omitempty"`
	PromotionName        string                                    `json:"promotionName,omitempty" bson:"promotion_name,omitempty"`
	PromotionType        *enum.PromotionTypeValue                  `json:"promotionType,omitempty" bson:"promotion_type,omitempty"`
	ApplyType            enum.ApplyTypeValue                       `json:"applyType,omitempty" bson:"apply_type,omitempty"`
	StartTime            *time.Time                                `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime              *time.Time                                `json:"endTime,omitempty" bson:"end_time,omitempty"`
	PublicTime           *time.Time                                `json:"publicTime,omitempty" bson:"public_time,omitempty"`
	MaxUsage             *int64                                    `json:"maxUsage" bson:"max_usage,omitempty"`
	MaxUsagePerCustomer  *int64                                    `json:"maxUsagePerCustomer" bson:"max_usage_per_customer,omitempty"`
	MaxAutoApplyCount    *int64                                    `json:"maxAutoApplyCount,omitempty" bson:"max_auto_apply_count,omitempty"`
	UsageTotal           *int64                                    `json:"usageTotal" bson:"usage_total,omitempty"`
	VoucherType          *enum.VoucherTypeValue                    `json:"type,omitempty" bson:"type,omitempty"`
	VoucherGroupCode     *string                                   `json:"voucherGroupCode,omitempty" bson:"voucher_group_code,omitempty"`
	UsageType            *enum.UsageTypeValue                      `json:"usageType,omitempty" bson:"usage_type,omitempty"`
	AppliedCustomers     *[]int64                                  `json:"appliedCustomers" bson:"applied_customers,omitempty"`
	AppliedCustomerMap   map[int64]bool                            `json:"-" bson:"applied_customer_map,omitempty"`
	Status               *enum.VoucherStatusValue                  `json:"status,omitempty" bson:"status,omitempty"`
	Promotion            *Promotion                                `json:"promotion,omitempty" bson:"-"`
	UserPromotion        *UserPromotion                            `json:"userPromotion,omitempty" bson:"-"`
	HashTag              string                                    `json:"-" bson:"hash_tag,omitempty"`
	Scopes               []Scope                                   `json:"scopes,omitempty" bson:"scopes,omitempty"`
	Rewards              []Reward                                  `json:"rewards,omitempty" bson:"rewards,omitempty"`
	OrConditions         map[enum.ConditionTypeValue]PromotionType `json:"orConditions,omitempty" bson:"or_conditions,omitempty"`
	AndConditions        map[enum.ConditionTypeValue]PromotionType `json:"andConditions,omitempty" bson:"and_conditions,omitempty"`
	ConditionDescription *string                                   `json:"conditionDescription,omitempty" bson:"condition_description,omitempty"`
	Priority             *int                                      `json:"priority,omitempty" bson:"priority,omitempty"`
	CustomerApplyType    enum.CustomerApplyTypeValue               `json:"customerApplyType,omitempty" bson:"customer_apply_type,omitempty"`
	IsSkipNextVoucher    *bool                                     `json:"isSkipNextVoucher,omitempty" bson:"is_skip_next_voucher,omitempty"`
	SystemDisplay        string                                    `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
	VoucherImage         *string                                   `json:"voucherImage,omitempty" bson:"voucher_image,omitempty"`
	PromotionOrganizer   *enum.PromotionOrganizerValue             `json:"promotionOrganizer,omitempty" bson:"promotion_organizer,omitempty"`
	ChargeFee            *enum.ChargeFeeValue                      `json:"chargeFee,omitempty" bson:"charge_fee,omitempty"`
	Tag                  *string                                   `json:"tag,omitempty" bson:"tag,omitempty"`
	SellerCode           *string                                   `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerCodes          *[]string                                 `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	SellerName           *string                                   `json:"sellerName,omitempty" bson:"seller_name,omitempty"`
	ApplyDiscount        *ApplyDiscountOptions                     `json:"applyDiscount,omitempty" bson:"apply_discount,omitempty"`
	ConditionsVoucher    *[]string                                 `json:"conditionsVoucher,omitempty" bson:"conditions_voucher,omitempty"`
	LinkToPage           *string                                   `json:"linkToPage,omitempty" bson:"link_to_page,omitempty"`
	LinkToStore          *string                                   `json:"linkToStore,omitempty" bson:"link_to_store,omitempty"`
	StoreCode            *string                                   `json:"storeCode,omitempty" bson:"store_code,omitempty"`
	StoreName            *string                                   `json:"storeName,omitempty" bson:"store_name,omitempty"`

	// For voucher re-issuable on cancel order
	IsReuseOnOrderCancel *bool `json:"isReuseOnOrderCancel,omitempty" bson:"is_reuse_on_order_cancel,omitempty"`
	VoucherReuseDuration *int  `json:"voucherReuseDuration,omitempty" bson:"voucher_reuse_duration,omitempty"`

	SettingType      *enum.SettingTypeValue `json:"settingType,omitempty" bson:"setting_type,omitempty"`
	SegmentationCode string                 `json:"segmentationCode,omitempty" bson:"segmentation_code,omitempty"`
	SegmentationName string                 `json:"segmentationName,omitempty" bson:"segmentation_name,omitempty"`

	SkipCheckVoucherGroup *bool `json:"skipCheckVoucherGroup,omitempty" bson:"skip_check_voucher_group,omitempty"`
	IsSpecific            *bool `json:"isSpecific,omitempty" bson:"is_specific,omitempty"`
}

type UpsertComboRewardPiece struct {
	PieceCode      string `json:"pieceCode,omitempty"`
	PieceName      string `json:"pieceName,omitempty"`
	ImageUrl       string `json:"imageUrl,omitempty"`
	ExpectQuantity int64  `json:"expectQuantity,omitempty"`
}

type UpsertSetComboPieceRequest struct {
	ComboCode          string                   `json:"comboCode,omitempty"`
	ComboVersionUpdate string                   `json:"comboVersionUpdate,omitempty"`
	ComboType          enum.ComboRewardEnumType `json:"comboType,omitempty"`

	Pieces []*UpsertComboRewardPiece `json:"pieces,omitempty"`
}

type MeVoucherListRequest struct {
	Cart                  *Cart  `json:"cart,omitempty" validate:"required"`
	Offset                int64  `json:"offset"`
	Limit                 int64  `json:"limit"`
	GetTotal              bool   `json:"getTotal"`
	GetValidate           bool   `json:"getValidate"`
	GetVoucherAuto        bool   `json:"getVoucherAuto"`
	GetBySellerInCart     bool   `json:"getBySellerInCart"`
	Search                string `json:"search"`
	AccountID             int64  `json:"accountId"`
	Scope                 string `json:"scope"`
	SellerCode            string `json:"sellerCode"`
	StoreCode             string `json:"storeCode"`
	KeepCurrentSort       bool   `json:"keepCurrentSort"`
	SortByDiscount        string `json:"sortByDiscount"`
	SkipVoucherCollected  bool   `json:"skipVoucherCollected"`
	SkipVoucherOwner      bool   `json:"skipVoucherOwner"`
	SkipVoucherGift       bool   `json:"skipVoucherGift"`
	SkipVoucherByProduct  bool   `json:"skipVoucherByProduct"`
	SkipVoucherByPayment  bool   `json:"skipVoucherByPayment"`
	GetVoucherToCollect   bool   `json:"getVoucherToCollect"`
	Sku                   string `json:"sku"`
	GetMedxVoucherOnly    bool   `json:"getMedxVoucherOnly"`
	GetVoucherSpecific    bool   `json:"getVoucherSpecific"`
	GetVoucherProductOnly bool   `json:"getVoucherProductOnly"`
	GetVoucherOrderOnly   bool   `json:"getVoucherOrderOnly"`

	SystemDisplay string `json:"systemDisplay,omitempty" bson:"-"`

	SourceDetail                  *OrderSourceDetail `json:"sourceDetail,omitempty" bson:"-"`
	Codes                         []string           `json:"codes,omitempty"`
	SortType                      string             `json:"sortType,omitempty"`
	RemoveVoucherDuplicateProduct bool               `json:"removeVoucherDuplicateProduct,omitempty"`
}

type CreateMMultiVoucherRequest struct {
	Data []*Voucher `json:"data,omitempty" bson:"data,omitempty"`
}

type UpdateMultiVoucherRequest struct {
	Data []*UpdateVoucherRequest `json:"data,omitempty" bson:"data,omitempty"`
}

type VoucherCheckRequest struct {
	VoucherCodes               []string `json:"voucherCodes,omitempty" validate:"required"`
	Cart                       *Cart    `json:"cart,omitempty" validate:"required"`
	AccountID                  int64    `json:"accountId,omitempty"`
	GetVoucherAutoApply        bool     `json:"getVoucherAutoApply"`
	KeepVoucherInCart          bool     `json:"keepVoucherInCart,omitempty"`
	SkipVoucherByPaymentMethod bool     `json:"skipVoucherByPaymentMethod"`
	SystemDisplay              string   `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
	RedeemCodeRemovedArr       []string `json:"redeemCodeRemovedArr"`

	SourceDetail            *OrderSourceDetail `json:"sourceDetail,omitempty" bson:"-"`
	Customer                *Customer          `json:"customer,omitempty"`
	GetVoucherAutoByPayment bool               `json:"getVoucherAutoByPayment"`
}

type ReuseOnOrderCancelRequest struct {
	RedeemCodes *[]*string `json:"redeemCodes,omitempty"`
	AccountID   int64      `json:"accountId,omitempty"`
	CustomerID  int64      `json:"customerId,omitempty"`
	OrderID     int64      `json:"orderId,omitempty"`
}

type UserVoucherRequest struct {
	CustomerID  int64                 `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	CustomerIDs []int64               `json:"customerIds,omitempty" bson:"customer_ids,omitempty"`
	VoucherCode string                `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	Status      *enum.CodeStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	TypeAction  string                `json:"typeAction,omitempty" bson:"type_action,omitempty"`
	AccountID   int64                 `json:"accountID,omitempty" bson:"account_id,omitempty"`
	PrevStatus  *enum.CodeStatusValue `json:"prevStatus,omitempty" bson:"prev_status,omitempty"`
}
