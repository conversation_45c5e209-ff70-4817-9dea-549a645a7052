package api

import (
	"encoding/json"
	"log"
	"strings"

	"github.com/mssola/user_agent"

	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

// GetActionSource ...
func getActionSource(req sdk.APIRequest) *model.Account {
	var source []*model.ActionSource
	sourceAttr := req.GetAttribute("X-Source")
	if sourceAttr != nil {
		source = sourceAttr.([]*model.ActionSource)
		if source == nil || source[0] == nil || source[0].Account == nil || source[0].Account.AccountID <= 0 {
			return nil
		}
		return source[0].Account
	}
	sourceStr := req.GetHeader("X-Source")
	if sourceStr == "" {
		return nil
	}

	var sourceHeader *model.ActionSource
	err := json.Unmarshal([]byte(sourceStr), &sourceHeader)
	if err != nil || sourceHeader == nil || sourceHeader.Account == nil {
		return nil
	}
	return sourceHeader.Account
}

func parserQ(q string) string {
	return strings.Replace(utils.NormalizeString(q), " ", "-", -1)
}

type UAInfo struct {
	Platform      string // web, mobile-web, mobile-app
	OSName        string
	OSVersion     string
	ClientName    string
	ClientVersion string
}

func getUAInfo(uaStr string) (result *UAInfo) {

	defer func() {
		if rec := recover(); rec != nil {
			log.Println("Panic & Recovered", uaStr, rec)
		}
	}()

	result = &UAInfo{}
	if strings.HasPrefix(uaStr, "thuocsi") {
		parts := strings.Split(uaStr, " ")
		result.Platform = "mobile-app"
		clientInfo := strings.Split(parts[0], "/")
		result.ClientName = clientInfo[0]
		if len(clientInfo) > 1 {
			result.ClientVersion = clientInfo[1]
		} else {
			log.Println("Unknown ClientVersion UA " + uaStr)
		}
		if len(parts) > 3 {
			osInfo := strings.Split(parts[3], "/")
			result.OSName = osInfo[0]
			if len(osInfo) > 1 {
				result.OSVersion = osInfo[1]
			}
		}

	} else {
		ua := user_agent.New(uaStr)
		os := ua.OSInfo()
		result.OSName = os.Name
		result.OSVersion = os.Version
		if ua.Mobile() {
			result.Platform = "mobile-web"
		} else {
			result.Platform = "web-desktop"
		}
		result.ClientName, result.ClientVersion = ua.Browser()
	}

	// normalize os name
	osName := strings.ToLower(result.OSName)
	if strings.Contains(uaStr, "iPad") && strings.Contains(osName, "os") {
		result.OSName = "iPad OS"
	} else if strings.Contains(osName, "iphone") || strings.Contains(osName, "ios") {
		result.OSName = "iOS"
	} else {
		result.OSName = strings.Title(result.OSName)
	}

	return result
}
