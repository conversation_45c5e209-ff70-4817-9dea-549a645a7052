package action

import (
	"fmt"
	"sort"
	"strings"
	"sync"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CheckVoucherNoAuto validates a list of voucher codes for a given customer and cart without applying them automatically.
// It performs the following steps:
// 1. Fetches customer information if not provided in the input.
// 2. Updates the cart data with customer-related details (e.g., level, province code, scope).
// 3. Fetches system settings based on the provided display system.
// 4. Concurrently retrieves voucher details and user promotion usage for each voucher code.
// 5. Validates each voucher against the provided cart, customer, and settings.
//
// Parameters:
// - input: A pointer to a model.VoucherCheckRequest containing the customer, cart, and voucher codes to validate.
//
// Returns:
// - *common.APIResponse: The API response containing the validation results for each voucher or an error message if applicable.
// - If no vouchers are found, the function returns a response with a message "No vouchers" and an OK status.
// - Each voucher is validated using the isValidVoucher function, and the results are included in the response.
func CheckVoucherNoAuto(input *model.VoucherCheckRequest) *common.APIResponse {
	var (
		customer       *model.Customer
		errGetCustomer error

		setting   *model.Setting
		lastOrder *model.Order

		vouchers   = make([]*model.Voucher, 0, len(input.VoucherCodes))
		muVouchers sync.Mutex

		mapUse   = make(map[string]*model.UserPromotion)
		muMapUse sync.Mutex

		wg sync.WaitGroup
	)

	// fetch customer
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		if input.Customer == nil ||
			input.Customer.CustomerID == 0 ||
			input.Customer.Level == "" ||
			input.Customer.ProvinceCode == "" ||
			input.Customer.Scope == "" {
			customer, errGetCustomer = client.Services.Customer.GetCustomerByAccountID(input.AccountID)
			if errGetCustomer != nil {
				return
			}
		} else {
			customer = input.Customer
		}

		// fill in cart data
		if input.Cart != nil {
			input.Cart.CustomerLevel = utils.ParseStringToPointer(customer.Level)
			if input.Cart.ProvinceCode == "" { // set province code
				input.Cart.ProvinceCode = customer.ProvinceCode
			}
			if input.Cart.CustomerScope == "" {
				input.Cart.CustomerScope = customer.Scope
			}
		}
	})

	// fetch last order
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		lastOrder, _ = client.Services.Order.GetLastOrderSuccessByAccountID(input.AccountID)
	})

	// fetch setting
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		qSetting := model.SettingDB.QueryOne(model.Setting{SystemDisplay: input.SystemDisplay})
		if qSetting.Status == common.APIStatus.Ok {
			setting = qSetting.Data.([]*model.Setting)[0]
		}
	})

	for _, voucherCode := range input.VoucherCodes {
		func(code string) {

			// fetch voucher
			wg.Add(1)
			go sdk.Execute(func() {
				defer wg.Done()

				qVoucher := model.VoucherCacheDB.QueryOne(bson.M{"code": code})

				if qVoucher.Status == common.APIStatus.Ok {
					muVouchers.Lock()
					vouchers = append(vouchers, qVoucher.Data.([]*model.Voucher)[0])
					muVouchers.Unlock()
				}
			})

			// init used voucher map
			wg.Add(1)
			go sdk.Execute(func() {
				defer wg.Done()

				qUserPromotion := model.UserPromotionDB.QueryOne(bson.M{
					"customer_id":  input.Cart.CustomerID,
					"voucher_code": code,
				})

				if qUserPromotion.Status == common.APIStatus.Ok {
					muMapUse.Lock()
					mapUse[code] = qUserPromotion.Data.([]*model.UserPromotion)[0]
					muMapUse.Unlock()
				}
			})
		}(voucherCode)
	}

	wg.Wait()

	if errGetCustomer != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   errGetCustomer.Error(),
			ErrorCode: "GET_CUSTOMER_ERROR",
		}
	}

	if len(vouchers) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "No vouchers",
		}
	}

	if lastOrder != nil {
		customer.OrderCount = lastOrder.CustomerOrderIndex
		customer.LastOrderTime = lastOrder.CreatedTime
	} else {
		customer.OrderCount = 0
		customer.LastOrderTime = nil
	}

	// check voucher
	dataResponse := make([]*errRes, 0)
	for _, voucher := range vouchers {
		value := isValidVoucher(voucher.Code, voucher, mapUse[voucher.Code], input.Cart, customer, setting, nil)
		value.CanUse = value.ErrorMessage == ""

		dataResponse = append(dataResponse, &value)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    dataResponse,
		Message: "Voucher check only success",
	}
}

// CheckVoucherWithAuto validates and processes voucher codes provided in the input.
// It performs the following operations:
// 1. Fetches customer information based on the provided account ID or customer object.
// 2. Retrieves the last successful order of the customer.
// 3. Fetches system settings based on the input's system display.
// 4. Initializes and processes vouchers, including auto-applied vouchers and vouchers in use.
// 5. Validates each voucher against customer details, cart information, and system settings.
// 6. Filters vouchers based on conflicts with gifts or products in the cart.
// 7. Groups and checks voucher applicability based on settings and cart details.
//
// Parameters:
// - input: A pointer to a model.VoucherCheckRequest containing the voucher codes, customer details, cart information, and other relevant data.
//
// Returns:
// - *common.APIResponse: An API response containing the status, validated voucher data, and any error messages.
// - It handles both manually applied and auto-applied vouchers, ensuring proper validation and conflict resolution.
// - The function ensures that only applicable vouchers are returned in the response, with detailed error messages for invalid vouchers.
func CheckVoucherWithAuto(input *model.VoucherCheckRequest) *common.APIResponse {
	mapVoucherInCart := make(map[string]bool)
	for _, item := range input.VoucherCodes {
		mapVoucherInCart[item] = true
	}

	var (
		customer       *model.Customer
		lastOrder      *model.Order
		errGetCustomer error
		setting        *model.Setting
		qVoucher       = &common.APIResponse{
			Status: common.APIStatus.Ok,
			Data:   []*model.Voucher{},
		}

		wg sync.WaitGroup
	)

	// fetch customer
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		if input.Customer == nil {
			customer, errGetCustomer = client.Services.Customer.GetCustomerByAccountID(input.AccountID)
		} else {
			customer = input.Customer
		}

		if input.Cart != nil && customer != nil {
			input.Cart.CustomerLevel = utils.ParseStringToPointer(customer.Level)
			if input.Cart.ProvinceCode == "" { // set province code
				input.Cart.ProvinceCode = customer.ProvinceCode
			}
			if input.Cart.CustomerScope == "" {
				input.Cart.CustomerScope = customer.Scope
			}
		}
	})

	// fetch last order
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		lastOrder, _ = client.Services.Order.GetLastOrderSuccessByAccountID(input.AccountID)
	})

	// fetch setting
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		qSetting := model.SettingDB.QueryOne(model.Setting{SystemDisplay: input.SystemDisplay})
		if qSetting.Status == common.APIStatus.Ok {
			setting = qSetting.Data.([]*model.Setting)[0]
		}
	})

	// init used voucher map
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		if input.GetVoucherAutoApply || input.GetVoucherAutoByPayment {
			var qVoucherInUse *common.APIResponse
			var voucherWg sync.WaitGroup

			voucherWg.Add(1)
			go sdk.Execute(func() {
				defer voucherWg.Done()

				customerVouchers := []string{}
				queryUserVoucher := model.UserPromotionCacheDB.Query(bson.M{
					"customer_id":    input.Cart.CustomerID,
					"status":         bson.M{"$in": []string{"ACTIVE", "USED"}},
					"voucher_status": "ACTIVE",
				}, 0, 0, nil)
				if queryUserVoucher.Status == common.APIStatus.Ok {
					for _, v := range queryUserVoucher.Data.([]*model.UserPromotion) {
						customerVouchers = append(customerVouchers, v.VoucherCode)
					}
				}

				mapVoucherRemoved := make(map[string]struct{})
				for _, code := range input.RedeemCodeRemovedArr {
					if _, ok := mapVoucherInCart[code]; !ok {
						mapVoucherRemoved[code] = struct{}{}
					}
				}

				lastID := primitive.NilObjectID
				for {
					queryVoucherAuto := bson.M{
						"status":     "ACTIVE",
						"apply_type": enum.ApplyType.AUTO,
						"$or": []bson.M{
							{
								"customer_apply_type": "ALL",
							},
							{
								"customer_apply_type": "MANY",
								"code":                bson.M{"$in": customerVouchers},
							},
						},
						"system_display": input.SystemDisplay,
					}
					if lastID != primitive.NilObjectID {
						queryVoucherAuto["_id"] = bson.M{"$gt": lastID}
					}
					qVoucherAuto := model.VoucherDB.Query(queryVoucherAuto, 0, 0, &primitive.M{"_id": 1})
					if qVoucherAuto.Status == common.APIStatus.Ok {
						for _, voucher := range qVoucherAuto.Data.([]*model.Voucher) {
							lastID = voucher.ID
							if _, ok := mapVoucherRemoved[voucher.Code]; ok {
								continue
							}
							if input.GetVoucherAutoByPayment && !input.GetVoucherAutoApply {
								isVoucherPayment, _, _ := isExistConditionPaymentMethod(voucher)
								if !isVoucherPayment {
									continue
								}
							}
							if !mapVoucherInCart[voucher.Code] {
								qVoucher.Data = append(qVoucher.Data.([]*model.Voucher), voucher)
							}
						}
						if len(qVoucherAuto.Data.([]*model.Voucher)) < 1000 {
							break
						}
					} else {
						break
					}
				}
			})

			voucherWg.Add(1)
			go sdk.Execute(func() {
				defer voucherWg.Done()

				qVoucherInUse = model.VoucherDB.Query(bson.M{
					"code": bson.M{"$in": input.VoucherCodes},
				}, 0, int64(len(input.VoucherCodes)), nil)
			})

			voucherWg.Wait()

			if qVoucherInUse.Status == common.APIStatus.Ok {
				for _, voucher := range qVoucherInUse.Data.([]*model.Voucher) {
					qVoucher.Data = append(qVoucher.Data.([]*model.Voucher), voucher)
				}
			}
		} else {
			qVoucher = model.VoucherDB.Query(bson.M{
				"code": bson.M{"$in": input.VoucherCodes},
			}, 0, 0, &primitive.M{"priority": -1})
		}
	})

	wg.Wait()

	if errGetCustomer != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   errGetCustomer.Error(),
			ErrorCode: "GET_CUSTOMER_ERROR",
		}
	}

	if customer == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Customer not found",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}

	if lastOrder != nil {
		customer.OrderCount = lastOrder.CustomerOrderIndex
		customer.LastOrderTime = lastOrder.CreatedTime
	} else {
		customer.OrderCount = 0
		customer.LastOrderTime = nil
	}

	isSkipVoucherApplyAll := false
	if setting != nil && setting.SkipUseVoucher != nil {
		isSkipVoucherApplyAll = isSkipByCustomer(customer, *setting.SkipUseVoucher)
	}

	mapVoucher := make(map[string]*model.Voucher)
	if qVoucher.Status == common.APIStatus.Ok {
		input.VoucherCodes = make([]string, 0)
		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			if input.SystemDisplay != "" && input.SystemDisplay != voucher.SystemDisplay {
				continue
			}

			if mapVoucher[voucher.Code] == nil {
				input.VoucherCodes = append(input.VoucherCodes, voucher.Code)
			}
			mapVoucher[voucher.Code] = voucher
		}
	} else {
		input.VoucherCodes = []string{}
	}

	if input.Cart == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Cart is required",
			ErrorCode: "CART_REQUIRED",
		}
	}

	qUsed := model.UserPromotionCacheDB.Query(model.UserPromotion{
		CustomerID: input.Cart.CustomerID,
		ComplexQuery: []*bson.M{
			{
				"voucher_code": bson.M{"$in": input.VoucherCodes},
			},
		},
	}, 0, 0, nil)

	mapUsed := make(map[string]*model.UserPromotion)
	if qUsed.Status == common.APIStatus.Ok {
		for _, used := range qUsed.Data.([]*model.UserPromotion) {
			mapUsed[used.VoucherCode] = used
		}
	}

	dataResponse := make([]*errRes, 0)
	mapGiftManual := make(map[string]*model.Gift, 0)
	mapProductManual := make(map[string]*bool, 0)
	for _, voucherCode := range input.VoucherCodes {
		value := isValidVoucher(voucherCode, mapVoucher[voucherCode], mapUsed[voucherCode], input.Cart, customer, setting, nil)
		value.CanUse = true
		if value.ErrorMessage != "" {
			value.CanUse = false
		}
		if !value.AutoApply && value.CanUse {
			for _, gift := range value.Gifts {
				mapGiftManual[gift.Sku] = &gift
			}
			for _, product := range value.MatchProducts {
				if product.SellerCode != nil {
					mapProductManual[fmt.Sprintf("%s.%s", *product.SellerCode, product.ProductCode)] = utils.ParseBoolToPointer(true)
				}
			}
		}
		//if value.AutoApply && !value.CanUse {
		//	continue
		//}
		voucher := mapVoucher[voucherCode]
		if voucher != nil && voucher.CustomerApplyType == enum.CustomerApplyType.ALL && isSkipVoucherApplyAll {
			value.CanUse = false
			value.ErrorMessage = "Bạn không đủ điều kiện sử dụng mã"
			if value.AutoApply {
				continue
			}
		}
		dataResponse = append(dataResponse, &value)
	}

	newDataResponse := make([]*errRes, 0)
	for _, data := range dataResponse {
		if data.AutoApply && (isSkipVoucherAutoConflictGift(data.Gifts, mapGiftManual) || isSkipVoucherAutoConflictProduct(data.MatchProducts, mapProductManual)) {
			continue
		}
		newDataResponse = append(newDataResponse, data)
	}

	newDataResponse = checkVoucherGroupMapInUse(newDataResponse, setting, mapVoucherInCart, mapVoucher, input.Cart, input.GetVoucherAutoApply, input.SkipVoucherByPaymentMethod, input.GetVoucherAutoByPayment)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    newDataResponse,
		Message: "Voucher check success",
	}
}

func checkVoucherGroupMap(inUses, nonUses []*model.VoucherViewWebOnly, cart *model.Cart, setting *model.Setting) ([]*model.VoucherViewWebOnly, []*model.VoucherViewWebOnly) {
	voucherGroupConnectionCases := make(map[string]bool)
	if setting != nil {
		voucherGroupConnectionCases = setting.VoucherGroupConnectionCases
	}
	updatedInUses, updatedNonUses := checkVoucherGroupMapFromSetting(
		inUses,
		nonUses,
		voucherGroupConnectionCases,
		cart,
	)

	return updatedInUses, updatedNonUses
}

func checkVoucherGroupMapFromSetting(inUses, nonUses []*model.VoucherViewWebOnly, voucherGroupConnectionCases map[string]bool, cart *model.Cart) ([]*model.VoucherViewWebOnly, []*model.VoucherViewWebOnly) {
	if voucherGroupConnectionCases == nil {
		voucherGroupConnectionCases = make(map[string]bool)
	}
	inUseGroupMap := make(map[string]*model.VoucherViewWebOnly)

	mapGroupVoucherInUse := make(map[string]int)
	keyFormat := model.VGC_CASE_FORMAT
	for _, inUse := range inUses {
		if inUse.SkipCheckVoucherGroup != nil && *inUse.SkipCheckVoucherGroup {
			continue
		}
		mapGroupVoucherInUse[inUse.GroupCode+"."+inUse.SellerCode] = mapGroupVoucherInUse[inUse.GroupCode+"."+inUse.SellerCode] + 1
		inUseGroupMap[inUse.GroupCode+"."+inUse.SellerCode] = inUse
	}

	voucherGroupQuantityPairsContainNonUse := make(map[string]int)
	for _, nonUse := range nonUses {
		if nonUse.SkipCheckVoucherGroup != nil && *nonUse.SkipCheckVoucherGroup {
			continue
		}
		voucherGroupQuantityPairsContainNonUse[nonUse.GroupCode+"."+nonUse.SellerCode] = mapGroupVoucherInUse[nonUse.GroupCode+"."+nonUse.SellerCode] + 1
	}

	for k, v := range mapGroupVoucherInUse {
		sellerCode := strings.Split(k, ".")[1]
		k = strings.Split(k, ".")[0]
		for _, nonUse := range inUses {
			if !nonUse.CanUse {
				continue
			}
			if nonUse.SellerCode != sellerCode {
				continue
			}
			if nonUse.SkipCheckVoucherGroup != nil && *nonUse.SkipCheckVoucherGroup {
				continue
			}
			key := fmt.Sprintf(keyFormat, k, v, nonUse.GroupCode, 1)
			key2 := fmt.Sprintf(keyFormat, nonUse.GroupCode, 1, k, v)
			if nonUse.GroupCode == k {
				if v-1 == 0 {
					continue
				}
				key = fmt.Sprintf(keyFormat, k, v-1, nonUse.GroupCode, 1)
				key2 = fmt.Sprintf(keyFormat, nonUse.GroupCode, 1, k, v-1)
			}

			if !(voucherGroupConnectionCases[key] || voucherGroupConnectionCases[key2]) {
				nonUse.CanUse = false
				if vConflict, ok := inUseGroupMap[k+"."+sellerCode]; ok {
					nonUse.ErrorMessage = "Không dùng chung được với mã " + vConflict.Code
				} else {
					nonUse.ErrorMessage = "Mã giảm giá này hiện không hoạt động"
				}
				if cart.IsCartEmpty {
					nonUse.ErrorMessage = ""
				}
				nonUse.ActionStatus = "DISABLED"
			}
		}
		for _, nonUse := range nonUses {
			if !nonUse.CanUse {
				continue
			}
			if nonUse.SellerCode != sellerCode {
				continue
			}
			if nonUse.SkipCheckVoucherGroup != nil && *nonUse.SkipCheckVoucherGroup {
				continue
			}
			curNonUseQnt := 1
			//if k != nonUse.GroupCode && nonUse.GroupCode != "" {
			if k != nonUse.GroupCode {
				curNonUseQnt = voucherGroupQuantityPairsContainNonUse[nonUse.GroupCode+"."+nonUse.SellerCode]
			}

			key := fmt.Sprintf(keyFormat, k, v, nonUse.GroupCode, curNonUseQnt)
			key2 := fmt.Sprintf(keyFormat, nonUse.GroupCode, curNonUseQnt, k, v)

			if !(voucherGroupConnectionCases[key] || voucherGroupConnectionCases[key2]) {
				nonUse.CanUse = false
				if vConflict, ok := inUseGroupMap[k+"."+sellerCode]; ok {
					nonUse.ErrorMessage = "Không dùng chung được với mã " + vConflict.Code
				} else {
					nonUse.ErrorMessage = "Mã giảm giá này hiện không hoạt động"
				}
				if cart.IsCartEmpty {
					nonUse.ErrorMessage = ""
				}
				nonUse.ActionStatus = "DISABLED"
			}
		}
	}
	return inUses, nonUses
}

func isSkipVoucherAutoConflictGift(gifts []model.Gift, mapGiftManual map[string]*model.Gift) bool {
	for _, gift := range gifts {
		if data, ok := mapGiftManual[gift.Sku]; ok && data != nil {
			return true
		}
	}
	return false
}

func isSkipVoucherAutoConflictProduct(products []model.ProductConditionField, mapProductManual map[string]*bool) bool {
	for _, product := range products {
		if product.SellerCode != nil {
			if data, ok := mapProductManual[fmt.Sprintf("%s.%s", *product.SellerCode, product.ProductCode)]; ok && data != nil && *data {
				return true
			}
		}
	}
	return false
}

func checkVoucherGroupMapInUse(inUses []*errRes, setting *model.Setting, mapVoucherInCart map[string]bool, mapVoucher map[string]*model.Voucher, cart *model.Cart, getVoucherAuto, skipVoucherByPaymentMethod, getVoucherByPayment bool) []*errRes {
	if setting == nil {
		return inUses
	}
	//voucherGroupConnectionCases := setting.VoucherGroupConnectionCases
	useds := make([]*model.VoucherViewWebOnly, 0)
	autos := make([]*model.VoucherViewWebOnly, 0)
	mapInUse := make(map[string]*errRes)
	nAuto := setting.NumberOfVoucherManual
	nAutoSeller := setting.NumberOfVoucherManualPerSeller
	mapNAutoSeller := make(map[string]int)
	for _, inUse := range inUses {
		if inUse.PaymentMethod != "" {
			inUse.AutoApply = false
			if getVoucherByPayment {
				inUse.AutoApply = true
			}
		}
		voucher := mapVoucher[inUse.VoucherCode]
		voucher.CanUse = inUse.CanUse
		voucher.ErrorMessage = inUse.ErrorMessage
		voucher.Discount = inUse.Discount
		if mapVoucherInCart[inUse.VoucherCode] {
			if inUse.PaymentMethod != "" && !inUse.CanUse && getVoucherByPayment {
				continue
			}
			if !inUse.CanUse && inUse.AutoApply {
				continue
			}
			useds = append(useds, setViewData(voucher, cart))
			if inUse.SellerCode == "" {
				nAuto = nAuto - 1
			} else {
				if _, ok := mapNAutoSeller[inUse.SellerCode]; !ok {
					mapNAutoSeller[inUse.SellerCode] = nAutoSeller
				} else {
					mapNAutoSeller[inUse.SellerCode] = mapNAutoSeller[inUse.SellerCode] - 1
				}
			}
		} else {
			if voucher.CanUse && inUse.AutoApply {
				autos = append(autos, setViewData(voucher, cart))
			}
		}
		mapInUse[inUse.VoucherCode] = inUse
	}
	getDiscount := func(gifts []model.Gift) int {
		total := 0
		for _, gift := range gifts {
			total += int(gift.GiftValue)
		}
		return total
	}
	// sort by priority
	sort.Slice(useds, func(i, j int) bool {
		iPriority := 0
		jPriority := 0
		if useds[i].Priority != nil && *useds[i].Priority != 0 {
			iPriority = *useds[i].Priority
		}
		if useds[j].Priority != nil && *useds[j].Priority != 0 {
			jPriority = *useds[j].Priority
		}
		if iPriority == jPriority {
			iPriority = useds[i].Discount
			if iPriority == 0 {
				iPriority = getDiscount(useds[i].Gifts)
			}
			jPriority = useds[j].Discount
			if jPriority == 0 {
				jPriority = getDiscount(useds[j].Gifts)
			}
		}
		// sort by priority desc
		return iPriority > jPriority
	})
	sort.Slice(autos, func(i, j int) bool {
		iPriority := 0
		jPriority := 0
		if autos[i].Priority != nil && *autos[i].Priority != 0 {
			iPriority = *autos[i].Priority
		}
		if autos[j].Priority != nil && *autos[j].Priority != 0 {
			jPriority = *autos[j].Priority
		}
		if iPriority == jPriority {
			iPriority = autos[i].Discount
			if iPriority == 0 {
				iPriority = getDiscount(autos[i].Gifts)
			}
			jPriority = autos[j].Discount
			if jPriority == 0 {
				jPriority = getDiscount(autos[j].Gifts)
			}
		}
		// sort by priority desc
		return iPriority > jPriority
	})
	fnRes := make([]*errRes, 0)
	cAuto := 0
	mapCAutoSeller := make(map[string]int)
	useds, autos = checkVoucherGroupMap(useds, autos, &model.Cart{}, setting)
	for _, inUse := range useds {
		mapInUse[inUse.Code].CanUse = inUse.CanUse
		mapInUse[inUse.Code].ErrorMessage = inUse.ErrorMessage
		fnRes = append(fnRes, mapInUse[inUse.Code])
	}
	recheckAuto := make([]*model.VoucherViewWebOnly, 0)
	for _, auto := range autos {
		if auto.CanUse {
			recheckAuto = append(recheckAuto, auto)
		}
	}
	newListToCheck := make([]*model.VoucherViewWebOnly, 0)
	// đệ qui xóa 1, xóa 2... cho đến khi hết số mã auto
	for _, auto := range recheckAuto {
		// new list remove auto from recheck
		//newListToCheck = append(newListToCheck, auto)
		checkList := new([]*model.VoucherViewWebOnly)
		checkList = &newListToCheck
		_, check := checkVoucherGroupMap(*checkList, []*model.VoucherViewWebOnly{auto}, &model.Cart{}, setting)
		if check[0].CanUse {
			if auto.SellerCode == "" && cAuto < nAuto {
				cAuto++
				newListToCheck = append(newListToCheck, auto)
			}
			if _, ok := mapCAutoSeller[auto.SellerCode]; !ok {
				mapCAutoSeller[auto.SellerCode] = 0
			}
			if auto.SellerCode != "" && mapCAutoSeller[auto.SellerCode] < nAutoSeller {
				newListToCheck = append(newListToCheck, auto)
				mapCAutoSeller[auto.SellerCode]++
			}
		}
	}
	for _, auto := range newListToCheck {
		if auto.CanUse {
			fnRes = append(fnRes, mapInUse[auto.Code])
		}
	}

	return fnRes
	//
	//
	//inUses = removeInvalidAutoApplyVouchersFromOrigin(inUses, voucherGroupConnectionCases, setting, mapVoucherInCart)
	//
	//updatedInUses, nonUses := checkVoucherGroupMapInUseFromSetting(
	//	inUses,
	//	voucherGroupConnectionCases,
	//)
	//for _, nonUse := range nonUses {
	//	nonUse.CanUse = false
	//	nonUse.ErrorMessage = "Mã giảm giá này hiện không hoạt động"
	//}
	//
	//return updatedInUses
}
