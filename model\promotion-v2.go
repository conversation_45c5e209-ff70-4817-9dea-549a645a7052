package model

type PromotionV2 struct {
	Promotion
}

type PromotionType struct {
	OrConditions  []PromotionCondition `json:"orConditions,omitempty" bson:"or_conditions,omitempty"`
	AndConditions []PromotionCondition `json:"andConditions,omitempty" bson:"and_conditions,omitempty"`
}

type PromotionCondition struct {
	ProductConditionField          `bson:"product_condition,omitempty"`
	OrderConditionField            `bson:"order_condition,omitempty"`
	CustomerConditionField         `bson:"customer_condition,omitempty"`
	ProductTagConditionField       `bson:"product_tag_condition,omitempty"`
	PaymentConditionField          `bson:"payment_condition,omitempty"`
	SellerConditionField           `bson:"seller_condition,omitempty"`
	ProductBlackListConditionField `bson:"product_blacklist_condition,omitempty"`
}

type ProductConditionField struct {
	ProductID     int64   `json:"productId,omitempty" bson:"product_id,omitempty"`
	ProductCode   string  `json:"productCode,omitempty" bson:"product_code,omitempty"`
	SellerCode    *string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	MinQuantity   *int    `json:"minQuantity,omitempty" bson:"min_quantity,omitempty"`
	MinTotalPrice *int    `json:"minTotalPrice,omitempty" bson:"min_total_price,omitempty"`
	ProductName   string  `json:"productName,omitempty" bson:"product_name,omitempty"`
}

type OrderConditionField struct {
	MinTotalPrice     *int `json:"orderMinTotalPrice,omitempty" bson:"min_total_price,omitempty"`
	MinSkuQuantity    *int `json:"orderMinSkuQuantity,omitempty" bson:"min_sku_quantity,omitempty"`
	NewSkuQuantity    *int `json:"orderNewSkuQuantity,omitempty" bson:"new_sku_quantity,omitempty"`
	RepeatSkuQuantity *int `json:"orderRepeatSkuQuantity,omitempty" bson:"repeat_sku_quantity,omitempty"`
}

type CustomerConditionField struct {
	MinDayNoOrder *int `json:"minDayNoOrder,omitempty" bson:"min_day_no_order,omitempty"`
	MinOrderCount *int `json:"minOrderCount,omitempty" bson:"min_order_count,omitempty"`
	MaxOrderCount *int `json:"maxOrderCount,omitempty" bson:"max_order_count,omitempty"`
	IndexOrder    *int `json:"indexOrder,omitempty" bson:"index_order,omitempty"`
}

type ProductTagConditionField struct {
	TagCode       string  `json:"tagCode,omitempty" bson:"tag_code,omitempty"`
	SellerCode    *string `json:"tagSellerCode,omitempty" bson:"seller_code,omitempty"`
	MinQuantity   *int    `json:"tagMinQuantity,omitempty" bson:"min_quantity,omitempty"`
	SkuQuantity   *int    `json:"tagSkuQuantity,omitempty" bson:"sku_quantity,omitempty"`
	MinTotalPrice *int    `json:"tagMinTotalPrice,omitempty" bson:"min_total_price,omitempty"`
	TagName       string  `json:"tagName,omitempty" bson:"tag_name,omitempty"`
}

type PaymentConditionField struct {
	PaymentMethod     string `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	PaymentMethodName string `json:"paymentMethodName,omitempty" bson:"payment_method_name,omitempty"`
}

type SellerConditionField struct {
	SellerCode     string `json:"slSellerCode,omitempty" bson:"seller_code,omitempty"`
	MinQuantity    *int   `json:"sellerMinQuantity,omitempty" bson:"min_quantity,omitempty"`
	MinSkuQuantity *int   `json:"sellerMinSkuQuantity,omitempty" bson:"min_sku_quantity,omitempty"`
	MinTotalPrice  *int   `json:"sellerMinTotalPrice,omitempty" bson:"min_total_price,omitempty"`
}

type ProductBlackListConditionField struct {
	ProductName string  `json:"productNameBlackList,omitempty" bson:"product_name,omitempty"`
	SellerName  *string `json:"sellerNameBlackList,omitempty" bson:"seller_name,omitempty"`
	ProductCode string  `json:"productCodeBlackList,omitempty" bson:"product_code,omitempty"`
	SellerCode  *string `json:"sellerCodeBlackList,omitempty" bson:"seller_code,omitempty"`
}
