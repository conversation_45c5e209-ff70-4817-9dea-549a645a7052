package test

import (
	"fmt"
	"math"
	"testing"
	"time"
)

func TestTime(t *testing.T) {
	// t1 := time.Now().Add(-time.Hour * 28)
	// t2 := time.Now()

	tz := time.FixedZone("UTC+7", +7*60*60)
	t1 := time.Date(2021, 7, 1, 23, 59, 59, 999, tz)
	t2 := time.Date(2021, 7, 2, 00, 00, 00, 1, tz)

	fmt.Println(t1)
	fmt.Println(t2)

	fmt.Println("====================")

	t1Real := t1.In(tz)
	t2Real := t2.In(tz)

	fmt.Println(t1Real)
	fmt.Println(t2Real)

	fmt.Println("====================")
	fmt.Println("AFTER TRUNCATE: ")

	// newD1 := t1Real.Truncate(time.Hour * 24)
	// newD2 := t2Real.Truncate(time.Hour * 24)

	newD1 := time.Date(t1Real.Year(), t1Real.Month(), t1Real.Day(), 0, 0, 0, 0, tz)
	newD2 := time.Date(t2Real.Year(), t2Real.Month(), t2Real.Day(), 0, 0, 0, 0, tz)

	fmt.Println(newD1)
	fmt.Println(newD2)

	fmt.Printf("days: %v\n", math.Ceil(newD2.Sub(newD1).Hours()/24))
}

func TestTruncate(t *testing.T) {
	// t1 := time.Now().Add(-time.Hour * 28)
	// t2 := time.Now()

	tz := time.FixedZone("UTC+7", +7*60*60)
	t1 := time.Date(2021, 7, 1, 23, 59, 59, 999, tz)
	fmt.Println(t1)
	fmt.Println("====================")
	fmt.Println("AFTER TRUNCATE: ")

	newD1 := t1.Truncate(time.Hour * 24)

	fmt.Println(newD1)
}
