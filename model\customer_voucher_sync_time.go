package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"time"
)

type CustomerVoucherSyncTime struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	LastSyncTime *time.Time `json:"lastSyncTime,omitempty" bson:"last_sync_time,omitempty"`
	CustomerID   int        `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	AccountID    int        `json:"accountID,omitempty" bson:"account_id,omitempty"`
}

var CustomerVoucherSyncTimeDB = &db.Instance{
	ColName:        "customer_voucher_sync_time",
	TemplateObject: &CustomerVoucherSyncTime{},
}

func InitCustomerVoucherSyncTimeDB(s *mongo.Database) {
	CustomerVoucherSyncTimeDB.ApplyDatabase(s)

	t := true
	// unique index for account_id
	_ = CustomerVoucherSyncTimeDB.CreateIndex(bson.D{
		primitive.E{Key: "account_id", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})
}
