package enum

type LuckyWheelItemRewardType string

type luckyWheelItemReward struct {
	VOUCHER        LuckyWheelItemRewardType
	POINTS         LuckyWheelItemRewardType // điểm tích lũy
	TURNS          LuckyWheelItemRewardType
	TICKET_PATTERN LuckyWheelItemRewardType
	PIECE          LuckyWheelItemRewardType
	LK_POINTS      LuckyWheelItemRewardType // điểm vòng quay may mắn
	OTHER          LuckyWheelItemRewardType
}

var LuckyWheelItemReward = &luckyWheelItemReward{
	VOUCHER:        "VOUCHER",
	POINTS:         "POINTS",
	TURNS:          "TURNS",
	TICKET_PATTERN: "TICKET_PATTERN",
	PIECE:          "PIECE",
	LK_POINTS:      "LK_POINTS",
	OTHER:          "OTHER",
}
