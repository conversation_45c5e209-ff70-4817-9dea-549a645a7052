package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CheckProductFulfillmentDetail struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code                 string   `json:"code,omitempty" bson:"code,omitempty"`
	Status               string   `json:"status,omitempty" bson:"status,omitempty"`
	CheckFulfillmentCode string   `json:"checkFulfillmentCode,omitempty" bson:"check_fulfillment_code,omitempty"`
	CampaignProductCode  string   `json:"campaignProductCode,omitempty" bson:"campaign_product_code,omitempty"`
	Fulfill              *float64 `json:"fulfill,omitempty" bson:"fulfill,omitempty"`
	ProductCode          string   `json:"productCode,omitempty" bson:"product_code,omitempty"`
	IsValid              *bool    `json:"isValid,omitempty" bson:"is_valid,omitempty"`
	FailReason           string   `json:"failReason,omitempty" bson:"fail_reason,omitempty"`
	Sku                  string   `json:"sku,omitempty" bson:"sku,omitempty"`

	ComplexQuery      []*bson.M  `json:"-" bson:"$and,omitempty"`
	CreatedTimeFrom   *time.Time `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo     *time.Time `json:"createdTimeTo,omitempty" bson:"-"`
	CompletedTimeFrom *time.Time `json:"completedTimeFrom,omitempty" bson:"-"`
	CompletedTimeTo   *time.Time `json:"completedTimeTo,omitempty" bson:"-"`
}

// CheckProductFulfillmentDetailDB ...
var CheckProductFulfillmentDetailDB = &db.Instance{
	ColName:        "check_product_fulfillment_detail",
	TemplateObject: &CheckProductFulfillmentDetail{},
}

// InitCheckProductFulfillmentDetailModel is func init model check-product-fulfillment-detail
func InitCheckProductFulfillmentDetailModel(s *mongo.Database) {
	CheckProductFulfillmentDetailDB.ApplyDatabase(s)
}
