package client

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

const (
	pathGetVendorStoreList = "/seller/core/v1/vendor-store/list"
)

type sellerClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewSellerClient(apiHost, apiKey string, s *mongo.Database) *sellerClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}

	client := &sellerClient{
		svc: client.NewRESTClient(
			apiHost,
			"seller_client",
			DEFAULT_TIMEOUT,
			DEFAULT_RETRY_TIME,
			DEFAULT_WAIT_TIME,
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	// client.svc.SetDBLog(s)

	return client
}

// GetVendorStoreList ...
func (cli *sellerClient) GetVendorStoreList(offset, limit int) ([]*model.VendorStore, error) {
	params := map[string]string{}
	type q struct {
		Status string `json:"status"`
	}
	body := struct {
		Q            q    `json:"q"`
		GetTotal     bool `json:"getTotal"`
		GetStoreInfo bool `json:"getStoreInfo"`
		QueryForWeb  bool `json:"queryForWeb"`
		Offset       int  `json:"offset"`
		Limit        int  `json:"limit"`
	}{
		Q:            q{Status: "ACTIVE"},
		GetTotal:     true,
		GetStoreInfo: true,
		QueryForWeb:  true,
		Offset:       offset,
		Limit:        limit,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, body, pathGetVendorStoreList, nil)
	if err != nil {
		return nil, err
	}

	var result *model.VendorStoreResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}
