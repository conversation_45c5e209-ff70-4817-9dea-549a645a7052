package model

type SortVoucherFormula struct {
	Formula              string             `json:"formula"`
	SubFormula           map[string]string  `json:"subFormula"`
	DefaultValue         map[string]float64 `json:"defaultValue"`
	ApplyProvinceCodeMap map[string]bool    `json:"applyProvinceCodeMap"`
	ApplyCustomerIDMap   map[int64]bool     `json:"applyCustomerIDMap"`
}

type SortVoucherConfig struct {
	DefaultSortType string                        `json:"defaultSortType"`
	FormulaList     map[string]SortVoucherFormula `json:"formulaList"` // key is sort type
}
