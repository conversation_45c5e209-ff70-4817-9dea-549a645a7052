package action

import (
	"fmt"
	"math"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/conf"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetCheckProductFulfillmentLogList(query *model.CheckProductFulfillmentLog, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.CheckProductFulfillmentLogDB.Query(query, offset, limit, &primitive.M{"created_time": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.CheckProductFulfillmentLogDB.Count(query).Total
	}
	return result
}

func GetCheckProductFulfillmentDetailList(query *model.CheckProductFulfillmentDetail, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.CheckProductFulfillmentDetailDB.Query(query, offset, limit, &primitive.M{"created_time": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.CheckProductFulfillmentDetailDB.Count(query).Total
	}
	return result
}

func CheckCampaignProductFulfill(acc *model.Account) *common.APIResponse {
	go func() {
		checkProductFulfillment(acc, enum.CheckProductFulfillment.MANUAL)
	}()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Đang tiến hành kiểm tra tỉ lệ fulfill của các sản phẩm trong chương trình khuyến mãi",
	}
}

// CheckProductFulfillmentTask ...
func CheckProductFulfillmentTask(item *job.JobItem) (returnErr error) {
	if conf.Config.Env == "uat" {
		return nil
	}
	now := time.Now()
	checkProductFulfillment(nil, enum.CheckProductFulfillment.AUTO)
	lastDay := time.Date(now.Year(), now.Month(), now.Day()+1, 17, 0, 0, 0, now.Location())
	model.CheckProductFulfillmentJobExecutor.Push(nil, &job.JobItemMetadata{
		Topic:     "default",
		ReadyTime: &lastDay,
	})
	return nil
}

func checkProductFulfillment(acc *model.Account, checkCampaignProductFulfillType enum.CheckProductFulfillmentType) *common.APIResponse {
	now := time.Now()
	campaignList := getListCampaignToCheckFulfill()
	logCodes := make([]string, 0)
	listLog := make(map[string]*model.CheckProductFulfillmentLog)

	for _, cam := range campaignList {
		log := &model.CheckProductFulfillmentLog{
			StartTime:       &now,
			CampaignID:      cam.CampaignID,
			CampaignCode:    cam.CampaignCode,
			CampaignFulfill: cam.Fulfill,
			Status:          enum.CampaignLogStatus.IN_PROCESS,
			Type:            checkCampaignProductFulfillType,
		}
		if acc != nil {
			log.AccountID = acc.AccountID
		}
		log.Code = model.GenCodeWithTime()
		listLog[cam.CampaignCode] = log
		logCodes = append(logCodes, log.Code)

		_ = model.CheckProductFulfillmentLogDB.Create(log)
	}

	for _, cam := range campaignList {
		log := listLog[cam.CampaignCode]
		if log != nil {
			checkProductFulfillmentInCampaign(cam, log, acc, logCodes)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Check fulfillment successfully",
	}
}

func checkProductFulfillmentInCampaign(cam *model.Campaign, log *model.CheckProductFulfillmentLog, acc *model.Account, logCodes []string) {
	campaignProductList := getListCampaignProductToCheckFulfill(cam.CampaignCode)

	validItem := 0
	invalidItem := 0

	for _, item := range campaignProductList {
		detailCode := model.GenCodeWithTime()
		checkResultDetail := &model.CheckProductFulfillmentDetail{
			Code:                 detailCode,
			CheckFulfillmentCode: log.Code,
			CampaignProductCode:  item.CampaignProductCode,
			ProductCode:          item.ProductCode,
			Sku:                  item.Sku,
			Status:               "SUCCESS",
		}

		isValidFullFill := false
		isValidNewProduct := false
		now := time.Now()
		note := ""
		failReason := ""
		skuRes, errSku := client.Services.Product.GetSku(item.Sku)

		if errSku != nil {
			checkResultDetail.Status = "FAIL"
			checkResultDetail.FailReason = errSku.Error()
		}

		if skuRes.Fulfill == nil || cam.Fulfill == nil || (skuRes.Fulfill != nil && cam.Fulfill != nil && *skuRes.Fulfill >= *cam.Fulfill) {
			isValidFullFill = true
			checkResultDetail.Fulfill = skuRes.Fulfill
		} else {
			checkResultDetail.Fulfill = skuRes.Fulfill

			failReason = fmt.Sprintf("Sản phẩm có tỉ lệ fulfillment là %v%% không đạt tỉ lệ fulfill tối thiểu của chương trình", math.Round(*skuRes.Fulfill*100)/100)
			checkResultDetail.FailReason = failReason

			dt := time.Now().Add(7 * time.Hour)
			note = fmt.Sprintf("Sản phẩm có tỉ lệ fulfillment (%v%%) không đạt vào thời điểm duyệt chương trình %s", math.Round(*skuRes.Fulfill*100)/100, dt.Format("02-01-2006 15:04:05"))
		}

		if isValidFullFill {
			if cam.DaysOfNewProduct == nil || *cam.DaysOfNewProduct == 0 {
				isValidNewProduct = true
			} else if skuRes.CreatedTime != nil && (cam.DaysOfNewProduct != nil || *cam.DaysOfNewProduct != 0) && (skuRes.CreatedTime.Add(time.Duration(*cam.DaysOfNewProduct)*24*time.Hour).After(now) || skuRes.CreatedTime.Add(time.Duration(*cam.DaysOfNewProduct)*24*time.Hour).Equal(now)) {
				isValidNewProduct = true
			} else {
				failReason = fmt.Sprintf("Sản phẩm đã đăng bán %d ngày, chương trình chỉ cho phép các sản phẩm có số ngày đã đăng bán tối đa là %d ngày", int(now.Sub(*skuRes.CreatedTime).Hours()/24), *cam.DaysOfNewProduct)
				note = fmt.Sprintf("Sản phẩm đã đăng bán %d ngày, chương trình chỉ cho phép các sản phẩm có số ngày đã đăng bán tối đa là %d ngày", int(now.Sub(*skuRes.CreatedTime).Hours()/24), *cam.DaysOfNewProduct)
				checkResultDetail.FailReason = failReason
			}
		}

		checkResultDetail.IsValid = utils.ParseBoolToPointer(isValidFullFill && isValidNewProduct)

		if isValidFullFill && isValidNewProduct {
			validItem++
		} else {
			FALSE := false
			res := model.CampaignProductDB.UpdateOne(&model.CampaignProduct{
				CampaignProductCode: item.CampaignProductCode,
			}, &model.CampaignProduct{IsActive: &FALSE, PrivateNote: note})
			if res.Status == common.APIStatus.Ok {
				WarmUpCampaignProduct(item.Sku, cam.CampaignCode, nil)
				WarmupSkuSaleInfo(&model.SkuSaleInfo{
					SkuCode:             item.Sku,
					CampaignCode:        &item.CampaignCode,
					CampaignProductCode: &item.CampaignProductCode,
					IsActive:            &FALSE,
				})

				qTicket := model.TicketDB.QueryOne(model.Ticket{CampaignID: item.CampaignID, Sku: item.Sku})
				if qTicket.Status == common.APIStatus.Ok {
					ticket := qTicket.Data.([]*model.Ticket)[0]
					client.Services.Notification.CreateNotification(
						&model.Notification{
							UserID:       ticket.CreatedBy,
							Username:     fmt.Sprintf("SELLER_%d", ticket.CreatedBy),
							ReceiverType: utils.ParseStringToPointer("SELLER"),
							Topic:        "ANNOUNCEMENT",
							Title:        "Sản phẩm không đạt điều kiện đáp ứng của chương trình",
							Description:  fmt.Sprintf("Chương trình khuyến mãi %s", cam.CampaignName),
							Link:         fmt.Sprintf("/deals/campaigns/%s", cam.CampaignCode),
						})
				}
			}
			invalidItem++
		}
		model.CheckProductFulfillmentDetailDB.Create(checkResultDetail)
	}

	end := time.Now()
	_ = model.CheckProductFulfillmentLogDB.UpdateOne(model.CheckProductFulfillmentLog{Code: log.Code},
		model.CheckProductFulfillmentLog{
			EndTime:                         &end,
			Status:                          enum.CampaignLogStatus.DONE,
			Total:                           len(campaignProductList),
			NumberOfInactiveCampaignProduct: invalidItem,
			NumberOfActiveCampaignProduct:   validItem,
		},
	)
	if acc != nil {
		isCheckProductFulfillmentDone(logCodes, acc)
	}
}

func getListCampaignToCheckFulfill() []*model.Campaign {
	campaignList := make([]*model.Campaign, 0)
	now := time.Now()

	query := model.Campaign{
		IsActive: utils.ParseBoolToPointer(true),
		ComplexQuery: []*bson.M{
			{
				"start_time": bson.M{"$lte": now},
				"end_time":   bson.M{"$gte": now},
			},
		}}

	if qCampaign := model.CampaignDB.Query(query, 0, 0, nil); qCampaign.Status == common.APIStatus.Ok {
		campaignList = qCampaign.Data.([]*model.Campaign)
	}
	return campaignList
}

func getListCampaignProductToCheckFulfill(campaignCode string) []*model.CampaignProduct {
	campaignProductList := make([]*model.CampaignProduct, 0)
	internalSellers := []string{"MEDX", "MEDX_E", "MARKETING", "DENX"}
	query := model.CampaignProduct{
		CampaignCode: campaignCode,
		IsActive:     utils.ParseBoolToPointer(true),
		Status:       enum.CampaignProductStatus.NORMAL,
		ComplexQuery: []*bson.M{
			{
				"seller_code": &bson.M{
					"$nin": internalSellers,
				},
			},
		},
	}

	if qCampaignProduct := model.CampaignProductDB.Query(query, 0, 0, nil); qCampaignProduct.Status == common.APIStatus.Ok {
		campaignProductList = qCampaignProduct.Data.([]*model.CampaignProduct)
	}
	return campaignProductList
}

func logCheckProductFulfillmentFail(log *model.CheckProductFulfillmentLog, err *common.APIResponse) {
	now := time.Now()
	model.CheckProductFulfillmentLogDB.UpdateOne(model.CheckProductFulfillmentLog{Code: log.Code},
		model.CheckProductFulfillmentLog{Status: enum.CampaignLogStatus.FAIL, FailReason: fmt.Sprintf("%s-%s", err.ErrorCode, err.Message), EndTime: &now})
}

func isCheckProductFulfillmentDone(logCodes []string, acc *model.Account) {
	logHasDone := make([]string, 0)

	var query = model.CheckProductFulfillmentLog{}
	query.ComplexQuery = make([]*bson.M, 0)

	if len(logCodes) > 0 {
		query.ComplexQuery = []*bson.M{
			{
				"code": &bson.M{
					"$in": logCodes,
				},
			},
		}
	}

	resp := GetCheckProductFulfillmentLogList(&query, 0, 100, true)
	if resp.Status == common.APIStatus.Ok && resp.Data != nil {
		listLog := resp.Data.([]*model.CheckProductFulfillmentLog)
		for _, log := range listLog {
			if log.Status == enum.CampaignLogStatus.DONE {
				logHasDone = append(logHasDone, log.Code)
			}
		}
	}

	if len(logCodes) == len(logHasDone) {
		_ = client.Services.Notification.CreateNotification(&model.Notification{
			Username:     acc.Username,
			UserID:       acc.AccountID,
			ReceiverType: utils.ParseStringToPointer("EMPLOYEE"),
			Topic:        "ANNOUNCEMENT",
			Title:        fmt.Sprintln("Đã hoàn thành kiểm tra tỉ lệ fulfillment cho sản phẩm"),
			Link:         fmt.Sprintf("/marketing/check-product-fulfillment"),
		})
	}
}
