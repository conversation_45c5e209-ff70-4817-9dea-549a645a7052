package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateGamificationDetail(acc *model.Account, details []*model.GamificationDetail) *common.APIResponse {
	for _, detail := range details {
		if detail.GamificationDetailID == 0 {
			detail.CreatedBy = acc.AccountID
			detail.IsActive = utils.ParseBoolToPointer(true)
			detail.GamificationDetailID, detail.GamificationDetailCode = model.GenGamificationDetail()
			qCreate := model.GamificationDetailDB.Create(detail)
			if qCreate.Status != common.APIStatus.Ok {
				return qCreate
			}
		} else {
			qUpsert := model.GamificationDetailDB.UpdateOne(
				model.GamificationDetail{LuckyWheelCode: detail.LuckyWheelCode, GamificationDetailCode: detail.GamificationDetailCode, GamificationCode: detail.GamificationCode},
				detail)
			if qUpsert.Status != common.APIStatus.Ok {
				return qUpsert
			}
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Update successful.",
	}
}

func UpdateGamificationDetail(acc *model.Account, details []*model.GamificationDetail, gamificationID int64, lkCode string) *common.APIResponse {
	detailIds := make([]int64, 0)
	gameID := gamificationID
	luckWheelCode := lkCode
	gamificationResp := model.GamificationDB.QueryOne(model.Gamification{GamificationID: gamificationID})
	if gamificationResp.Status != common.APIStatus.Ok {
		return gamificationResp
	}
	for _, detail := range details {
		if detail.GamificationDetailID == 0 {
			detail.CreatedBy = acc.AccountID
			detail.IsActive = utils.ParseBoolToPointer(true)
			detail.GamificationDetailID, detail.GamificationDetailCode = model.GenGamificationDetail()

			qCreate := model.GamificationDetailDB.Create(detail)
			if qCreate.Status != common.APIStatus.Ok {
				return qCreate
			}
			detailIds = append(detailIds, detail.GamificationDetailID)
			gameID = detail.GamificationID
			luckWheelCode = detail.LuckyWheelCode
		} else {
			luckWheelCode = detail.LuckyWheelCode
			gameID = detail.GamificationID
			detailIds = append(detailIds, detail.GamificationDetailID)
			qUpsert := model.GamificationDetailDB.UpdateOne(
				model.GamificationDetail{LuckyWheelCode: detail.LuckyWheelCode, GamificationDetailCode: detail.GamificationDetailCode, GamificationCode: detail.GamificationCode},
				detail)
			if qUpsert.Status != common.APIStatus.Ok {
				return qUpsert
			}
		}
	}
	if luckWheelCode != "" {
		model.GamificationDetailDB.Delete(bson.M{"gamification_detail_id": bson.M{"$nin": detailIds}, "lucky_wheel_code": luckWheelCode})
	} else {
		model.GamificationDetailDB.Delete(bson.M{"gamification_detail_id": bson.M{"$nin": detailIds}, "gamification_id": gameID})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Update successful.",
	}
}

func GetListGamificationDetail(query *model.GamificationDetail, offset, limit int64, getTotal bool) *common.APIResponse {
	// if query.SystemDisplay == "" {
	// 	query.SystemDisplay = enum.SystemDisplay.Buymed
	// }
	res := model.GamificationDetailDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if res.Status != common.APIStatus.Ok {
		return res
	}
	if getTotal {
		total := model.GamificationDetailDB.Count(query).Total
		res.Total = total
	}
	return res
}

func GetSingleGamificationDetail(query *model.GamificationDetail) *common.APIResponse {
	return model.GamificationDetailDB.QueryOne(query)
}
