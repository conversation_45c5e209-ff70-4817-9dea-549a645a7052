package model

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/helper"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type VoucherGroupType struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	Name            *string            `json:"name,omitempty" bson:"name,omitempty" validate:"required"`
	Slug            *string            `json:"slug,omitempty" bson:"slug,omitempty"`
	Description     *string            `json:"description,omitempty" bson:"description,omitempty"`
	TypeID          int64              `json:"typeId,omitempty" bson:"type_id,omitempty"`
	Code            *string            `json:"code,omitempty" bson:"code,omitempty"`
	IsActive        *bool              `json:"isActive,omitempty" bson:"is_active,omitempty"`
	VoucherImage    *string            `json:"voucherImage,omitempty" bson:"voucher_image,omitempty"`
	HashTag         string             `json:"-" bson:"hash_tag,omitempty"`
}

type VoucherGroupTypeQuery struct {
	TypeID     int64      `json:"typeId,omitempty" bson:"type_id,omitempty"`
	IsActive   *bool      `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Slug       *string    `json:"slug,omitempty" bson:"slug,omitempty"`
	Code       *string    `json:"code,omitempty" bson:"code,omitempty"`
	TimeFrom   *time.Time `json:"timeFrom,omitempty" bson:"-"`
	TimeTo     *time.Time `json:"timeTo,omitempty" bson:"-"`
	SearchText string     `json:"search,omitempty" bson:"-"`
	Limit      int64      `json:"limit,omitempty" bson:"-"`
	Offset     int64      `json:"offset,omitempty" bson:"-"`
	GetTotal   bool       `json:"getTotal,omitempty" bson:"-"`
	HashTag    string     `json:"-" bson:"hash_tag,omitempty"`

	ComplexAndQueries []*bson.M `json:"-" bson:"$and,omitempty"`
	ComplexOrQueries  []*bson.M `json:"-" bson:"$or,omitempty"`
}

var VoucherGroupTypeDB = &db.Instance{
	ColName:        "voucher_group_type",
	TemplateObject: &VoucherGroupType{},
}

// InitVoucherTypeModel is func init model
func InitVoucherGroupTypeModel(s *mongo.Database) {
	VoucherGroupTypeDB.ApplyDatabase(s)
}

// AddStartTimeQuery add a bson document type M to ComplexAndQueries to
// get items that have date greater or equal to TimeFrom
func (v *VoucherGroupTypeQuery) AddStartTimeQueryIfHave() {
	if v.TimeFrom != nil {
		v.ComplexAndQueries = append(
			v.ComplexAndQueries,
			&primitive.M{
				"created_time": bson.M{
					"$gte": v.TimeFrom,
				},
			},
		)
	}
}

// AddStartTimeQuery add a bson document type M to ComplexAndQueries to
// get items that have date less or equal to TimeFrom
func (v *VoucherGroupTypeQuery) AddEndTimeQueryIfHave() {
	if v.TimeTo != nil {
		v.ComplexAndQueries = append(
			v.ComplexAndQueries,
			&primitive.M{
				"created_time": bson.M{
					"$lte": v.TimeTo,
				},
			},
		)
	}
}

func (v *VoucherGroupTypeQuery) AddTextSearchQueryIfHave() {
	if len(v.SearchText) > 0 {
		v.ComplexAndQueries = append(v.ComplexAndQueries, &bson.M{
			"hash_tag": primitive.Regex{
				Pattern: fmt.Sprintf(".*%s.*", helper.NormalizeString(v.SearchText)), Options: "",
			}})
	}
}

// GenVoucherTypeID generates a new voucher type id incrementally,
// and returns the new id and code of length 5
func (content *VoucherGroupType) GenVoucherGroupTypeID() {
	id, _ := GenVoucherGroupTypeID()
	content.TypeID = id
}

// FormatContentString formats string fields, including trimming name and description
func (content *VoucherGroupType) FormatContentString() error {
	if content.Name == nil {
		return fmt.Errorf("tên không được để trống")
	}

	*content.Name = strings.Trim(*content.Name, " ")

	if len(*content.Name) == 0 {
		return fmt.Errorf("tên không được để trống")
	}

	if content.Description != nil {
		*content.Description = strings.Trim(*content.Description, " ")
	}

	return nil
}
