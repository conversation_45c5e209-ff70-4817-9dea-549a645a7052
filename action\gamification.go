package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetGamification(query *model.Gamification) *common.APIResponse {
	qGamification := model.GamificationDB.QueryOne(query)
	if qGamification.Status != common.APIStatus.Ok {
		return qGamification
	}
	gamification := qGamification.Data.([]*model.Gamification)[0]
	qDetail := model.GamificationDetailDB.Query(&model.Gamification{
		GamificationID: gamification.GamificationID,
	}, 0, 0, nil)
	if qDetail.Status == common.APIStatus.Ok {
		gamification.Details = qDetail.Data.([]*model.GamificationDetail)
	}
	qGamification.Data = []*model.Gamification{gamification}
	return qGamification
}

func GetGamificationList(query *model.Gamification, offset, limit int64, getTotal bool) *common.APIResponse {

	// default get gamefication from buymed
	// if query.SystemDisplay == "" {
	// 	query.SystemDisplay = enum.SystemDisplay.Buymed
	// }

	result := model.GamificationDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	gamificationList := result.Data.([]*model.Gamification)
	ids := make([]*int64, 0)
	detailMap := make(map[int64][]*model.GamificationDetail, 0)
	for _, gamification := range gamificationList {
		ids = append(ids, &gamification.GamificationID)
	}
	queryDetail := &model.GamificationDetail{ComplexQuery: []*bson.M{
		{
			"gamification_id": bson.M{"$in": ids},
		},
	}}
	offsetDetail := int64(0)
	limitDetail := int64(1000)
	for {
		if qDetail := model.GamificationDetailDB.Query(queryDetail, offsetDetail*limitDetail, limitDetail, nil); qDetail.Status == common.APIStatus.Ok {
			for _, detail := range qDetail.Data.([]*model.GamificationDetail) {
				detailMap[detail.GamificationID] = append(detailMap[detail.GamificationID], detail)
			}
			offsetDetail++
		} else {
			break
		}
	}

	if getTotal {
		countResult := model.GamificationDB.Count(query)
		result.Total = countResult.Total
	}
	now := time.Now()
	for _, gamification := range gamificationList {
		gamification.Details = detailMap[gamification.GamificationID]
		gamification.Status = "PROCESSING"
		if gamification.StartTime != nil && gamification.StartTime.After(now) {
			gamification.Status = "UPCOMING"
		}
		if gamification.EndTime != nil && gamification.EndTime.Before(now) {
			gamification.Status = "EXPIRED"
		}
	}

	return result
}

func GetGamificationSelfList(acc *model.Account, query *model.Gamification, offset, limit int64, getTotal bool, customerId int64) *common.APIResponse {
	var customer *model.Customer
	var err error
	if customerId > 0 {
		customer, err = client.Services.Customer.GetCustomerByCustomerID(customerId)
	} else {
		customer, err = client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	}
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "GET_INFO_FAIL",
		}
	}
	now := time.Now()
	gamificationIDs := make([]int64, 0)
	customerMapBlackList := make(map[int64]*model.GamificationCustomer, 0)

	var systemDisplay = query.SystemDisplay
	if systemDisplay == "" {
		systemDisplay = enum.SystemDisplay.Buymed
		query.SystemDisplay = enum.SystemDisplay.Buymed
	}

	if query.Type == "" {
		query.Type = enum.GamificationType.GAMIFICATION_MISSION
	}

	qGamificationCustomer := model.GamificationCustomerDB.Query(bson.M{
		"customer_id": customer.CustomerID,
	}, 0, 0, &primitive.M{"_id": -1})
	if qGamificationCustomer.Status == common.APIStatus.Ok {
		for _, gamificationCustomer := range qGamificationCustomer.Data.([]*model.GamificationCustomer) {

			if gamificationCustomer.Status == "BLACKLIST" {
				customerMapBlackList[gamificationCustomer.GamificationID] = gamificationCustomer
			} else {
				gamificationIDs = append(gamificationIDs, gamificationCustomer.GamificationID)
			}
		}
	}
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []bson.M{
			{
				"scope.customer_scopes": customer.Scope,
			},
			{
				"scope.customer_scopes": bson.M{"$size": 0},
			},
			{
				"scope.customer_scopes": nil,
			},
		},
	})
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []bson.M{
			{
				"scope.customer_areas": customer.ProvinceCode,
			},
			{
				"scope.customer_areas": bson.M{"$size": 0},
			},
			{
				"scope.customer_areas": nil,
			},
		},
	})
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []bson.M{
			{
				"scope.customer_levels": customer.Level,
			},
			{
				"scope.customer_levels": bson.M{"$size": 0},
			},
			{
				"scope.customer_levels": nil,
			},
		},
	})

	// Default: only retrieve gamification without customer tags
	tagConditions := []bson.M{
		{
			"scope.customer_tags": bson.M{"$size": 0},
		},
		{
			"scope.customer_tags": nil,
		},
	}

	// Add tag matching condition only if customer has tags
	if len(customer.Tags) > 0 {
		tagConditions = append(tagConditions, bson.M{
			"scope.customer_tags": bson.M{"$in": customer.Tags},
		})
	}

	// Always filter gamification by customer tags regardless of their existence
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": tagConditions,
	})

	if customer.ConfirmedTime == nil {
		customer.ConfirmedTime = customer.CreatedTime
	}
	// check active date of customer and gamification
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []bson.M{
			{
				"customer_active_date": bson.M{
					"$lte": customer.ConfirmedTime,
				},
			},
			{
				"customer_active_date": nil,
			},
		},
	})
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []bson.M{
			{
				"scope.customer_apply_type": "ALL",
			},
			{
				"scope.customer_apply_type": nil,
			},
			{
				"gamification_id": bson.M{"$in": gamificationIDs},
			},
		},
	})
	query.IsActive = utils.ParseBoolToPointer(true)
	result := model.GamificationDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	gamificationList := make([]*model.Gamification, 0)
	for _, gamification := range result.Data.([]*model.Gamification) {
		if customerMapBlackList[gamification.GamificationID] != nil {
			continue
		}
		gamificationList = append(gamificationList, gamification)
	}
	result.Data = gamificationList
	ids := make([]*int64, 0)
	detailIds := make([]*int64, 0)
	detailMap := make(map[int64][]*model.GamificationDetail, 0)
	resultMap := make(map[int64]*model.GamificationResult, 0)
	gameMap := make(map[int64]*model.Gamification, 0)
	for _, gamification := range gamificationList {
		ids = append(ids, &gamification.GamificationID)
		gameMap[gamification.GamificationID] = gamification
	}
	queryDetail := &model.GamificationDetail{ComplexQuery: []*bson.M{
		{
			"gamification_id": bson.M{"$in": ids},
		},
	}}
	offsetDetail := int64(0)
	limitDetail := int64(1000)
	for {
		if qDetail := model.GamificationDetailDB.Query(queryDetail, offsetDetail*limitDetail, limitDetail, nil); qDetail.Status == common.APIStatus.Ok {
			for _, detail := range qDetail.Data.([]*model.GamificationDetail) {
				if detail.RewardDescription != nil && len(*detail.RewardDescription) > 0 {
					detail.Reward = &model.GamificationDetailReward{
						Description: *detail.RewardDescription,
					}
				}
				if detail.Condition != nil && detail.Condition.MinTotalValue != nil {
					detail.Condition.Target = *detail.Condition.MinTotalValue
				}
				detailMap[detail.GamificationID] = append(detailMap[detail.GamificationID], detail)
				detailIds = append(detailIds, &detail.GamificationDetailID)
			}
			offsetDetail++
		} else {
			break
		}
	}

	queryResult := &model.GamificationResult{
		AccountID: acc.AccountID,
		ComplexQuery: []*bson.M{
			{
				"gamification_detail_id": bson.M{"$in": detailIds},
			},
		}}
	offsetResult := int64(0)
	limitResult := int64(1000)
	for {
		qResult := model.GamificationResultDB.Query(queryResult, offsetResult*limitResult, limitResult, nil)
		if qResult.Status == common.APIStatus.Ok {
			for _, result := range qResult.Data.([]*model.GamificationResult) {
				resultMap[result.GamificationDetailID] = result
			}
			offsetResult++
		} else {
			break
		}
	}

	mapCheckCustomerSubmit := make(map[int64]bool, 0)
	if qCheckSubmit := model.GamificationCustomerDB.Query(model.GamificationCustomer{CustomerID: customer.CustomerID, ComplexQuery: []*bson.M{
		{"gamification_id": bson.M{"$in": ids}},
	}}, 0, 0, nil); qCheckSubmit.Status == common.APIStatus.Ok {
		for _, submit := range qCheckSubmit.Data.([]*model.GamificationCustomer) {
			if submit.Status == "ACTIVE" {
				mapCheckCustomerSubmit[submit.GamificationID] = true
			}
		}
	}

	if getTotal {
		countResult := model.GamificationDB.Count(query)
		result.Total = countResult.Total
	}
	for _, details := range detailMap {
		for _, detail := range details {
			cond := detail.Condition
			var detailValue *model.DetailValue
			isCompleted := false

			if data, ok := resultMap[detail.GamificationDetailID]; ok {
				if data.DetailValue != nil {
					detail.Result = &model.GamificationDetailResult{
						Value:  data.DetailValue.MinTotalValue,
						Data:   data.Data,
						Status: data.Status,
					}
					detailValue = data.DetailValue
				} else {
					detail.Result = &model.GamificationDetailResult{
						Value:  data.Value,
						Data:   data.Data,
						Status: data.Status,
					}
					detailValue = &model.DetailValue{}
				}

				if query.Type == enum.GamificationType.DASHBOARD_MISSION {
					detail.ProcessInfos = handleDashboardMissionProcessInfo(detail.ProcessInfos, detailValue, cond, isCompleted)
				}
			}
		}
	}
	//now := time.Now()
	for _, gamification := range gamificationList {
		gamification.Details = detailMap[gamification.GamificationID]
		gamification.Status = "PROCESSING"
		if gamification.StartTime.After(now) {
			gamification.Status = "UPCOMING"
		}
		if gamification.EndTime.Before(now) {
			gamification.Status = "EXPIRED"
		}
		gamification.IsSubmitted = mapCheckCustomerSubmit[gamification.GamificationID]
	}

	return result
}

func handleDashboardMissionProcessInfo(processInfos []*model.MissionProcessInfo, data *model.DetailValue, cond *model.GamificationDetailCondition, isCompleted bool) []*model.MissionProcessInfo {
	if !isNilOrDefaultValue(cond.MinTotalValue) {
		processInfos = append(processInfos, &model.MissionProcessInfo{
			UnitName: "Tổng giá trị đã đặt",
			Value: func() int {
				return data.MinTotalValue
			}(),
			Target:      *cond.MinTotalValue,
			IsCompleted: data.MinTotalValue >= *cond.MinTotalValue,
		})
	}

	if !isNilOrDefaultValue(cond.MinTotalSkuCount) {
		if data.SkuUniques != nil {
			data.MinTotalSkuCount = len(data.SkuUniques)
		} else {
			data.MinTotalSkuCount = 0
		}
		processInfos = append(processInfos, &model.MissionProcessInfo{
			UnitName: "Tổng số lượng sản phẩm",
			Value: func() int {
				return data.MinTotalSkuCount
			}(),
			Target:      *cond.MinTotalSkuCount,
			IsCompleted: data.MinTotalSkuCount >= *cond.MinTotalSkuCount,
		})
	}

	if !isNilOrDefaultValue(cond.MinTotalSkuCompletedCount) {
		processInfos = append(processInfos, &model.MissionProcessInfo{
			UnitName: "Tổng số lượng sản phẩm đã hoàn tất",
			Value: func() int {
				return data.CompletedQuantityCount
			}(),
			Target:      *cond.MinTotalSkuCompletedCount,
			IsCompleted: data.CompletedQuantityCount >= *cond.MinTotalSkuCompletedCount,
		})
	}

	return processInfos
}

func GetGamificationSelfSingle(acc *model.Account, query *model.Gamification) *common.APIResponse {
	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "GET_INFO_FAIL",
		}
	}
	var systemDisplay = query.SystemDisplay
	if systemDisplay == "" {
		systemDisplay = enum.SystemDisplay.Buymed
		query.SystemDisplay = enum.SystemDisplay.Buymed
	}
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []bson.M{
			{
				"scope.customer_scopes": customer.Scope,
			},
			{
				"scope.customer_scopes": bson.M{"$size": 0},
			},
			{
				"scope.customer_scopes": nil,
			},
		},
	})
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []bson.M{
			{
				"scope.customer_areas": customer.ProvinceCode,
			},
			{
				"scope.customer_areas": bson.M{"$size": 0},
			},
			{
				"scope.customer_areas": nil,
			},
		},
	})
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []bson.M{
			{
				"scope.customer_levels": customer.Level,
			},
			{
				"scope.customer_levels": bson.M{"$size": 0},
			},
			{
				"scope.customer_levels": nil,
			},
		},
	})
	if customer.ConfirmedTime == nil {
		customer.ConfirmedTime = customer.CreatedTime
	}
	// check active date of customer and gamification
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$or": []bson.M{
			{
				"customer_active_date": bson.M{
					"$lte": customer.ConfirmedTime,
				},
			},
			{
				"customer_active_date": nil,
			},
		},
	})
	query.IsActive = utils.ParseBoolToPointer(true)
	result := model.GamificationDB.QueryOne(query)
	if result.Status != common.APIStatus.Ok {
		return result
	}
	gamification := result.Data.([]*model.Gamification)[0]
	detailIds := make([]*int64, 0)
	detailMap := make(map[int64][]*model.GamificationDetail, 0)
	resultMap := make(map[int64]*model.GamificationResult, 0)
	queryDetail := &model.GamificationDetail{GamificationID: gamification.GamificationID}
	offsetDetail := int64(0)
	limitDetail := int64(1000)
	for {
		if qDetail := model.GamificationDetailDB.Query(queryDetail, offsetDetail*limitDetail, limitDetail, nil); qDetail.Status == common.APIStatus.Ok {
			for _, detail := range qDetail.Data.([]*model.GamificationDetail) {
				if detail.RewardDescription != nil && len(*detail.RewardDescription) > 0 {
					detail.Reward = &model.GamificationDetailReward{
						Description: *detail.RewardDescription,
					}
				}
				if detail.Condition != nil && detail.Condition.MinTotalValue != nil {
					detail.Condition.Target = *detail.Condition.MinTotalValue
				}
				detailMap[detail.GamificationID] = append(detailMap[detail.GamificationID], detail)
				detailIds = append(detailIds, &detail.GamificationDetailID)
			}
			offsetDetail++
		} else {
			break
		}
	}

	queryResult := &model.GamificationResult{
		AccountID: acc.AccountID,
		ComplexQuery: []*bson.M{
			{
				"gamification_detail_id": bson.M{"$in": detailIds},
			},
		}}
	offsetResult := int64(0)
	limitResult := int64(1000)
	for {
		qResult := model.GamificationResultDB.Query(queryResult, offsetResult*limitResult, limitResult, nil)
		if qResult.Status == common.APIStatus.Ok {
			for _, result := range qResult.Data.([]*model.GamificationResult) {
				resultMap[result.GamificationDetailID] = result
			}
			offsetResult++
		} else {
			break
		}
	}

	for _, details := range detailMap {
		for _, detail := range details {
			if data, ok := resultMap[detail.GamificationDetailID]; ok {
				if data.DetailValue != nil {
					detail.Result = &model.GamificationDetailResult{
						Value:  data.DetailValue.MinTotalValue,
						Data:   data.Data,
						Status: data.Status,
					}
				} else {
					detail.Result = &model.GamificationDetailResult{
						Value:  data.Value,
						Data:   data.Data,
						Status: data.Status,
					}
				}
			}
		}
	}
	gamification.Details = detailMap[gamification.GamificationID]
	if qCheckCustomerSubmit := model.GamificationCustomerDB.QueryOne(bson.M{
		"customer_id":     customer.CustomerID,
		"status":          bson.M{"$ne": "BLACKLIST"},
		"gamification_id": gamification.GamificationID,
	}); qCheckCustomerSubmit.Status == common.APIStatus.Ok {
		d := qCheckCustomerSubmit.Data.([]*model.GamificationCustomer)[0]
		if d.Status == "ACTIVE" {
			gamification.IsSubmitted = true
		}
		if d.Status == "BLACKLIST" {
			return &common.APIResponse{
				Status:    common.APIStatus.NotFound,
				Message:   "Not found gamification",
				ErrorCode: "NOT_FOUND",
			}
		}
	} else {
		if gamification.Scope != nil && gamification.Scope.CustomerApplyType == "MANY" {
			return &common.APIResponse{
				Status:    common.APIStatus.NotFound,
				Message:   "Not found gamification",
				ErrorCode: "NOT_FOUND",
			}
		}
	}

	now := time.Now()
	gamification.Status = "PROCESSING"
	if gamification.StartTime.After(now) {
		gamification.Status = "UPCOMING"
	}
	if gamification.EndTime.Before(now) {
		gamification.Status = "EXPIRED"
	}
	return result
}

func CreateGamification(acc *model.Account, gamification *model.Gamification) *common.APIResponse {
	if gamification != nil && gamification.Type == "" {
		gamification.Type = enum.GamificationType.GAMIFICATION_MISSION
	}

	gamification.CreatedBy = acc.AccountID

	gamification.GamificationID, gamification.GamificationCode = model.GenGamification()
	normalName := strings.Replace(utils.NormalizeString(gamification.Name), " ", "-", -1)
	normalCode := strings.Replace(utils.NormalizeString(gamification.GamificationCode), " ", "-", -1)

	gamification.HashTag = fmt.Sprintf("%d-%s-%s", gamification.GamificationID, normalCode, normalName)
	details := gamification.Details
	gamification.EndCalResultTime = gamification.EndTime
	endTime := gamification.EndTime
	if gamification.NumberOfDayCalResult != nil {
		endCalResultTime := endTime.AddDate(0, 0, *gamification.NumberOfDayCalResult)
		gamification.EndCalResultTime = &endCalResultTime
	}

	gamification.RewardStatus = enum.RewardProgress.READY

	if gamification.SystemDisplay == "" {
		gamification.SystemDisplay = enum.SystemDisplay.Buymed
	}

	if len(gamification.Details) > 0 {
		for _, detail := range details {
			detail.CreatedBy = acc.AccountID
			detail.IsActive = utils.ParseBoolToPointer(true)
			detail.GamificationDetailID, detail.GamificationDetailCode = model.GenGamificationDetail()
			detail.GamificationID, detail.GamificationCode = gamification.GamificationID, gamification.GamificationCode
		}
		res := model.GamificationDetailDB.CreateMany(details)
		if res.Status != common.APIStatus.Ok {
			return res
		}
	}

	return model.GamificationDB.Create(gamification)
}

func UpdateGamification(acc *model.Account, gamification *model.Gamification) *common.APIResponse {
	query := model.Gamification{
		GamificationCode: gamification.GamificationCode,
		GamificationID:   gamification.GamificationID,
	}
	if query.GamificationID == 0 && query.GamificationCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing id or code",
			ErrorCode: "MISSING_ID_OR_CODE",
		}
	}
	qGamification := model.GamificationDB.QueryOne(query)
	curGamification := qGamification.Data.([]*model.Gamification)[0]
	if qGamification.Status != common.APIStatus.Ok {
		return qGamification
	}

	if gamification.NumberOfDayCalResult == nil {
		gamification.NumberOfDayCalResult = curGamification.NumberOfDayCalResult
	}

	if gamification.EndTime == nil {
		gamification.EndTime = curGamification.EndTime
	}
	gamification.EndCalResultTime = gamification.EndTime
	endTime := gamification.EndTime
	if gamification.NumberOfDayCalResult != nil {
		endCalResultTime := endTime.AddDate(0, 0, *gamification.NumberOfDayCalResult)
		gamification.EndCalResultTime = &endCalResultTime
	}

	gamification.UpdatedBy = acc.AccountID
	if len(gamification.Details) > 0 {
		for _, detail := range gamification.Details {
			if detail.GamificationDetailID == 0 && detail.GamificationDetailCode == "" {
				detail.CreatedBy = acc.AccountID
				detail.IsActive = utils.ParseBoolToPointer(true)
				detail.GamificationDetailID, detail.GamificationDetailCode = model.GenGamificationDetail()
				detail.GamificationID, detail.GamificationCode = curGamification.GamificationID, curGamification.GamificationCode
				if res := model.GamificationDetailDB.Create(detail); res.Status != common.APIStatus.Ok {
					return res
				}
			} else {
				queryDetail := &model.GamificationDetail{
					GamificationDetailID:   detail.GamificationDetailID,
					GamificationDetailCode: detail.GamificationDetailCode,
					GamificationCode:       gamification.GamificationCode,
					GamificationID:         gamification.GamificationID,
				}
				if res := model.GamificationDetailDB.UpdateOne(queryDetail, detail); res.Status != common.APIStatus.Ok {
					return res
				}
			}
		}
	}
	if gamification.IsClearCustomerActiveDate {
		model.GamificationDB.UpdateOneWithOption(query, bson.M{"$unset": bson.M{"customer_active_date": ""}})
	}
	return model.GamificationDB.UpdateOne(query, gamification)
}

func GetGamificationResultList(query *model.GamificationResult, offset, limit int64, getTotal bool) *common.APIResponse {

	qResult := model.GamificationResultDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if qResult.Status != common.APIStatus.Ok {
		return qResult
	}
	if getTotal {
		countResult := model.GamificationResultDB.Count(query)
		qResult.Total = countResult.Total
	}
	results := qResult.Data.([]*model.GamificationResult)
	ids := make([]*int64, 0)
	for _, result := range results {
		ids = append(ids, &result.GamificationDetailID)
	}
	queryDetail := &model.Gamification{ComplexQuery: []*bson.M{
		{
			"gamification_detail_id": bson.M{"$in": ids},
		},
	}}
	detailMap := make(map[int64]*model.GamificationDetail, 0)
	offsetDetail := int64(0)
	limitDetail := int64(1000)
	for {
		if qDetail := model.GamificationDetailDB.Query(queryDetail, offsetDetail*limitDetail, limitDetail, nil); qDetail.Status == common.APIStatus.Ok {
			for _, detail := range qDetail.Data.([]*model.GamificationDetail) {
				detailMap[detail.GamificationDetailID] = detail
			}
			offsetDetail++
		} else {
			break
		}
	}

	for _, result := range results {
		if data, ok := detailMap[result.GamificationDetailID]; ok {
			result.Detail = data
		}
	}

	return qResult
}

func GetGamificationSelfResultList(acc *model.Account, query *model.GamificationResult, offset, limit int64, getTotal bool) *common.APIResponse {

	query.AccountID = acc.AccountID
	qResult := model.GamificationResultDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if qResult.Status != common.APIStatus.Ok {
		return qResult
	}
	if getTotal {
		countResult := model.GamificationResultDB.Count(query)
		qResult.Total = countResult.Total
	}
	results := qResult.Data.([]*model.GamificationResult)
	ids := make([]*int64, 0)
	for _, result := range results {
		ids = append(ids, &result.GamificationDetailID)
	}
	queryDetail := &model.Gamification{ComplexQuery: []*bson.M{
		{
			"gamification_detail_id": bson.M{"$in": ids},
		},
	}}
	detailMap := make(map[int64]*model.GamificationDetail, 0)
	offsetDetail := int64(0)
	limitDetail := int64(1000)
	for {
		if qDetail := model.GamificationDetailDB.Query(queryDetail, offsetDetail*limitDetail, limitDetail, nil); qDetail.Status == common.APIStatus.Ok {
			for _, detail := range qDetail.Data.([]*model.GamificationDetail) {
				detailMap[detail.GamificationDetailID] = detail
			}
			offsetDetail++
		} else {
			break
		}
	}

	for _, result := range results {
		if data, ok := detailMap[result.GamificationDetailID]; ok {
			result.Detail = data
		}
	}

	return qResult
}

func GetGamificationListBySellerCode(query *model.Gamification, offset, limit int64, getTotal bool) *common.APIResponse {

	if query.SystemDisplay == "" {
		query.SystemDisplay = enum.SystemDisplay.Buymed
	}

	query.IsActive = utils.ParseBoolToPointer(true)

	result := model.GamificationDB.Query(query, offset, limit, &primitive.M{"start_time": 1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	gamificationList := result.Data.([]*model.Gamification)
	ids := make([]*int64, 0)
	detailMap := make(map[int64][]*model.GamificationDetail, 0)
	for _, gamification := range gamificationList {
		ids = append(ids, &gamification.GamificationID)
	}
	queryDetail := &model.GamificationDetail{ComplexQuery: []*bson.M{
		{
			"gamification_id": bson.M{"$in": ids},
		},
	}}
	offsetDetail := int64(0)
	limitDetail := int64(1000)
	for {
		if qDetail := model.GamificationDetailDB.Query(queryDetail, offsetDetail*limitDetail, limitDetail, nil); qDetail.Status == common.APIStatus.Ok {
			for _, detail := range qDetail.Data.([]*model.GamificationDetail) {
				detailMap[detail.GamificationID] = append(detailMap[detail.GamificationID], detail)
			}
			offsetDetail++
		} else {
			break
		}
	}

	if getTotal {
		countResult := model.GamificationDB.Count(query)
		result.Total = countResult.Total
	}
	now := time.Now()
	for _, gamification := range gamificationList {
		gamification.Details = detailMap[gamification.GamificationID]
		gamification.Status = "PROCESSING"
		if gamification.StartTime.After(now) {
			gamification.Status = "UPCOMING"
		}
		if gamification.EndTime.Before(now) {
			gamification.Status = "EXPIRED"
		}
	}

	return result
}

// AddRewardGenerationJob: add point or voucher generation job to job queue,
func AddRewardsGenerationJob(
	acc model.Account,
	input model.Gamification,
) *common.APIResponse {

	resp := queryReadyGamification(input)

	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	gam := resp.Data.([]*model.Gamification)[0]

	err := validateRewardConditions(gam)

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "INVALID_REWARD_CONDITIONS",
		}
	}

	gamDetailsResp := model.GamificationDetailDB.Query(&model.GamificationDetail{
		GamificationID: gam.GamificationID,
	}, 0, 100, nil)

	if gamDetailsResp.Status != common.APIStatus.Ok {
		return gamDetailsResp
	}

	gds := gamDetailsResp.Data.([]*model.GamificationDetail)

	if len(gds) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing detail",
			ErrorCode: "MISSING_DETAIL",
		}
	}

	for _, detail := range gds {
		if detail == nil || detail.Reward == nil {
			continue
		}

		err := addRewardGenJob(acc, *gam, *detail)
		if err != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.NotFound,
				Message:   err.Error(),
				ErrorCode: "ADD_REWARD_GEN_JOB_FAIL",
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: fmt.Sprintf("Added %d reward generation jobs", len(gds)),
	}
}

func SubmitJoinGamification(acc *model.Account, gamificationID int64) *common.APIResponse {
	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "GET_INFO_FAIL",
		}
	}
	qGamification := model.GamificationDB.QueryOne(&model.Gamification{GamificationID: gamificationID})
	if qGamification.Status != common.APIStatus.Ok {
		return qGamification
	}
	g := qGamification.Data.([]*model.Gamification)[0]
	if g.RequireSubmit == nil || (g.RequireSubmit != nil && !*g.RequireSubmit) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Gamification not require submit",
			ErrorCode: "NOT_REQUIRE_SUBMIT",
		}
	}
	now := time.Now()
	if g.EndTime != nil && now.After(*g.EndTime) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chương trình đã kết thúc, không thể tham gia",
			ErrorCode: "NOT_STARTED",
		}
	}
	qGamificationCustomer := model.GamificationCustomerDB.QueryOne(&model.GamificationCustomer{
		CustomerID:     customer.CustomerID,
		GamificationID: gamificationID,
		Status:         "INACTIVE",
	})
	if qGamificationCustomer.Status == common.APIStatus.Ok {
		return model.GamificationCustomerDB.UpdateOne(&model.GamificationCustomer{
			CustomerID:     customer.CustomerID,
			GamificationID: gamificationID,
		}, model.GamificationCustomer{Status: "ACTIVE"})
	}
	return model.GamificationCustomerDB.Create(
		model.GamificationCustomer{
			Code:           model.GenCodeWithTime(),
			CustomerID:     customer.CustomerID,
			GamificationID: gamificationID,
			JoinType:       "CUSTOMER_SUBMIT",
			JoinTime:       &now,
			Status:         "ACTIVE",
		})
}

func GamificationCustomerList(input *model.GamificationCustomer, offset, limit int64, getTotal bool) *common.APIResponse {

	// if input.SystemDisplay == "" {
	// 	input.SystemDisplay = enum.SystemDisplay.Buymed
	// }

	res := model.GamificationCustomerDB.Query(input, offset, limit, &primitive.M{"_id": -1})
	if res.Status != common.APIStatus.Ok {
		return res
	}
	if getTotal {
		countResult := model.GamificationCustomerDB.Count(input)
		res.Total = countResult.Total
	}
	return res
}

func AddListCustomerIntoGamification(gamificationId int64, customerIds []int64, luckyWheelCode string, status string) *common.APIResponse {
	now := time.Now()
	news := make([]*model.GamificationCustomer, 0)
	qGamification := model.GamificationDB.QueryOne(&model.Gamification{GamificationID: gamificationId})
	if qGamification.Status != common.APIStatus.Ok {
		return qGamification
	}

	for i, customerId := range customerIds {
		item := &model.GamificationCustomer{
			CustomerID:     customerId,
			GamificationID: gamificationId,
			LuckyWheelCode: luckyWheelCode,
			Status:         status,
		}
		item.Code = model.GenCodeWithTime(i)
		item.JoinTime = &now
		if item.JoinType == "" {
			item.JoinType = "ADD_BY_STAFF"
		}
		if item.Status == "" {
			item.Status = "INACTIVE"
		}
		news = append(news, item)
		if len(news) >= 500 {
			res := model.GamificationCustomerDB.CreateMany(news)
			if res.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    common.APIStatus.Existed,
					ErrorCode: "CUSTOMER_EXISTED",
					Message:   "Customer existed",
				}
			}
			news = make([]*model.GamificationCustomer, 0)
		}
	}
	if len(news) > 0 {
		res := model.GamificationCustomerDB.CreateMany(news)
		if res.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Existed,
				ErrorCode: "CUSTOMER_EXISTED",
				Message:   "Customer existed",
			}
		}
		return res
	}
	return &common.APIResponse{
		Status:    common.APIStatus.Existed,
		ErrorCode: "CUSTOMER_EXISTED",
		Message:   "Customer existed",
	}
}

func UpdateGamificationCustomerStatus(input model.GamificationCustomer) *common.APIResponse {
	if input.Code == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing code",
			ErrorCode: "MISSING_CODE",
		}
	}
	if qCheckExist := model.GamificationCustomerDB.QueryOne(&model.GamificationCustomer{
		Code: input.Code,
	}); qCheckExist.Status != common.APIStatus.Ok {
		return qCheckExist
	}
	return model.GamificationCustomerDB.UpdateOne(&model.GamificationCustomer{
		Code: input.Code,
	}, &model.GamificationCustomer{
		Status: input.Status,
	})
}

func DeleteGamificationCustomer(code string) *common.APIResponse {
	if code == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing code",
			ErrorCode: "MISSING_CODE",
		}
	}
	if qCheckExist := model.GamificationCustomerDB.QueryOne(&model.GamificationCustomer{
		Code: code,
	}); qCheckExist.Status != common.APIStatus.Ok {
		return qCheckExist
	}
	return model.GamificationCustomerDB.Delete(&model.GamificationCustomer{
		Code: code,
	})
}

func validateRewardConditions(input *model.Gamification) error {
	var numDaysResultCal = 0
	if input.NumberOfDayCalResult != nil {
		numDaysResultCal = *input.NumberOfDayCalResult
	}
	enableTime := input.EndTime.AddDate(0, 0, numDaysResultCal)
	if time.Now().Before(enableTime) {
		return fmt.Errorf("can not generate reward before %s", enableTime.Format("2006-01-02 15:04:05"))
	}
	return nil
}

// queryReadyGamification will query ready gamification with reward status is empty or ready
// then set status to processing
func queryReadyGamification(input model.Gamification) *common.APIResponse {
	gamResp := model.GamificationDB.QueryOne(
		bson.M{
			"gamification_id": input.GamificationID,
			"$or": []model.Gamification{
				{RewardStatus: enum.RewardProgress.READY},
				{RewardStatus: ""},
			},
		},
	)

	if gamResp.Status != common.APIStatus.Ok || gamResp.Data == nil {
		return gamResp
	}

	// Set reward status to processing immediately to avoid duplicate reward generation
	statusResp := model.GamificationDB.UpdateOne(
		&model.Gamification{
			GamificationID: input.GamificationID,
		},
		&model.Gamification{
			RewardStatus: enum.RewardProgress.PROCESSING,
		},
	)

	if statusResp.Status != common.APIStatus.Ok {
		return statusResp
	}

	return gamResp
}

func addRewardGenJob(
	acc model.Account,
	g model.Gamification,
	detail model.GamificationDetail,
) error {
	if detail.Reward == nil {
		return fmt.Errorf("could not find reward")
	}

	gamiResults := getGamificationRewardJobResults(acc, g, detail)

	// completedCount can change when voucher is splited
	completedCount := 0
	for _, gResult := range gamiResults {
		if gResult != nil && gResult.Status == enum.GamificationResultStatus.COMPLETED {
			completedCount++
		}
	}
	completedResultsCount := completedCount

	pushCount := 0
	for _, gResult := range gamiResults {
		err := gResult.canPushRewardGenJob()
		if err != nil {
			continue
		}
		gResult.CompletedResultsCount = completedResultsCount
		pushCount, _ = gResult.pushRewardGenJobs(&completedCount, pushCount)
	}
	return nil
}

func getGamificationRewardJobResults(
	acc model.Account,
	g model.Gamification,
	gamiDetail model.GamificationDetail) []*GamiJobResult {
	offset := int64(0)
	limit := int64(1000)
	gamiResults := make([]*model.GamificationResult, 0)
	for {
		gamiResultResp := model.GamificationResultDB.Query(&model.GamificationResult{
			GamificationDetailID: gamiDetail.GamificationDetailID,
		}, offset*limit, limit, nil)
		offset++

		if gamiResultResp.Status != common.APIStatus.Ok {
			break
		}

		gamiResults = append(gamiResults, gamiResultResp.Data.([]*model.GamificationResult)...)
	}
	GamiJobResults := make([]*GamiJobResult, 0)
	for _, gamiResult := range gamiResults {
		gamiJobResult := &GamiJobResult{
			GamificationResult: gamiResult,
		}
		err := gamiJobResult.setGamiDetailReward(&gamiDetail)
		gamiJobResult.Acc = acc
		gamiJobResult.Gami = g
		if err != nil {
			continue
		}
		GamiJobResults = append(GamiJobResults, gamiJobResult)
	}
	return GamiJobResults
}

type Mission struct {
	Type                 string      `json:"type" validate:"required"`
	Types                []string    `json:"types"`
	Action               string      `json:"action" validate:"omitempty"`
	CustomerID           int64       `json:"customerID"`
	CustomerPhone        string      `json:"phone"`
	Source               string      `json:"source"`
	AccountID            int64       `json:"accountID"`
	Tags                 []string    `json:"tags"`
	Data                 interface{} `json:"data"`
	Key                  string      `json:"key" validate:"required"`
	GamificationDetailID int64       `json:"gamificationDetailID"`
	LuckyWheelCode       string      `json:"luckyWheelCode"`
	GamificationCode     string      `json:"gamificationCode"`
	SystemDisplay        string      `json:"systemDisplay"`
}

type ReScoreMission struct {
	GamificationDetailID int64  `json:"gamificationDetailID" bson:"gamification_detail_id"`
	CustomerID           int64  `json:"customerId" bson:"customer_id"`
	LuckyWheelCode       string `json:"luckyWheelCode" bson:"lucky_wheel_code"`
	GamificationCode     string `json:"gamificationCode" bson:"gamification_code"`
}

func PushGamificationScore(mission *Mission) *common.APIResponse {
	if len(mission.Types) > 0 {
		for _, t := range mission.Types {
			mission.Type = t
			err := model.ScoreGamificationJob.Push(mission, &job.JobItemMetadata{
				Keys:  []string{mission.Type, fmt.Sprintf("%d", mission.CustomerID)},
				Topic: "default",
			})
			if err != nil {
				fmt.Println(err.Error())
			}
		}
	} else {
		readyTime := time.Now()
		if mission.Source == "CIRCA" {
			if mission.Type == "SHARE" {
				mission.Key = fmt.Sprintf("PHONE_%s_SHARED", mission.CustomerPhone)
				readyTime = readyTime.Add(time.Second * 60)
			}
			if mission.Type == "DISCOVER" {
				readyTime = readyTime.Add(time.Second * 60)
				mission.Key = fmt.Sprintf("PHONE_%s_DISCOVERED", mission.CustomerPhone)
			}
		} else {
			if mission.Type == "SHARE" {
				mission.Key = fmt.Sprintf("ACCOUNT_%d_SHARED", mission.AccountID)
				readyTime = readyTime.Add(time.Second * 3)
			}
			if mission.Type == "DISCOVER" {
				mission.Key = fmt.Sprintf("ACCOUNT_%d_DISCOVERED", mission.AccountID)
			}
		}
		err := model.ScoreGamificationJob.Push(mission, &job.JobItemMetadata{
			Keys:      []string{mission.Type, fmt.Sprintf("%d", mission.CustomerID)},
			Topic:     "default",
			ReadyTime: &readyTime,
		})
		if err != nil {
			fmt.Println(err.Error())
		}
	}
	//getMission(mission).Score()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "",
	}
}

func PushGamificationReScore(mission *ReScoreMission) *common.APIResponse {
	err := model.ReScoreGamificationJob.Push(mission, &job.JobItemMetadata{
		Keys:  []string{mission.GamificationCode, mission.LuckyWheelCode},
		Topic: "default",
	})
	if err != nil {
		fmt.Println(err.Error())
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "",
	}
}

func SyncCustomerJoinGamification(gamificationCode string) *common.APIResponse {
	join, completed := 0, 0
	qDetail := model.GamificationDetailDB.Query(&model.GamificationDetail{
		GamificationCode: gamificationCode,
	}, 0, 0, nil)
	if qDetail.Status != common.APIStatus.Ok {
		return qDetail
	}

	queryCompleted := model.GamificationResult{
		Status: enum.GamificationResultStatus.COMPLETED,
	}

	qJoin := model.GamificationResultDB.Distinct(&model.GamificationResult{GamificationCode: gamificationCode}, "customer_id")
	if qJoin.Status == common.APIStatus.Ok {
		join = len(qJoin.Data.([]interface{}))
	}

	mapCustomerComplete := make(map[int64][]string) // map[customerID][]string
	for _, detail := range qDetail.Data.([]*model.GamificationDetail) {
		queryCompleted.GamificationDetailID = detail.GamificationDetailID
		qCompleted := model.GamificationResultDB.Distinct(queryCompleted, "customer_id")
		if qCompleted.Status == common.APIStatus.Ok {
			for _, customerID := range qCompleted.Data.([]interface{}) {
				if _, ok := mapCustomerComplete[customerID.(int64)]; !ok {
					mapCustomerComplete[customerID.(int64)] = []string{}
				}
				mapCustomerComplete[customerID.(int64)] = append(mapCustomerComplete[customerID.(int64)], detail.GamificationDetailCode)
			}
		}
	}
	// complete all mission then increase completed
	for _, v := range mapCustomerComplete {
		if len(v) == len(qDetail.Data.([]*model.GamificationDetail)) {
			completed++
		}
	}
	return model.GamificationDB.UpdateOne(&model.Gamification{GamificationCode: gamificationCode}, model.Gamification{NumberOfJoinedCustomer: utils.ParseIntToPointer(join), NumberOfCompletedCustomer: utils.ParseIntToPointer(completed)})
}

func UpdateGamificationResultF(data *model.GamificationResult) *common.APIResponse {
	if data.GamificationResultCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing id",
			ErrorCode: "MISSING_ID",
		}
	}
	return model.GamificationResultDB.UpdateOne(&model.GamificationResult{
		GamificationResultCode: data.GamificationResultCode,
	}, data)
}

func MigrateGamificationType() {
	start := time.Now()
	fmt.Println("Migrate gamification start")
	defer func() {
		fmt.Println("Migrate gamification end", time.Since(start))
	}()

	// get customer list
	var offset, limit int64 = 0, 100

	var _id primitive.ObjectID
	gamificationList := []*model.Gamification{}

	for {
		query := bson.M{}

		if _id != primitive.NilObjectID {
			query["_id"] = bson.M{
				"$lt": _id,
			}
		}

		queryRes := model.GamificationDB.Query(query, offset, limit, &primitive.M{"_id": -1})
		if queryRes.Status != common.APIStatus.Ok {
			break
		}

		for _, gamification := range queryRes.Data.([]*model.Gamification) {
			gamificationList = append(gamificationList, gamification)
			_id = gamification.ID
		}
	}

	for _, gami := range gamificationList {
		if gami.Type == "" {
			model.GamificationDB.UpdateOne(model.Gamification{
				GamificationCode: gami.GamificationCode,
			}, model.Gamification{
				Type: enum.GamificationType.GAMIFICATION_MISSION,
			})
		}
	}
}
