package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

func GetCheckinConfigByCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	code := req.GetParam("code")
	if code == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "code is required",
			ErrorCode: "CODE_REQUIRED",
		})
	}

	return resp.Respond(action.GetCheckinConfig(code))
}

func GetCheckinConfigList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	// fill query
	query := model.CheckinConfig{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	return resp.Respond(action.GetCheckinConfigList(&query, offset, limit, getTotal))
}

func GetCheckinItemList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	// fill query
	query := model.CheckinItem{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	return resp.Respond(action.GetCheckinItemList(&query, offset, limit, getTotal))
}

func UpdateCheckinConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CheckinConfig
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}

	return resp.Respond(action.UpdateCheckinConfig(&input))
}

func UpdateCheckinItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CheckinItem
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}

	return resp.Respond(action.UpdateCheckinItem(&input))
}

func CreateCheckinConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CheckinConfig
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}

	return resp.Respond(action.CreateCheckinConfig(&input))
}

func CreateCheckinItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CheckinItem
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data, " + err.Error(),
		})
	}

	return resp.Respond(action.CreateCheckinItem(&input))
}

func GetSelfCheckin(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GetSelfCheckin(getActionSource(req)))
}

func GetDailyCheckinLog(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit  = sdk.ParseInt64(req.GetParam("limit"), 20)
		q      = req.GetParam("q")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.CheckinLog{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetDailyCheckinLog(acc, &query, offset, limit, true))
	}

	return resp.Respond(&common.APIResponse{
		Status: common.APIStatus.Invalid,
	})
}

func SelfCheckin(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload struct {
		Code string `json:"code"`
	}
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data, " + err.Error(),
		})
	}
	return resp.Respond(action.SelfCheckin(getActionSource(req), payload.Code, ""))
}
