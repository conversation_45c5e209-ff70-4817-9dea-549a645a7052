package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type GamificationDetail struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	GamificationCode string `json:"gamificationCode,omitempty" bson:"gamification_code,omitempty"`
	GamificationID   int64  `json:"gamificationID,omitempty" bson:"gamification_id,omitempty"`

	LuckyWheelCode string `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`

	GamificationDetailCode string `json:"gamificationDetailCode,omitempty" bson:"gamification_detail_code,omitempty"`
	GamificationDetailID   int64  `json:"gamificationDetailID,omitempty" bson:"gamification_detail_id,omitempty"`

	Condition    *GamificationDetailCondition `json:"condition,omitempty" bson:"condition,omitempty"`
	Reward       *GamificationDetailReward    `json:"reward,omitempty" bson:"reward,omitempty"`
	Rewards      []*GamificationDetailReward  `json:"rewards,omitempty" bson:"rewards,omitempty"`
	IsActive     *bool                        `json:"isActive,omitempty" bson:"is_active,omitempty"`
	IndexMission *int64                       `json:"indexMission,omitempty" bson:"index_mission,omitempty"`

	Result *GamificationDetailResult `json:"result,omitempty" bson:"-"`

	StartTime   *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime     *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	MissionName *string    `json:"missionName,omitempty" bson:"mission_name,omitempty"`

	OrderTimeType string             `json:"orderTimeType,omitempty" bson:"order_time_type,omitempty"` // CREATED_TIME, COMPLETED_TIME
	SettingTime   *SettingTimeDetail `json:"settingTime,omitempty" bson:"setting_time,omitempty"`

	RewardDescription          *string `json:"rewardDescription,omitempty" bson:"reward_description,omitempty"`
	ParentGamificationDetailID *int64  `json:"parentGamificationDetailID,omitempty" bson:"parent_gamification_detail_id,omitempty"` // required parent completed

	ProcessInfos      []*MissionProcessInfo `json:"processInfos,omitempty" bson:"-"`
	IsBlocked         bool                  `json:"isBlocked,omitempty" bson:"-"`
	ParentMissionName string                `json:"parentMissionName,omitempty" bson:"-"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

type SettingTimeDetail struct {
	TypeTimeMission string `json:"typeTimeMission,omitempty" bson:"type_time_mission,omitempty"` // MONTH, QUARTER
	Quarter         *int   `json:"quarter,omitempty" bson:"quarter,omitempty"`                   // 1, 2, 3, 4
}

type MissionProcessInfo struct {
	UnitName    string `json:"unitName" bson:"-"`
	Value       int    `json:"value" bson:"-"`
	Target      int    `json:"target" bson:"-"`
	IsCompleted bool   `json:"isCompleted" bson:"-"`
}

type GamificationDetailResult struct {
	Value  int                                `json:"value,omitempty" bson:"value,omitempty"`
	Data   []*GamificationResultData          `json:"data,omitempty" bson:"data,omitempty"`
	Status enum.GamificationResultStatusValue `json:"status,omitempty" bson:"status,omitempty"`
}

type GamificationDetailCondition struct {
	Type        enum.GamificationConditionTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	Target      int                                 `json:"target,omitempty" bson:"target,omitempty"`
	Description string                              `json:"description,omitempty" bson:"description,omitempty"`

	StartTime  *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime    *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	ActionName *string    `json:"actionName,omitempty" bson:"action_name,omitempty"`
	ActionLink *string    `json:"actionLink,omitempty" bson:"action_link,omitempty"`
	RankPoint  *int       `json:"rankPoint,omitempty" bson:"rank_point,omitempty"`

	MinTotalSkuCount *int     `json:"minTotalSkuCount,omitempty" bson:"min_total_sku_count,omitempty"`                                           //
	MinValuePerOrder *int     `json:"minValuePerOrder,omitempty" bson:"min_value_per_order,omitempty" validate:"omitempty,gte=0,lte=1000000000"` //
	MinOrderCount    *int     `json:"minOrderCount,omitempty" bson:"min_order_count,omitempty" validate:"omitempty,gte=0,lte=1000000000"`        //
	MinSkuCount      *int     `json:"minSkuCount,omitempty" bson:"min_sku_count,omitempty" validate:"omitempty,gte=0,lte=1000000000"`            //
	MinSkuPerOrder   *int     `json:"minSkuPerOrder,omitempty" bson:"min_sku_per_order,omitempty" validate:"omitempty,gte=0,lte=1000000000"`     //
	MinTotalValue    *int     `json:"minTotalValue,omitempty" bson:"min_total_value,omitempty" validate:"omitempty,gte=0,lte=1000000000"`        //
	KindOfValue      string   `json:"kindOfValue,omitempty" bson:"kind_of_value,omitempty"`                                                      // ACTUAL || ORDER || ACTUAL_NOT_FEE
	OrderStatuses    []string `json:"orderStatuses,omitempty" bson:"order_statuses,omitempty"`                                                   //
	OrderStatus      string   `json:"orderStatus,omitempty" bson:"order_status,omitempty"`                                                       //
	SocialNetwork    string   `json:"socialNetwork,omitempty" bson:"social_network,omitempty"`                                                   //

	MinTotalSkuCompletedCount *int `json:"minTotalSkuCompletedCount,omitempty" bson:"min_total_sku_completed_count,omitempty"` // condition of DASHBOARD_MISSION

	ProductIDs  *[]int64  `json:"productIds,omitempty" bson:"product_ids,omitempty"`   //
	Skus        *[]string `json:"skus,omitempty" bson:"skus,omitempty"`                //
	SellerCodes *[]string `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"` //
	Tags        *[]string `json:"tags,omitempty" bson:"tags,omitempty"`                //
}

type GamificationDetailReward struct {
	RewardType enum.GamificationRewardType `json:"type,omitempty" bson:"type,omitempty"`

	// case reward is promotion, can add promotion id and value percent (sales percent)
	PromotionId  *int64   `json:"promotionId,omitempty" bson:"promotion_id,omitempty"`
	ValuePercent *float64 `json:"valuePercent,omitempty" bson:"value_percent,omitempty" validate:"omitempty,gte=0,lte=1"`

	// control the maximum discount value of a created voucher
	MaxVoucherDiscount *int `json:"maxVoucherDiscount,omitempty" bson:"max_voucher_discount,omitempty" validate:"omitempty,gte=1,lte=1000000000"`

	// control the max quantity of created vouchers for each gamification result
	MaxVouchersQnt *int `json:"maxVouchersQnt,omitempty" bson:"max_vouchers_qnt,omitempty" validate:"omitempty,gte=1,lte=1000"`

	// case reward is cumulative point, can add absolute point or point percentage
	PointType    enum.GamificaitonRewardPointType `json:"pointType,omitempty" bson:"point_type,omitempty"`
	Point        *int                             `json:"point,omitempty" bson:"point,omitempty" validate:"omitempty,gte=0,lte=10000"`
	PointPercent *float64                         `json:"pointPercent,omitempty" bson:"point_percent,omitempty" validate:"omitempty,gte=0,lte=1"`

	//case reward type is voucher and rotation
	QuantityRotation *int `json:"quantityRotation,omitempty" bson:"quantity_rotation,omitempty"`

	Description string `json:"description,omitempty" bson:"description,omitempty"`

	Points          int64    `json:"points,omitempty" bson:"points,omitempty"`
	TicketPattern   string   `json:"ticketPattern,omitempty" bson:"ticket_pattern,omitempty"`
	VoucherPattern  *string  `json:"voucherPattern,omitempty" bson:"voucher_pattern,omitempty"`
	TurnsRotation   int64    `json:"turnsRotation,omitempty" bson:"turns_rotation,omitempty"`
	LuckyWheelCodes []string `json:"luckyWheelCodes,omitempty" bson:"lucky_wheel_codes,omitempty"`
	PieceCode       string   `json:"pieceCode,omitempty" bson:"piece_code,omitempty"`
	ComboCode       string   `json:"comboCode,omitempty" bson:"combo_code,omitempty"`
}

type RewardVoucherGen struct {
	// case reward is voucher, apply from promotion
	PromotionID int64 `json:"promotionID,omitempty" bson:"promotion_id,omitempty"`

	// case reward is voucher, but calculate by value percent
	Discount int `json:"discount,omitempty" bson:"discount,omitempty"`

	// VoucherCode will be assigned to each generated voucher
	VoucherCode string `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`

	CustomerID           int64     `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	CustomerScope        string    `json:"customerScope,omitempty" bson:"customer_scope,omitempty"`
	EndTime              time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Account              Account   `json:"account,omitempty" bson:"account,omitempty"`
	GamificationDetailID int64     `json:"gamificationDetailID,omitempty" bson:"gamification_detail_id,omitempty"`
	GamiResultID         int64     `json:"gamiResultID,omitempty" bson:"gami_result_id,omitempty"`
	GamiID               int64     `json:"gamiID,omitempty" bson:"gami_id,omitempty"`
	GamiCode             string    `json:"gamiCode,omitempty" bson:"gami_code,omitempty"`

	// CompletedTotal is the current count of total pushed vouchers
	CompletedTotal int `json:"completedTotal,omitempty" bson:"completed_total,omitempty"`

	// CurCount represents the order of job in the gamification results list at the time of execution
	CurCount int `json:"curCount,omitempty" bson:"cur_count,omitempty"`

	// number of completed gamification results
	CompletedResultsCount int `json:"completedResultsCount,omitempty" bson:"completed_results_count,omitempty"`
}

type UserVoucherJob struct {
	Account      *Account       `json:"account,omitempty" bson:"account,omitempty"`
	UserVoucher  *UserPromotion `json:"userVoucher,omitempty" bson:"user_voucher,omitempty"`
	GamiResultID int64          `json:"gamiResultID,omitempty" bson:"gami_result_id,omitempty"`
	GamiCode     string         `json:"gamiCode,omitempty" bson:"gami_code,omitempty"`

	// CompletedTotal is the current count of total pushed vouchers
	CompletedTotal int `json:"completedTotal,omitempty" bson:"completed_total,omitempty"`

	// number of completed gamification results
	CompletedResultsCount int `json:"completedResultsCount,omitempty" bson:"completed_results_count,omitempty"`

	// CurCount represents the order of job in the gamification results list at the time of execution
	CurCount int `json:"curCount,omitempty" bson:"cur_count,omitempty"`
}

var GamificationDetailDB = &db.Instance{
	ColName:        "gamification_detail",
	TemplateObject: &GamificationDetail{},
}

// InitGamificationDetailModel is func init model
func InitGamificationDetailModel(s *mongo.Database) {
	GamificationDetailDB.ApplyDatabase(s)

	// t := true
	// GamificationDetailDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_detail_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// GamificationDetailDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "gamification_detail_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
}
