package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateLKStore is func create lucky wheel store
func CreateLKStore(acc *model.Account, payload *model.LuckyWheelStore) *common.APIResponse {
	// validate payload
	if payload.LuckyWheelCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "LUCKY_WHEEL_CODE_REQUIRED",
			Message:   "Lucky wheel code is required",
		}
	}
	if payload.Name == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "NAME_REQUIRED",
			Message:   "Name is required",
		}
	}
	// generate code && fill default value
	payload.Code = model.GenCodeWithTime()
	payload.CreatedBy = acc.AccountID
	payload.Version = model.GenVersion()
	// create
	return model.LuckyWheelStoreDB.Create(payload)
}

// UpdateLKStore is func update lucky wheel store
func UpdateLKStore(acc *model.Account, payload *model.LuckyWheelStore) *common.APIResponse {
	// validate payload
	if payload.Code == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "CODE_REQUIRED",
			Message:   "Code is required",
		}
	}
	if payload.Version == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "VERSION_REQUIRED",
			Message:   "Version is required",
		}
	}
	// query check exist
	qCheck := model.LuckyWheelStoreDB.QueryOne(model.LuckyWheelStore{
		Code: payload.Code,
	})
	if qCheck.Status != common.APIStatus.Ok {
		return qCheck
	}
	// check version
	if qCheck.Data.([]*model.LuckyWheelStore)[0].Version != payload.Version {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "VERSION_INVALID",
			Message:   "Version is invalid",
		}
	}
	// set version
	curVersion := payload.Version
	payload.Version = model.GenVersion()
	// update & return
	return model.LuckyWheelStoreDB.UpdateOne(model.LuckyWheelStore{Code: payload.Code, Version: curVersion}, payload)
}

// CreateLKStoreItem is func create lucky wheel store item
func CreateLKStoreItem(acc *model.Account, payload *model.LuckyWheelStoreItem) *common.APIResponse {
	// validate payload
	if payload.StoreCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "STORE_CODE_REQUIRED",
			Message:   "Store code is required",
		}
	}
	if payload.StoreItemCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "STORE_ITEM_CODE_REQUIRED",
			Message:   "Store item code is required",
		}
	}
	// generate code && fill default value
	payload.CreatedBy = acc.AccountID
	payload.Version = model.GenVersion()
	// create
	return model.LuckyWheelStoreItemDB.Create(payload)
}

// UpdateLKStoreItem is func update lucky wheel store item
func UpdateLKStoreItem(acc *model.Account, payload *model.LuckyWheelStoreItem) *common.APIResponse {
	// validate payload
	if payload.StoreCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "STORE_CODE_REQUIRED",
			Message:   "Store code is required",
		}
	}
	if payload.StoreItemCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "STORE_ITEM_CODE_REQUIRED",
			Message:   "Store item code is required",
		}
	}
	if payload.Version == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "VERSION_REQUIRED",
			Message:   "Version is required",
		}
	}
	// query check exist
	qCheck := model.LuckyWheelStoreItemDB.QueryOne(model.LuckyWheelStoreItem{
		StoreCode:     payload.StoreCode,
		StoreItemCode: payload.StoreItemCode,
	})
	if qCheck.Status != common.APIStatus.Ok {
		return qCheck
	}
	// check version
	if qCheck.Data.([]*model.LuckyWheelStoreItem)[0].Version != payload.Version {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "VERSION_INVALID",
			Message:   "Version is invalid",
		}
	}
	// set version
	curVersion := payload.Version
	payload.Version = model.GenVersion()
	// update & return
	return model.LuckyWheelStoreItemDB.UpdateOne(model.LuckyWheelStoreItem{
		StoreCode:     payload.StoreCode,
		StoreItemCode: payload.StoreItemCode,
		Version:       curVersion,
	}, payload)
}

// GetLKStore is func get lucky wheel store
func GetLKStore(filter *model.LuckyWheelStore) *common.APIResponse {
	return model.LuckyWheelStoreDB.QueryOne(filter)
}

// GetLKStoreList is func get lucky wheel store list
func GetLKStoreList(filter *model.LuckyWheelStore, offset, limit int64, getTotal bool) *common.APIResponse {
	qLKStore := model.LuckyWheelStoreDB.Query(filter, offset, limit, &primitive.M{"_id": -1})
	if qLKStore.Status != common.APIStatus.Ok {
		return qLKStore
	}
	if getTotal {
		qLKStore.Total = model.LuckyWheelStoreDB.Count(filter).Total
	}
	return qLKStore
}

// GetLKStoreItemList is func get lucky wheel store item list
func GetLKStoreItemList(filter *model.LuckyWheelStoreItem, offset, limit int64, getTotal bool) *common.APIResponse {
	qLKStoreItem := model.LuckyWheelStoreItemDB.Query(filter, offset, limit, &primitive.M{"_id": -1})
	if qLKStoreItem.Status != common.APIStatus.Ok {
		return qLKStoreItem
	}
	if getTotal {
		qLKStoreItem.Total = model.LuckyWheelStoreItemDB.Count(filter).Total
	}
	return qLKStoreItem
}

// GetLKStoreLogList is func get lucky wheel store log list
func GetLKStoreLogList(filter *model.LuckyWheelStoreLog, offset, limit int64, getTotal bool) *common.APIResponse {
	qLKStoreLog := model.LuckyWheelStoreLogDB.Query(filter, offset, limit, &primitive.M{"_id": -1})
	if qLKStoreLog.Status != common.APIStatus.Ok {
		return qLKStoreLog
	}
	if getTotal {
		qLKStoreLog.Total = model.LuckyWheelStoreLogDB.Count(filter).Total
	}
	return qLKStoreLog
}

// GetSelfLKStore is func get self lucky wheel store
func GetSelfLKStore(acc *model.Account, luckyWheelCode string) *common.APIResponse {
	// query store by lucky wheel code
	qStore := model.LuckyWheelStoreDB.QueryOne(model.LuckyWheelStore{LuckyWheelCode: luckyWheelCode})
	if qStore.Status != common.APIStatus.Ok {
		return qStore
	}
	store := qStore.Data.([]*model.LuckyWheelStore)[0]
	// query store item by store code && lucky wheel code
	qStoreItem := model.LuckyWheelStoreItemDB.Query(
		bson.M{
			"store_code":       store.Code,
			"lucky_wheel_code": luckyWheelCode,
			"is_active":        true,
		}, 0, 100, &primitive.M{"display_priority": -1})
	if qStoreItem.Status != common.APIStatus.Ok {
		return qStoreItem
	}
	storeItems := make([]*model.LuckyWheelStoreItem, 0)
	for _, item := range qStoreItem.Data.([]*model.LuckyWheelStoreItem) {
		if item.StartTime != nil && item.StartTime.After(time.Now()) {
			continue
		}
		if item.EndTime != nil && item.EndTime.Before(time.Now()) {
			continue
		}
		storeItems = append(storeItems, item)
	}
	store.StoreItems = storeItems
	qLog := model.LuckyWheelStoreLogDB.Query(model.LuckyWheelStoreLog{
		StoreCode: store.Code,
		AccountID: acc.AccountID,
	}, 0, 0, &primitive.M{"_id": -1})

	if qLog.Status == common.APIStatus.Ok {
		mapLog := make(map[string]int) // key: store item code, value: count
		for _, log := range qLog.Data.([]*model.LuckyWheelStoreLog) {
			mapLog[log.StoreItemCode] = mapLog[log.StoreItemCode] + 1
		}
		for _, item := range store.StoreItems {
			item.ExchangedCount = mapLog[item.StoreItemCode]
			item.CanExchange = item.MaxExchange == nil || (item.MaxExchange != nil && item.ExchangedCount < *item.MaxExchange)
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    []*model.LuckyWheelStore{store},
		Message: "Get lucky wheel store successfully",
	}
}

// ExchangeLKStoreItem is func exchange lucky wheel store item
func ExchangeLKStoreItem(acc *model.Account, luckyWheelCode, storeItemCode string) *common.APIResponse {
	// check account is customer
	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			ErrorCode: "CUSTOMER_NOT_FOUND",
			Message:   "Customer not found",
		}
	}
	// query store item by lucky wheel code & store item code
	qStoreItem := model.LuckyWheelStoreItemDB.QueryOne(model.LuckyWheelStoreItem{
		LuckyWheelCode: luckyWheelCode,
		StoreItemCode:  storeItemCode,
	})
	if qStoreItem.Status != common.APIStatus.Ok {
		return qStoreItem
	}
	storeItem := qStoreItem.Data.([]*model.LuckyWheelStoreItem)[0]
	if storeItem.IsActive == nil || !*storeItem.IsActive {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			ErrorCode: "STORE_ITEM_INACTIVE",
			Message:   "Phần thưởng không còn khả dụng",
		}
	}
	// check max exchange
	queryCheckMaxExchange := bson.M{"account_id": acc.AccountID, "lucky_wheel_code": luckyWheelCode, "point": bson.M{"$gte": storeItem.Point}}
	if storeItem.MaxExchange != nil {

		queryCheckMaxExchange["$or"] = []*bson.M{
			{
				"exchanged_item." + storeItemCode: nil,
			},
			{
				"exchanged_item." + storeItemCode: bson.M{"$lt": *storeItem.MaxExchange},
			},
		}
	}
	qIncrease := model.CustomerLuckyWheelDB.UpdateOneWithOption(
		queryCheckMaxExchange,
		bson.M{"$inc": bson.M{"exchanged_item." + storeItemCode: 1, "point": -storeItem.Point}})
	if qIncrease.Status != common.APIStatus.Ok {
		messageError := "Đổi phần thưởng không thành công"
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "EXCHANGE_ITEM_FAILED",
			Message:   messageError,
		}
	}
	// handle message return
	rewardStrArr := make([]string, 0)
	if storeItem.Rewards != nil {
		for _, reward := range *storeItem.Rewards {
			switch reward.TypeReward {
			case enum.LuckyWheelItemReward.VOUCHER:
				patternVoucher := "GIFT_#"
				if reward.VoucherPattern != nil {
					patternVoucher = *reward.VoucherPattern
				}
				voucherID, voucherCode := model.GenVoucherID()
				voucherCode = strings.ReplaceAll(patternVoucher, "#", voucherCode)
				reward.VoucherCode = voucherCode
				reward.VoucherID = voucherID
				rewardStrArr = append(rewardStrArr, "1 "+reward.RewardDescription+" - "+voucherCode)
			case enum.LuckyWheelItemReward.TURNS, enum.LuckyWheelItemReward.POINTS:
				rewardStrArr = append(rewardStrArr, reward.RewardDescription)
			case enum.LuckyWheelItemReward.OTHER:
				rewardStrArr = append(rewardStrArr, reward.RewardDescription)
			}
		}
	}
	// create log
	model.LuckyWheelStoreLogDB.Create(model.LuckyWheelStoreLog{
		StoreCode:      storeItem.StoreCode,
		LuckyWheelCode: luckyWheelCode,
		StoreItemCode:  storeItemCode,
		StoreItemName:  storeItem.Name,
		DisplayUrl:     storeItem.DisplayUrl,
		CustomerID:     0,
		AccountID:      acc.AccountID,
		CustomerPhone:  "",
		UsedPoint:      storeItem.Point,
		Rewards:        storeItem.Rewards,
		Message:        strings.Join(rewardStrArr, ", "),
	})
	// handle rewards & send notification
	go func() {
		if storeItem.Rewards != nil {
			queryUpdateCustomerLuckyWheel := bson.M{"account_id": acc.AccountID, "lucky_wheel_code": luckyWheelCode}
			for _, reward := range *storeItem.Rewards {
				switch reward.TypeReward {
				case enum.LuckyWheelItemReward.VOUCHER:
					voucherRes := CreateVoucherByPromotionID(nil, customer.CustomerID, reward.PromotionID, reward.VoucherCode, reward.VoucherID, reward.NumberOfDayUseVoucher)
					if voucherRes.Status == common.APIStatus.Ok {
						//voucher := voucherRes.Data.([]*model.Voucher)[0]
					}
				case enum.LuckyWheelItemReward.TURNS:
					model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
						"$inc": bson.M{"quantity": reward.TurnsRotation},
					})
				case enum.LuckyWheelItemReward.TICKET_PATTERN:
					// not implement
				case enum.LuckyWheelItemReward.POINTS:
					errIncrPoint := client.Services.Customer.UpdatePoint(acc.AccountID, reward.Points, "LUCKY_WHEEL")
					if errIncrPoint == nil {
						// todo notify
					}
				case enum.LuckyWheelItemReward.OTHER:

				}
			}
		}
	}()

	client.Services.Notification.CreateNotification(&model.Notification{
		Username:     customer.Username,
		UserID:       customer.AccountID,
		ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
		Topic:        "ANNOUNCEMENT",
		Title:        fmt.Sprintf("Bạn đã dùng %d nơ đỏ và đổi thành công phần thưởng %s !", storeItem.Point, strings.Join(rewardStrArr, ", ")),
		Link:         "#",
		Tags:         []enum.NotificationTagEnum{enum.NotificationTag.PROMOTION, enum.NotificationTag.IMPORTANT},
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Bạn nhận được " + strings.Join(rewardStrArr, ", "),
	}
}
