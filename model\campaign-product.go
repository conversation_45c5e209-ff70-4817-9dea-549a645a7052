package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// DataRaw ...
type DataRaw struct {
	Type string `json:"-" bson:"type,omitempty"`
	Val  string `json:"-" bson:"val,omitempty"`
} // @name DataRaw

type CampaignProduct struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	CampaignID          int64                          `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	CampaignCode        string                         `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	CampaignType        enum.CampaignValueType         `json:"campaignType,omitempty" bson:"campaign_type,omitempty"`
	CampaignProductID   int64                          `json:"campaignProductID,omitempty" bson:"campaign_product_id,omitempty"`
	CampaignProductCode string                         `json:"campaignProductCode,omitempty" bson:"campaign_product_code,omitempty"`
	ProductID           int64                          `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode         string                         `json:"productCode,omitempty" bson:"product_code,omitempty"`
	Sku                 string                         `json:"sku,omitempty" bson:"sku,omitempty"`
	SellerCode          string                         `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	Price               int64                          `json:"price,omitempty" bson:"price,omitempty"`
	SalePrice           int64                          `json:"salePrice,omitempty" bson:"sale_price,omitempty"`
	CampaignPrice       *int64                         `json:"campaignPrice,omitempty" bson:"campaign_price,omitempty"`
	PercentageDiscount  *int64                         `json:"percentageDiscount,omitempty" bson:"percentage_discount,omitempty"`
	AbsoluteDiscount    *int64                         `json:"absoluteDiscount,omitempty" bson:"absolute_discount,omitempty"`
	MaxDiscount         *int64                         `json:"maxDiscount,omitempty" bson:"max_discount,omitempty"`
	Quantity            *int64                         `json:"quantity,omitempty" bson:"quantity,omitempty"`
	SoldQuantity        *int64                         `json:"soldQuantity,omitempty" bson:"sold_quantity,omitempty"`
	MaxQuantityPerOrder *int64                         `json:"maxQuantityPerOrder,omitempty" bson:"max_quantity_per_order,omitempty"`
	IsActive            *bool                          `json:"isActive,omitempty" bson:"is_active,omitempty"`
	SaleType            enum.CampaignSaleValueType     `json:"saleType,omitempty" bson:"sale_type,omitempty"`
	StartTime           time.Time                      `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime             time.Time                      `json:"endTime,omitempty" bson:"end_time,omitempty"`
	FlashSaleTime       []string                       `json:"flashSaleTimeRefs,omitempty" bson:"flash_sale_time_refs,omitempty"` // ref code to campaign
	Status              enum.CampaignProductStatusType `json:"status,omitempty" bson:"status,omitempty"`
	CancelReason        string                         `json:"cancelReason,omitempty" bson:"cancel_reason,omitempty"`
	ChargeFee           string                         `json:"chargeFee,omitempty" bson:"charge_fee,omitempty"` // MARKETING . SELLER
	Version             string                         `json:"version,omitempty" bson:"version,omitempty"`
	PrivateNote         string                         `json:"privateNote,omitempty" bson:"private_note,omitempty"`
	StatusPriority      int                            `json:"statusPriority,omitempty" bson:"status_priority,omitempty"` // cache only
	Priority            *int                           `json:"priority,omitempty" bson:"priority,omitempty"`              // sort
	Note                string                         `json:"-" bson:"note,omitempty"`
	FlashSaleTimes      []*CampaignFlashSaleTimeItem   `json:"-" bson:"flash_sale_time,omitempty"` // to check flash sale time for cart
	HashTag             string                         `json:"-" bson:"hash_tag,omitempty"`
	UniqueSkuActive     *string                        `json:"-" bson:"unique_sku_active,omitempty"`
	ComplexQuery        []*bson.M                      `json:"-" bson:"$and,omitempty"`
	Campaign            *Campaign                      `json:"-" bson:"campaign,omitempty"`
	//FilterRaw           []*DataRaw                     `json:"-" bson:"filter_raw,omitempty"`

	Filters   []string   `json:"-" bson:"filter,omitempty"`
	FilterRaw []*DataRaw `json:"-" bson:"filter_raw,omitempty"` // old field -> todo remove

	SystemDisplay string `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`

	MaxQuantityPerCustomer *int64 `json:"maxQuantityPerCustomer,omitempty" bson:"max_quantity_per_customer,omitempty"`
	ApprovedTicketID       *int64 `json:"approvedTicketID,omitempty" bson:"approved_ticket_id,omitempty"`
}

var CampaignProductDB = &db.Instance{
	ColName:        "campaign_product",
	TemplateObject: &CampaignProduct{},
}

var CampaignProductCacheDB = &db.Instance{
	ColName:        "campaign_product_v2",
	TemplateObject: &CampaignProduct{},
}

// InitCampaignProductModel is func init model
func InitCampaignProductModel(dbInst *db.Instance, s *mongo.Database) {
	dbInst.ApplyDatabase(s)
	// Add indexes in `promotion-worker` source code
}

// InitCampaignProductCacheModel is func init model
func InitCampaignProductCacheModel(dbInst *db.Instance, s *mongo.Database) {
	dbInst.ApplyDatabase(s)
	// Add indexes in `promotion-worker` source code
}
