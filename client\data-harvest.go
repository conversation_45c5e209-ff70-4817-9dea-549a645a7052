package client

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/mongo"
)

type dataHarvestClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewDataHarvestClient(apiHost, apiKey, logName string, s *mongo.Database) *dataHarvestClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}

	cli := &dataHarvestClient{
		svc: client.NewRESTClient(
			apiHost,
			logName,
			DEFAULT_TIMEOUT,
			DEFAULT_RETRY_TIME,
			DEFAULT_WAIT_TIME,
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	// cli.svc.SetDBLog(s)
	return cli
}

// GetCustomerExperience is get customer experience
func (cli *dataHarvestClient) GetCustomerExperience(customerId, accountId int64) (*model.CustomerExperience, error) {
	params := map[string]string{
		//"customerId": fmt.Sprintf("%d", customerId),
		"accountId": fmt.Sprintf("%d", accountId),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/data-harvest/customer-report/v1/customer-experience", nil)
	if err != nil {
		return nil, err
	}
	var result model.CustomerExperienceResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}
	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("error: %s", result.Message)
	}
	return result.Data[0], nil
}
