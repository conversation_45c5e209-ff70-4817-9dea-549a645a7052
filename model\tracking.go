package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type Tracking struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Source      string `json:"source,omitempty" bson:"source,omitempty" validate:"required"`
	Page        string `json:"page,omitempty" bson:"page,omitempty"`
	CurrentPage string `json:"currentPage,omitempty" bson:"current_page,omitempty"`
	Screen      string `json:"screen,omitempty" bson:"screen,omitempty"`
	EventAction string `json:"eventAction,omitempty" bson:"event_action,omitempty" validate:"required"`
	EventLabel  string `json:"eventLabel,omitempty" bson:"event_label,omitempty"`

	AccountID  int64 `json:"accountId,omitempty" bson:"account_id,omitempty" validate:"required"`
	CustomerID int64 `json:"customerId,omitempty" bson:"customer_id,omitempty" validate:"required"`

	TimeByYear  int        `json:"timeByYear,omitempty" bson:"time_by_year,omitempty"`
	TimeByMonth time.Month `json:"timeByMonth,omitempty" bson:"time_by_month,omitempty"`
	TimeByDay   int        `json:"timeByDay,omitempty" bson:"time_by_day,omitempty"`
	TimeByHour  int        `json:"timeByHour,omitempty" bson:"time_by_hour,omitempty"`

	HashTag string `json:"-" bson:"hash_tag,omitempty"`

	StatisticBy string `json:"statisticBy,omitempty" bson:"-"`
}

type TrackingEvent struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	EventAction string `json:"eventAction,omitempty" bson:"event_action,omitempty" validate:"required"`
	EventLabel  string `json:"eventLabel,omitempty" bson:"event_label,omitempty" validate:"required"`
	HashTag     string `json:"-" bson:"hash_tag,omitempty"`
}

var TrackingDB = &db.Instance{
	ColName:        "tracking",
	TemplateObject: &Tracking{},
}

// InitTrackingModel is func init model
func InitTrackingModel(s *mongo.Database) {
	TrackingDB.ApplyDatabase(s)
}

var TrackingEventDB = &db.Instance{
	ColName:        "tracking_event",
	TemplateObject: &TrackingEvent{},
}

// InitTrackingEventModel is func init model
func InitTrackingEventModel(s *mongo.Database) {
	TrackingEventDB.ApplyDatabase(s)
}
