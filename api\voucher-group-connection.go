package api

import (
	"encoding/json"

	"github.com/go-playground/validator/v10"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

func GetVoucherGroupConnectionsList(req sdk.APIRequest, res sdk.APIResponder) error {
	q := &model.VoucherGroupConnectionQuery{}
	if req.GetParam("q") != "" {
		err := json.Unmarshal([]byte(req.GetParam("q")), q)
		if err != nil {
			return err
		}
	}

	q.Offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	q.Limit = int64(sdk.ParseInt(req.GetParam("limit"), 20))
	q.GetTotal = req.GetParam("getTotal") == "true"
	q.SearchText = req.GetParam("search")

	if q.VoucherGroupCode != "" {
		return res.Respond(action.GetVoucherGroupConnectionsListByGroupCode(q))
	}

	return res.Respond(action.GetVoucherGroupConnectionsList(q))
}

func GetVoucherGroupConnection(req sdk.APIRequest, res sdk.APIResponder) error {
	q := &model.VoucherGroupConnectionQuery{}
	err := json.Unmarshal([]byte(req.GetParam("q")), q)

	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "CANNOT_PARSE_QUERY_OR_QUERY_NOT_FOUND",
		})
	}

	validateResp := validateVoucherGroupConnectionQuery(q)

	if validateResp != nil {
		return res.Respond(validateResp)
	}

	return res.Respond(action.GetVoucherGroupConnection(q))
}

func validateVoucherGroupConnectionQuery(data *model.VoucherGroupConnectionQuery) *common.APIResponse {
	if data == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Query data not found",
			ErrorCode: "QUERY_NOT_FOUND",
		}
	}

	if data.ConnectionId != 0 || data.ConnectionCode != "" {
		return nil
	}

	validate := validator.New()
	err := validate.Struct(data)

	if err != nil {
		return validatorErrorResponse(err)
	}

	return nil
}

func CreateVoucherGroupConnection(req sdk.APIRequest, res sdk.APIResponder) error {
	var body model.VoucherGroupConnection
	err := req.GetContent(&body)

	if err != nil {
		return err
	}

	validateRes := validateVoucherGroupConnection(&body)

	if validateRes != nil {
		return res.Respond(validateRes)
	}

	if acc := getActionSource(req); acc != nil {
		body.CreatedBy = acc.AccountID
		return res.Respond(action.CreateVoucherGroupConnection(&body))
	}

	return res.Respond(unauthorizedResponse())
}

func UpdateVoucherGroupConnection(req sdk.APIRequest, res sdk.APIResponder) error {
	content := model.VoucherGroupConnection{}

	err := req.GetContent(&content)

	if err != nil {
		return err
	}

	query := &model.VoucherGroupConnectionQuery{
		ConnectionId:   content.ConnectionId,
		ConnectionCode: content.ConnectionCode,
	}

	queryValidationRes := validateVoucherGroupConnectionQuery(query)

	if queryValidationRes != nil {
		return res.Respond(queryValidationRes)
	}

	if acc := getActionSource(req); acc != nil {
		content.UpdatedBy = acc.AccountID
		return res.Respond(action.UpdateVoucherGroupConnection(query, &content))
	}

	return res.Respond(unauthorizedResponse())
}

func validateVoucherGroupConnection(data *model.VoucherGroupConnection) *common.APIResponse {

	validate := validator.New()
	err := validate.Struct(data)

	if err != nil {
		return validatorErrorResponse(err)
	}

	return nil
}
