package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateGamification(offset, limit int64) *common.APIResponse {
	// query gamification with field system display is empty
	query := &model.Gamification{
		ComplexQuery: []*bson.M{
			{"system_display": primitive.M{"$exists": false}},
		},
	}

	gameResp := model.GamificationDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if gameResp.Status != common.APIStatus.Ok {
		return gameResp
	}
	games := gameResp.Data.([]*model.Gamification)
	for _, game := range games {
		game.SystemDisplay = enum.SystemDisplay.Buymed
		updateGame := model.GamificationDB.UpdateOne(model.Gamification{ID: game.ID}, game)
		if updateGame.Status != common.APIStatus.Ok {
			fmt.Println("Update game error: ", updateGame.Message)
			return updateGame
		}
	}
	return &common.APIResponse{Status: common.APIStatus.Ok, Data: games}
}

func RemoveVoucherCacheByCode(voucherCode string) *common.APIResponse {
	if len(voucherCode) <= 0 {
		return &common.APIResponse{Status: common.APIStatus.Invalid, Message: "Voucher code is required"}
	}

	return model.VoucherCacheDB.Delete(bson.M{"code": voucherCode})
}
