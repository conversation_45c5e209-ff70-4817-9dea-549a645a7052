This library was authored by <PERSON>, and contains contributions from:

v<PERSON><PERSON><PERSON> (regex support)
i<PERSON><PERSON> (ternary operator)
oxtoaca<PERSON> (parameter structures, deferred parameter retrieval)
wmiller848 (bitwise operators)
prashantv (optimization of bools)
dpaolella (exposure of variables used in an expression)
benpaxton (fix for missing type checks during literal elide process)
abrander (panic-finding testing tool, float32 conversions)
xfennec (fix for dates being parsed in the current Location)
bgaifullin (lifting restriction on complex/struct types)
gautambt (hexadecimal literals)
felixonmars (fix multiple typos in test names)
sambonfire (automatic type conversion for accessor function calls)