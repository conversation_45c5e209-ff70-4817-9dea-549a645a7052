package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ComboReward struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code           string  `json:"code,omitempty" bson:"code,omitempty"`
	Description    *string `json:"description,omitempty" bson:"description,omitempty"`
	IsActive       *bool   `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Name           string  `json:"name,omitempty" bson:"name,omitempty"`
	LuckyWheelCode string  `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	VersionUpdate  string  `json:"versionUpdate,omitempty" bson:"version_update,omitempty"`

	StartTime *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`

	ComboType *enum.ComboRewardEnumType `json:"comboType,omitempty" bson:"combo_type,omitempty"`

	ImageUrl string              `json:"imageUrl,omitempty" bson:"image_url,omitempty"`
	Pieces   []*ComboRewardPiece `json:"pieces,omitempty" bson:"-"`
	Rewards  *[]*ComboRewardItem `json:"rewards,omitempty" bson:"rewards,omitempty"`

	MaxQuantity            *int64 `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`
	MaxQuantityPerDay      *int64 `json:"maxQuantityPerDay,omitempty" bson:"max_quantity_per_day,omitempty"`
	MaxQuantityPerCustomer *int64 `json:"maxQuantityPerCustomer,omitempty" bson:"max_quantity_per_customer,omitempty"`
	UsedQuantity           int64  `json:"usedQuantity,omitempty" bson:"used_quantity,omitempty"`
	ShowPriority           int64  `json:"showPriority,omitempty" bson:"show_priority,omitempty"`

	TitleReward string `json:"titleReward,omitempty" bson:"title_reward,omitempty"`

	//query
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`

	// Response to customer only
	AbleExchange                   *bool `json:"ableExchange,omitempty" bson:"-"`
	IsMaxQuantityExceed            *bool `json:"isMaxQuantityExceed,omitempty" bson:"-"`
	IsMaxQuantityPerCustomerExceed *bool `json:"isMaxQuantityPerCustomerExceed,omitempty" bson:"-"`
	IsComplete                     *bool `json:"isComplete,omitempty" bson:"-"`
}

type ComboRewardPiece struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	ComboCode string `json:"comboCode,omitempty" bson:"combo_code,omitempty"`
	PieceCode string `json:"pieceCode,omitempty" bson:"piece_code,omitempty"`
	Position  int    `json:"position,omitempty" bson:"position,omitempty"`

	PieceName      string `json:"pieceName,omitempty" bson:"piece_name,omitempty"`
	ImageUrl       string `json:"imageUrl,omitempty" bson:"image_url,omitempty"`
	ExpectQuantity int64  `json:"expectQuantity,omitempty" bson:"expect_quantity,omitempty"`

	//query
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`

	// Response to customer only
	CollectedQuantity int64 `json:"collectedQuantity" bson:"-"`
}

type ComboRewardItem struct {
	TypeReward            enum.ComboRewardItemType `json:"typeReward,omitempty" bson:"type_reward,omitempty"`
	RewardDescription     string                   `json:"rewardDescription,omitempty" bson:"reward_description,omitempty"`
	Points                int64                    `json:"points,omitempty" bson:"points,omitempty"`
	PromotionID           int64                    `json:"promotionID,omitempty" bson:"promotion_id,omitempty"`
	TicketPattern         string                   `json:"ticketPattern,omitempty" bson:"ticket_pattern,omitempty"`
	VoucherPattern        *string                  `json:"voucherPattern,omitempty" bson:"voucher_pattern,omitempty"`
	TurnsRotation         int64                    `json:"turnsRotation,omitempty" bson:"turns_rotation,omitempty"`
	VoucherID             int64                    `json:"voucherID,omitempty" bson:"-"`
	VoucherCode           string                   `json:"voucherCode,omitempty" bson:"-"`
	PieceCode             string                   `json:"pieceCode,omitempty" bson:"piece_code,omitempty"`
	ComboCode             string                   `json:"comboCode,omitempty" bson:"combo_code,omitempty"`
	NumberOfDayUseVoucher *int64                   `json:"numberOfDayUseVoucher,omitempty" bson:"number_of_day_use_voucher,omitempty"`
}

var ComboRewardDB = &db.Instance{
	ColName:        "combo_reward",
	TemplateObject: &ComboReward{},
}

// InitComboRewardModel is func init model
func InitComboRewardModel(s *mongo.Database) {
	ComboRewardDB.ApplyDatabase(s)
	// t := true
	// ComboRewardDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
	// ComboRewardDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "lucky_wheel_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

var ComboRewardPieceDB = &db.Instance{
	ColName:        "combo_reward_piece",
	TemplateObject: &ComboRewardPiece{},
}

// InitComboRewardPieceModel is func init model
func InitComboRewardPieceModel(s *mongo.Database) {
	ComboRewardPieceDB.ApplyDatabase(s)
	// t := true
	// ComboRewardPieceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "piece_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
	// ComboRewardPieceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "combo_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

// db.getCollection("combo_reward").ensureIndex({"code": 1},{background:true, unique: true})
// db.getCollection("combo_reward").ensureIndex({"lucky_wheel_code": 1, "is_active": 1},{background:true})
// db.getCollection("combo_reward_piece").ensureIndex({"piece_code": 1},{background:true, unique: true})
// db.getCollection("combo_reward_piece").ensureIndex({"combo_code": 1},{background:true})
