package api

import (
	"encoding/json"
	"fmt"
	"strings"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"go.mongodb.org/mongo-driver/bson"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

func GetSingleLuckyWheel(req sdk.APIRequest, resp sdk.APIResponder) error {
	str := req.GetParam("gamificationId")

	if str != "" {
		gamificationId := sdk.ParseInt64(str, 0)
		if gamificationId > 0 {
			return resp.Respond(action.GetLuckyWheelById(gamificationId))
		}
	}

	type myRequest struct {
		model.LuckyWheel
		ListIDs []int64 `json:"listIds"`
	}
	var input myRequest
	str = req.GetParam("q")
	if str == "" {
		str = "{}"
	}

	err := json.Unmarshal([]byte(str), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}

	var offset = int64(sdk.ParseInt(req.GetParam("offset"), 0))
	var limit = int64(sdk.ParseInt(req.GetParam("limit"), 20))
	var getTotal = req.GetParam("getTotal") == "true"
	return resp.Respond(action.GetLuckyWheelList(&input.LuckyWheel, offset, limit, getTotal))
}

func GetLuckyWheelList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
		codes    = req.GetParam("codes")
		search   = req.GetParam("search")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.LuckyWheel{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}
	if len(search) > 0 {
		search := parserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", search), Options: ""},
		})
	}

	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"code": &bson.M{
				"$in": codesArr,
			},
		})
	}

	return resp.Respond(action.GetLuckyWheelList(&query, offset, limit, getTotal))
}

func GetLuckyWheelItemList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit  = sdk.ParseInt64(req.GetParam("limit"), 20)
		q      = req.GetParam("q")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.LuckyWheelItem{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	return resp.Respond(action.GetLuckyWheelItemList(&query))
}

func CreateLuckyWheel(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.LuckyWheel
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.LuckyWheelCreate(UserInfo.Account, &input))
}

func UpdateLuckyWheel(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.LuckyWheel
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}

	return resp.Respond(action.UpdateLuckyWheel(getActionSource(req), &input))
}

func CreateLuckyWheelItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.LuckyWheelItem
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.LuckyWheelItemCreate(UserInfo.Account, &input))
}

func UpdateLuckyWheelItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.LuckyWheelItem
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}

	return resp.Respond(action.UpdateLuckyWheelItem(getActionSource(req), &input))
}

func PostSpinLuckyWheel(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		Code          string `json:"code"`
		Type          string `json:"type"`
		Phone         string `json:"phone"`
		ProvinceCode  string `json:"provinceCode"`
		SystemDisplay string `json:"source"`
	}

	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}
	if input.SystemDisplay == "" {
		input.SystemDisplay = "BUYMED"
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.PostSpinLuckyWheel(acc, input.Code, input.Type, input.SystemDisplay, input.Phone, input.ProvinceCode))
	}

	return resp.Respond(&common.APIResponse{
		Status: common.APIStatus.Invalid,
	})

}

func GetSelfLuckyWheel(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		luckyWheelCode = req.GetParam("luckyWheelCode")
		phone          = req.GetParam("phone")
		provinceCode   = req.GetParam("provinceCode")
		systemDisplay  = req.GetParam("source")
	)

	if systemDisplay == "" {
		systemDisplay = "BUYMED"
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetSelfLuckyWheel(acc, luckyWheelCode, systemDisplay, phone, provinceCode))
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Missing User Token.",
	})
}

func GetSelfLuckyWheelLog(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset        = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit         = sdk.ParseInt64(req.GetParam("limit"), 20)
		code          = req.GetParam("luckyWheelCode")
		typeLog       = req.GetParam("type")
		phone         = req.GetParam("phone")
		systemDisplay = req.GetParam("source")
	)
	if systemDisplay == "" {
		systemDisplay = "BUYMED"
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetSelfLuckyWheelLog(acc, code, typeLog, phone, systemDisplay, offset, limit, true))
	}

	return resp.Respond(&common.APIResponse{
		Status: common.APIStatus.Invalid,
	})
}

func GetLuckyWheelLog(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit  = sdk.ParseInt64(req.GetParam("limit"), 20)
		q      = req.GetParam("q")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.LuckyWheelLog{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetLuckyWheelLog(acc, &query, offset, limit, true))
	}

	return resp.Respond(&common.APIResponse{
		Status: common.APIStatus.Invalid,
	})
}

func CreateCustomerLuckyWheel(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CustomerLuckyWheel
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CreateCustomerLuckyWheel(getActionSource(req), &input))
}

func GetMissionLuckyWheel(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		luckyWheelCode = req.GetParam("luckyWheelCode")
		phone          = req.GetParam("phone")
		systemDisplay  = req.GetParam("source")
	)
	return resp.Respond(action.GetMissionLuckyWheel(getActionSource(req), luckyWheelCode, phone, systemDisplay))
}

func IncreaseCustomerLuckyWheel(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CustomerLuckyWheelLog
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	input.SystemDisplay = input.Source

	return resp.Respond(action.IncreaseCustomerLuckyWheel(getActionSource(req), &input))
}

func GetLuckyWheelCustomList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	// fill query
	query := model.LuckyWheelCustom{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetLuckyWheelCustomList(&query, offset, limit, getTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "UNAUTHORZIED",
	})
}

func CreateLuckyWheelCustom(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.LuckyWheelCustom
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.CreateLuckyWheelCustom(UserInfo.Account, &input))
}

func UpdateLuckyWheelCustom(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.LuckyWheelCustom
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateLuckyWheelCustom(&input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "UNAUTHORZIED",
	})
}

func GetLuckyWheelEventList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	// fill query
	query := model.LuckyWheelEvent{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetLuckyWheelEventList(&query, offset, limit, getTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "UNAUTHORZIED",
	})
}

func CreateLuckyWheelEvent(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.LuckyWheelEvent
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.CreateLuckyWheelEvent(UserInfo.Account, &input))
}

func UpdateLuckyWheelEvent(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.LuckyWheelEvent
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateLuckyWheelEvent(&input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "UNAUTHORZIED",
	})
}

func GetLuckyWheelItemsDescription(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	// fill query
	query := model.LuckyWheelItemDescription{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetLuckyWheelItemsDescription(&query, offset, limit, getTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "UNAUTHORZIED",
	})
}

func CreateLuckyWheelItemDescription(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.LuckyWheelItemDescription
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	// Check UpdatedBy
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error. " + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing User Token.",
		})
	}

	return resp.Respond(action.CreateLuckyWheelItemsDescription(UserInfo.Account, &input))
}

func UpdateLuckyWheelItemDescription(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.LuckyWheelItemDescription
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateLuckyWheelItemsDescription(&input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "UNAUTHORZIED",
	})
}

func GetLKComboLogList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	query := model.LuckyWheelComboLog{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}
	return resp.Respond(action.GetLKComboLogList(&query, offset, limit, getTotal))
}
