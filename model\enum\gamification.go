package enum

type GamificationConditionTypeValue string

type gamificationConditionType struct {
	TotalOrderPrice              GamificationConditionTypeValue
	TotalActualPrice             GamificationConditionTypeValue
	ORDER                        GamificationConditionTypeValue
	PRODUCT                      GamificationConditionTypeValue
	TAG                          GamificationConditionTypeValue
	SELLER                       GamificationConditionTypeValue
	DISCOVER                     GamificationConditionTypeValue
	SHARE                        GamificationConditionTypeValue
	INVITE                       GamificationConditionTypeValue
	TotalOrderPriceWithDelivered GamificationConditionTypeValue
}

var GamificationConditionType = &gamificationConditionType{
	TotalOrderPrice:              "TOTAL_ORDER_PRICE",
	TotalActualPrice:             "TOTAL_ACTUAL_PRICE",
	ORDER:                        "ORDER",
	PRODUCT:                      "PRODUCT",
	TAG:                          "TAG",
	SELLER:                       "SELLER",
	DISCOVER:                     "DISCOVER",
	SHARE:                        "SHARE",
	INVITE:                       "INVITE",
	TotalOrderPriceWithDelivered: "TOTAL_ORDER_PRICE_WITH_DELIVERED",
}

type GamificationResultStatusValue string

type gamificationResultStatus struct {
	IN_PROGRESS GamificationResultStatusValue
	COMPLETED   GamificationResultStatusValue
}

var GamificationResultStatus = &gamificationResultStatus{
	IN_PROGRESS: "IN_PROGRESS",
	COMPLETED:   "COMPLETED",
}

type GamificationLogStatusValue string

type gamificationLogStatus struct {
	IN_PROCESS GamificationLogStatusValue
	TODO       GamificationLogStatusValue
	DONE       GamificationLogStatusValue
	FAIL       GamificationLogStatusValue
}

var GamificationLogStatus = &gamificationLogStatus{
	IN_PROCESS: "IN_PROCESS",
	TODO:       "TODO",
	DONE:       "DONE",
	FAIL:       "FAIL",
}

type SyncGamificationType string

type syncGamification struct {
	AUTO   SyncGamificationType
	MANUAL SyncGamificationType
}

var SyncGamification = &syncGamification{
	AUTO:   "AUTO",
	MANUAL: "MANUAL",
}

type GamificationRewardType string
type gamificationReward struct {
	POINT          GamificationRewardType
	VOUCHER        GamificationRewardType
	TURNS          GamificationRewardType
	TICKET_PATTERN GamificationRewardType
	OTHER          GamificationRewardType
}

var GamificaitonRewardType = &gamificationReward{
	POINT:          "POINTS",
	VOUCHER:        "VOUCHER",
	TURNS:          "TURNS",
	TICKET_PATTERN: "TICKET_PATTERN",
	OTHER:          "OTHER",
}

type GamificaitonRewardPointType string

type gamificationRewardPoint struct {
	ABSOLUTE GamificaitonRewardPointType
	PERCENT  GamificaitonRewardPointType
}

var GamificationRewardPointType = &gamificationRewardPoint{
	ABSOLUTE: "ABSOLUTE",
	PERCENT:  "PERCENT",
}

type RewardProgressType string

type rewardProgress struct {
	READY      RewardProgressType
	PROCESSING RewardProgressType
	COMPLETED  RewardProgressType
}

var RewardProgress = &rewardProgress{
	READY:      "READY",
	PROCESSING: "PROCESSING",
	COMPLETED:  "COMPLETED",
}

type GamificationTypeValue string

type gamificationType struct {
	GAMIFICATION_MISSION GamificationTypeValue
	DASHBOARD_MISSION    GamificationTypeValue
}

var GamificationType = &gamificationType{
	GAMIFICATION_MISSION: "GAMIFICATION_MISSION",
	DASHBOARD_MISSION:    "DASHBOARD_MISSION",
}
