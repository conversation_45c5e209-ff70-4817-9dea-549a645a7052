package api

import (
	"github.com/go-playground/validator/v10"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

func validatorErrorResponse(err error) *common.APIResponse {
	validatorErrors := err.(validator.ValidationErrors)
	message := err.Error()
	if len(validatorErrors) > 0 {
		message = validatorErrors[0].Error()
	}
	return &common.APIResponse{
		Status:    common.APIStatus.Error,
		Message:   message,
		ErrorCode: "INVALID_INPUT",
	}
}

func unauthorizedResponse() *common.APIResponse {
	return &common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "You are not authorized to perform this action",
		ErrorCode: "ACTION_NOT_FOUND",
	}
}
