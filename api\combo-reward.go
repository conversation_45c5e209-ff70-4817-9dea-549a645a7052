package api

import (
	"encoding/json"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func GetComboRewardList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
		search   = req.GetParam("search")
		codes    = req.GetParam("codes")
	)

	// fill query
	query := model.ComboReward{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	if len(search) > 0 {
		search := parserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"name": utils.GetSearchRegex(search, true),
		})
	}
	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"code": &bson.M{
				"$in": codesArr,
			},
		})
	}

	return resp.Respond(action.GetListComboReward(&query, offset, limit, getTotal))
}

func GetComboRewardPieceList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == "true"
		q          = req.GetParam("q")
		search     = req.GetParam("search")
		comboCodes = req.GetParam("comboCodes")
		codes      = req.GetParam("codes")
	)

	// fill query
	query := model.ComboRewardPiece{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	if len(search) > 0 {
		search := parserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"piece_name": utils.GetSearchRegex(search, true),
		})
	}

	if len(comboCodes) > 0 {
		codesArr := strings.Split(comboCodes, ",")
		listComboCodes := make([]string, 0)
		for _, comboCode := range codesArr {
			if comboCode != "" {
				listComboCodes = append(listComboCodes, comboCode)
			}
		}
		query.ComplexQuery = []*bson.M{
			{
				"combo_code": &bson.M{
					"$in": listComboCodes,
				},
			},
		}
	}
	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"piece_code": &bson.M{
				"$in": codesArr,
			},
		})
	}

	return resp.Respond(action.GetListComboRewardPiece(&query, offset, limit, getTotal))
}

func CreateComboReward(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = new(model.ComboReward)

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreateComboReward(acc, input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func ToggleComboReward(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = new(struct {
		ComboCode     string `json:"code"`
		VersionUpdate string `json:"versionUpdate"`
	})

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if input.ComboCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "MISSING_COMBO_CODE",
			ErrorCode: "MISSING_COMBO_CODE",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.ToggleActiveComboReward(acc, input.ComboCode, input.VersionUpdate))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CreateSetComboPiece(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = new(model.UpsertSetComboPieceRequest)

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpsertSetComboPiece(acc, input, "CREATE"))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func UpdateSetComboPiece(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = new(model.UpsertSetComboPieceRequest)

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpsertSetComboPiece(acc, input, "UPDATE"))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func UpdateComboReward(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = new(model.ComboReward)

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateComboReward(acc, input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func UpdateComboRewardPiece(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = new(model.ComboRewardPiece)

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateComboRewardPiece(acc, input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func GetSelfComboReward(req sdk.APIRequest, resp sdk.APIResponder) error {
	luckyWheelCode := req.GetParam("luckyWheelCode")

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetSelfComboReward(acc, luckyWheelCode))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CollectComboReward(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		ComboCode     string `json:"comboCode"`
		Phone         string `json:"phone"`
		SystemDisplay string `json:"source"`
	}

	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}
	if input.SystemDisplay == "" {
		input.SystemDisplay = "BUYMED"
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CollectComboReward(acc, input.ComboCode, input.Phone))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}
