package api

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/helper"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	MAX_QNT_PER_CUSTOMER_GREATER_THAN_MAX_QNT = "MAX_QNT_PER_CUSTOMER_GREATER_THAN_MAX_QNT"
)

// CampaignCreate ...
func CampaignCreate(req sdk.APIRequest, resp sdk.APIResponder) error {
	// bind
	var input model.CampaignCreateRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	// return resp.Respond(action.CreateCampaign(1, &input))

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreateCampaign(acc.AccountID, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CampaignList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q") // {} -> model.Campaign{"campaignName": "exm"}
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
		codes    = req.GetParam("codes")
		region   = req.GetParam("region")
	)

	// query q
	query := model.Campaign{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	query.ComplexQuery = make([]*bson.M, 0)
	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = []*bson.M{
			{
				"campaign_code": &bson.M{
					"$in": codesArr,
				},
			},
		}
	}

	if len(query.CampaignName) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", helper.NormalizeString(query.CampaignName)), Options: ""},
		})
		query.CampaignName = ""
	}

	if query.ProcessingTimeFrom != nil || query.ProcessingTimeTo != nil {
		if query.ProcessingTimeFrom != nil {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"start_time": bson.M{"$gte": query.ProcessingTimeFrom},
			})
		}

		if query.ProcessingTimeTo != nil {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"start_time": bson.M{"$lte": query.ProcessingTimeTo},
			})
		}
	}

	if query.SystemDisplay == "" {
		query.SystemDisplay = "BUYMED"
	} else if query.SystemDisplay == "ALL" {
		query.SystemDisplay = ""
	}

	if region != "" {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"regions": region,
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListCampaign(&query, offset, limit, getTotal, false, "", &primitive.M{"_id": -1}))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CampaignUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CampaignUpdateRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateCampaign(acc.AccountID, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CampaignGet(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q") // {}
	)

	if len(q) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Param missing",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	// query q
	query := model.Campaign{}
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if query.SystemDisplay == "" {
		query.SystemDisplay = "BUYMED"
	} else if query.SystemDisplay == "ALL" {
		query.SystemDisplay = ""
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetCampaign(&query, false, ""))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func SellerCampaignList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q          = req.GetParam("q")
		sellerCode = req.GetParam("sellerCode")
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal   = req.GetParam("getTotal") == "true"
		sort       = req.GetParam("sort")
	)

	if len(sellerCode) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Seller code missing",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	query := model.CampaignActiveRequest{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	TRUE := true
	query.IsActive = &TRUE

	query.ComplexOrQuery = []*bson.M{
		{
			"seller_codes": bson.M{
				"$size": 0,
			},
		},
		{
			"seller_codes": sellerCode,
		},
	}

	if query.RegistrationStartTime != nil && query.RegistrationEndTime != nil {
		query.ComplexQuery = []*bson.M{
			{
				"registration_start_time": bson.M{
					"$gte": query.RegistrationStartTime,
				},
			},
			{
				"registration_end_time": bson.M{
					"$lte": query.RegistrationEndTime,
				},
			},
		}
	}

	if query.StartTime != nil && query.EndTime != nil {
		comp := []*bson.M{
			{
				"start_time": bson.M{
					"$gte": query.StartTime,
				},
			},
			{
				"end_time": bson.M{
					"$lte": query.EndTime,
				},
			},
		}
		if len(query.ComplexQuery) > 0 {
			query.ComplexQuery = append(query.ComplexQuery, comp...)
		} else {
			query.ComplexQuery = comp
		}

	}

	if len(query.CampaignName) > 0 {
		comp := []*bson.M{
			{
				"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", helper.NormalizeString(query.CampaignName)), Options: ""},
			},
		}
		query.CampaignName = ""
		if len(query.ComplexQuery) > 0 {
			query.ComplexQuery = append(query.ComplexQuery, comp...)
		} else {
			query.ComplexQuery = comp
		}

	}

	sortCampaign := &primitive.M{"_id": -1}

	if len(sort) > 0 {
		switch sort {
		case "REGISTER":
			{
				sortCampaign = &primitive.M{"registration_start_time": 1}
			}
		case "-REGISTER":
			{
				sortCampaign = &primitive.M{"registration_start_time": -1}
			}
		case "START_TIME":
			{
				sortCampaign = &primitive.M{"start_time": 1}
			}
		case "-START_TIME":
			{
				sortCampaign = &primitive.M{"start_time": -1}
			}
		}
	}

	if query.SystemDisplay == "" {
		query.SystemDisplay = "BUYMED"
	} else if query.SystemDisplay == "ALL" {
		query.SystemDisplay = ""
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListCampaign(&query, offset, limit, getTotal, query.IsJoined, sellerCode, sortCampaign))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func SellerCampaignGet(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q          = req.GetParam("q")
		sellerCode = req.GetParam("sellerCode")
	)

	if len(sellerCode) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Seller code missing",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if len(q) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Param query missing",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	query := model.CampaignActiveRequest{}
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	TRUE := true
	query.IsActive = &TRUE

	query.ComplexOrQuery = []*bson.M{
		{
			"seller_codes": bson.M{
				"$size": 0,
			},
		},
		{
			"seller_codes": sellerCode,
		},
	}

	if query.SystemDisplay == "" {
		query.SystemDisplay = "BUYMED"
	} else if query.SystemDisplay == "ALL" {
		query.SystemDisplay = ""
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetCampaign(&query, true, sellerCode))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// SellerCampaignCreate ...
func SellerCampaignCreate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.SellerCampaignCreateRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if errCode, err := validateCampaignProductConditions(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: errCode,
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.SellerCreateTicketCampaign(acc.AccountID, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CampaignTicketList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q              = req.GetParam("q") // {} -> model.Ticket{"sellerCode": "exm"}
		offset         = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit          = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal       = req.GetParam("getTotal") == "true"
		externalSeller = req.GetParam("externalSeller") == "true"
	)

	// query q
	query := model.Ticket{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if externalSeller {
		internalSellers := []string{"MEDX", "MEDX_E", "DENX"}
		query.ComplexQuery = []*bson.M{
			{
				"seller_code": &bson.M{
					"$nin": internalSellers,
				},
			},
		}
	}

	return resp.Respond(action.GetListTicketCampaign(&query, offset, limit, getTotal))
}

func SellerCampaignListTicket(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q          = req.GetParam("q")
		sellerCode = req.GetParam("sellerCode")
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal   = req.GetParam("getTotal") == "true"
	)

	if len(sellerCode) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Seller code missing",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	query := model.Ticket{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	query.SellerCode = sellerCode

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListTicketCampaign(&query, offset, limit, getTotal))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func TicketUpdateStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.TicketUpdateStatusRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateTicketStatus(acc.AccountID, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func SellerTicketUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.SellerTicketUpdateRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if errCode, err := validateCampaignProductConditions(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: errCode,
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.SellerUpdateTicket(acc.AccountID, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CampaignSearchList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CampaignSearchRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if len(input.CampaignIDs) > 0 {
		return resp.Respond(action.GetListCampaign(&model.Campaign{
			ComplexQuery: []*bson.M{
				{
					"campaign_id": bson.M{
						"$in": input.CampaignIDs,
					},
				},
			},
		}, 0, 0, false, false, "", &primitive.M{"_id": -1}))
	}
	if len(input.CampaignCodes) > 0 {
		return resp.Respond(action.GetListCampaign(&model.Campaign{
			ComplexQuery: []*bson.M{
				{
					"campaign_code": bson.M{
						"$in": input.CampaignCodes,
					},
				},
			},
		}, 0, 0, false, false, "", &primitive.M{"_id": -1}))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Campaign ID or Code not match",
		ErrorCode: "PAYLOAD_VALIDATE",
	})
}

func CampaignProductList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q") // {}
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
		tab      = req.GetParam("tab")

		ticketIDs = req.GetParam("ticketIDs")
	)

	// query q
	query := model.CampaignProduct{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if len(ticketIDs) > 0 {
		query.ComplexQuery = appendQueryTicketIDs(query.ComplexQuery, ticketIDs)
	}

	TRUE := true
	FALSE := false

	if tab == "active" {
		query.IsActive = &TRUE
		query.Status = enum.CampaignProductStatus.NORMAL
	}

	if tab == "inactive" {
		query.IsActive = &FALSE
		query.Status = enum.CampaignProductStatus.NORMAL
	}

	if tab == "cancelled" {
		query.Status = enum.CampaignProductStatus.CANCELLED
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListCampaignProduct(&query, offset, limit, getTotal, &primitive.M{"_id": -1}))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func appendQueryTicketIDs(query []*bson.M, ticketIDs string) []*bson.M {
	ids := strings.Split(ticketIDs, ",")
	numIds := make([]int64, 0)
	for _, id := range ids {
		numIds = append(numIds, sdk.ParseInt64(id, 0))
	}
	query = append(query, &bson.M{
		"approved_ticket_id": bson.M{
			"$in": numIds,
		},
	},
	)
	return query
}

func CampaignActiveList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q          = req.GetParam("q") // {} -> model.Campaign{"campaignName": "exm"}
		sellerCode = req.GetParam("sellerCode")
	)

	// query q
	query := model.Campaign{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListActiveCampaign(&query, sellerCode, acc))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CampaignProductCreate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CampaignProductCreateRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if errCode, err := validateCampaignProductConditions(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: errCode,
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreateCampaignProduct(acc, &input, false))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// validateCampaignProductConditions check max quantity per customer should not greater than quantity
// with following types: CampaignProductCreateRequest, CampaignProductUpdateRequest,
// SellerCampaignCreateRequest, SellerTicketUpdateRequest
// return error code and error
func validateCampaignProductConditions(input interface{}) (string, error) {
	if input == nil {
		return "INPUT_EMPTY", fmt.Errorf("input is nil")
	}
	if input, ok := input.(*model.CampaignProductCreateRequest); ok {
		maxQntPerCustomer := input.MaxQuantityPerCustomer
		if maxQntPerCustomer != nil {
			if *maxQntPerCustomer > input.Quantity {
				return MAX_QNT_PER_CUSTOMER_GREATER_THAN_MAX_QNT, fmt.Errorf(
					"MaxQuantityPerCustomer should not greater than Quantity in CampaignProductCreateRequest %s",
					input.CampaignCode)
			}
		}
		return "", nil
	}

	if input, ok := input.(*model.CampaignProductUpdateRequest); ok {
		maxQntPerCustomer := input.MaxQuantityPerCustomer
		if maxQntPerCustomer != nil && input.Quantity != nil {
			if *maxQntPerCustomer > *input.Quantity {
				return MAX_QNT_PER_CUSTOMER_GREATER_THAN_MAX_QNT, fmt.Errorf(
					"MaxQuantityPerCustomer should not greater than Quantity in CampaignProductUpdateRequest %s",
					input.CampaignCode)
			}
		}
		return "", nil
	}

	if input, ok := input.(*model.SellerCampaignCreateRequest); ok {
		maxQntPerCustomer := input.MaxQuantityPerCustomer
		if maxQntPerCustomer != nil {
			if *maxQntPerCustomer > input.Quantity {
				return MAX_QNT_PER_CUSTOMER_GREATER_THAN_MAX_QNT, fmt.Errorf(
					"MaxQuantityPerCustomer should not greater than Quantity in SellerCampaignCreateRequest %d",
					input.CampaignID)
			}
		}
		return "", nil
	}

	if input, ok := input.(*model.SellerTicketUpdateRequest); ok {
		maxQntPerCustomer := input.MaxQuantityPerCustomer
		if maxQntPerCustomer != nil {
			if *maxQntPerCustomer > input.Quantity {
				return MAX_QNT_PER_CUSTOMER_GREATER_THAN_MAX_QNT, fmt.Errorf(
					"MaxQuantityPerCustomer should not greater than Quantity in SellerTicketUpdateRequest %d",
					input.TicketID)
			}
		}
		return "", nil
	}

	return "WRONG_TYPE", fmt.Errorf("invalid input type")
}

func CampaignProductUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CampaignProductUpdateRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if errCode, err := validateCampaignProductConditions(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: errCode,
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateCampaignProduct(acc, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CampaignProductDelete(req sdk.APIRequest, resp sdk.APIResponder) error {
	code := req.GetParam("code")
	id := sdk.ParseInt64(req.GetParam("id"), 0)

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.DeleteCampaignProduct(acc, code, id))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CheckCampaignWithCart(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Cart
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	//if acc := getActionSource(req); acc != nil {
	return resp.Respond(action.CheckCampaignWithCart(&model.Account{}, &input))
	////}
	//return resp.Respond(&common.APIResponse{
	//	Status:    common.APIStatus.Unauthorized,
	//	Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
	//	ErrorCode: "ACTION_NOT_FOUND",
	//})
}

func UpdateSoldQuantityFromOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.UpdateSoldQuantityFromOrderRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateSoldQuantityFromOrder(acc, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CampaignHistoryGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
	)
	var query = model.CampaignHistory{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListCampaignHistory(&query, offset, limit, getTotal))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func SellerCampaignCheck(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.SellerCampaignCheck
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.SellerCampaignCheck(input.SellerCode, input.ProductIds))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func SellerCampaignProductList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q") // {}
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
		sort     = req.GetParam("sort")
	)

	// query q
	query := model.CampaignProduct{}
	if len(q) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Seller code or campaign code missing",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	err := json.Unmarshal([]byte(q), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if len(query.SellerCode) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Seller code or campaign code missing",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	sortCampaign := &primitive.M{"_id": -1}
	if len(sort) > 0 {
		switch sort {
		case "SOLD_QUANTITY":
			{
				sortCampaign = &primitive.M{"sold_quantity": 1}
			}
		case "-SOLD_QUANTITY":
			{
				sortCampaign = &primitive.M{"sold_quantity": -1}
			}
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListCampaignProduct(&query, offset, limit, getTotal, sortCampaign))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CheckProductCampaignRegister(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload = struct {
		SKU           string `json:"sku"`
		CampaignCode  string `json:"campaignCode"`
		SystemDisplay string `json:"systemDisplay,omitempty"`
	}{}
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CheckProductCampaignRegister(payload.SKU, payload.CampaignCode, payload.SystemDisplay))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func SyncProductCampaignSaleTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload = struct {
		CampaignCode string `json:"campaignCode"`
	}{}
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.SyncProductCampaignSaleTime(payload.CampaignCode))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func ImportCampaignProduct(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ImportCampaignProductRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể đọc dữ liệu",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	for _, item := range input.Data {
		if errCode, err := validateCampaignProductConditions(&item); err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: errCode,
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.ImportCampaignProduct(acc, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func GetCampaignActiveBySku(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload = struct {
		Sku       string    `json:"sku"`
		StartTime time.Time `json:"startTime"`
		EndTime   time.Time `json:"endTime"`

		SystemDisplay string `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
	}{}
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetCampaignActiveBySku(payload.Sku, payload.StartTime, payload.EndTime, payload.SystemDisplay))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func TurnOffProductInCampaignBySku(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload = struct {
		Sku string `json:"sku"`
	}{}
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.TurnOffProductInCampaignBySku(payload))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func SyncSkuInfoInCampaign(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload = struct {
		Sku string `json:"sku"`
	}{}
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	return resp.Respond(action.SyncSkuInfoInCampaign(payload.Sku))
}

func CheckCampaignProductFulfillment(req sdk.APIRequest, resp sdk.APIResponder) error {
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CheckCampaignProductFulfill(acc))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// GetCheckProductFulfillmentLogList is func get list level
func GetCheckProductFulfillmentLogList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		codes    = req.GetParam("codes")
	)
	var query = model.CheckProductFulfillmentLog{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	query.ComplexQuery = make([]*bson.M, 0)
	if len(codes) > 0 {
		codesArr := strings.Split(codes, ",")
		query.ComplexQuery = []*bson.M{
			{
				"code": &bson.M{
					"$in": codesArr,
				},
			},
		}
	}

	if query.CreatedTimeFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFrom,
			},
		})
	}

	if query.CreatedTimeTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lte": query.CreatedTimeTo,
			},
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetCheckProductFulfillmentLogList(&query, offset, limit, getTotal))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// GetCheckProductFulfillmentDetailList is func get list level
func GetCheckProductFulfillmentDetailList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
	)
	var query = model.CheckProductFulfillmentDetail{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetCheckProductFulfillmentDetailList(&query, offset, limit, getTotal))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func MigrateCampaignFlashSaleItems(req sdk.APIRequest, resp sdk.APIResponder) error {
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.MigrateCampaignFlashSaleItems())
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func WarmupCampaignProductBySkuMain(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.WarmupCampaignProductRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể đọc dữ liệu",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.WarmupCampaignProductBySku(&input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func GetSellerCampaignTicketList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 1000 {
		limit = 1000
	}

	if offset < 0 {
		offset = 0
	}

	query := model.Ticket{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   err.Error(),
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetSellerCampaignTicketList(&query, offset, limit, getTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func UpdateMultiTicketStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.UpdateMultiTicketStatusRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể đọc dữ liệu",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateMultipleTicketStatus(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CreateMultiTicket(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CreateMultiTicketRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể đọc dữ liệu",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreateMultipleTicket(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}
