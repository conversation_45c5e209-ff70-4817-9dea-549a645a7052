package api

import (
	"encoding/json"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

func CreateGamificationDetail(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		Data []*model.GamificationDetail `json:"data"`
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreateGamificationDetail(acc, input.Data))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "<PERSON><PERSON><PERSON> kho<PERSON>n của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func UpdateGamificationDetail(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		Data           []*model.GamificationDetail `json:"data"`
		GamificationID int64                       `json:"gamificationId"`
		LuckyWheelCode string                      `json:"luckyWheelCode"`
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateGamificationDetail(acc, input.Data, input.GamificationID, input.LuckyWheelCode))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func GetListGamificationDetail(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)
	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.GamificationDetail{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetListGamificationDetail(&query, offset, limit, getTotal))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func GetSingleGamificationDetail(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.GamificationDetail
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetSingleGamificationDetail(&input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}
