package circa

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/conf"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathCreateVoucher = "/v1/promotion/lucky-wheel/voucher"
)

// CreateCircaVoucherResponse is resp from circa client
type CreateCircaVoucherResponse struct {
	Success bool                            `json:"success"`
	Data    *DataCreateCircaVoucherResponse `json:"data"`
	Errors  []*CircaError                   `json:"errors"`
}

type DataCreateCircaVoucherResponse struct {
	VoucherCode string `json:"voucher_code"`
}

type CircaError struct {
	Code             string `json:"code"`
	LocalizedMessage struct {
		Key string `json:"key"`
	} `json:"localized_message"`
}

// Client is model define CustomerClient
type Client struct {
	circaClient *client.RestClient
	headers     map[string]string
}

// NewServiceClient is func create new service client
func NewServiceClient(apiHost, apiKey string, s *mongo.Database) *Client {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	c := &Client{
		circaClient: client.NewRESTClient(
			apiHost,
			"circa_client",
			25*time.Second,
			1,
			50*time.Millisecond,
		),
		//headers: map[string]string{
		//	"Authorization": apiKey,
		//},
	}
	c.circaClient.SetDBLog(s)
	return c
}

// CreateCircaVoucher ...
func (cli *Client) CreateCircaVoucher(phone, code, des string) (*DataCreateCircaVoucherResponse, error) {
	params := map[string]string{}
	var payload = struct {
		Phone       string `json:"phone"`
		Code        string `json:"code"`
		Description string `json:"description"`
	}{
		Phone:       phone,
		Code:        code,
		Description: des,
	}
	dataBytes, _ := json.Marshal(payload)
	dataJsonStr := string(dataBytes)
	var payloadHashed = struct {
		Data       string `json:"data"`
		HashedData string `json:"hashed_data"`
	}{
		Data:       dataJsonStr,
		HashedData: hashSHA256WithSecretKey([]byte(conf.Config.CircaCallbackKey), dataBytes),
	}

	res, err := cli.circaClient.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, payloadHashed, pathCreateVoucher, nil)
	if err != nil {
		return nil, err
	}

	var result *CreateCircaVoucherResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil || result == nil {
		return nil, err
	}

	if !result.Success {
		if len(result.Errors) > 0 {
			return nil, fmt.Errorf("%v", result.Errors[0].LocalizedMessage.Key)
		}
		return nil, fmt.Errorf("create circa voucher failed")
	}

	return result.Data, nil
}

func hashSHA256WithSecretKey(secretKey, data []byte) string {
	h := hmac.New(sha256.New, []byte(conf.Config.CircaCallbackKey))
	h.Write(data)
	return hex.EncodeToString(h.Sum(nil))
}
