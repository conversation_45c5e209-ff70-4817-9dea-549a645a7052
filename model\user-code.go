package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type UserPromotion struct {
	ID              primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	UpdatedBy       int64              `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	VersionNo       string             `json:"versionNo,omitempty" bson:"version_no,omitempty"`

	CustomerID       int64                 `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	CustomerIDs      []int64               `json:"customerIds,omitempty" bson:"-"`
	OrderIDs         *[]int64              `json:"orderIds,omitempty" bson:"order_ids,omitempty"`
	PromotionID      int64                 `json:"promotionId,omitempty" bson:"promotion_id,omitempty"`
	VoucherCode      string                `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	TransID          string                `json:"transId,omitempty" bson:"trans_id,omitempty"`
	Status           *enum.CodeStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	Amount           *int64                `json:"amount,omitempty" bson:"amount,omitempty"`
	CollectedTime    *time.Time            `json:"collectedTime,omitempty" bson:"collected_time,omitempty"`
	UsedTime         *time.Time            `json:"usedTime,omitempty" bson:"used_time,omitempty"`
	UseType          enum.UseTypeValue     `json:"useType,omitempty" bson:"use_type,omitempty"`
	IsCollected      *bool                 `json:"isCollected,omitempty" bson:"is_collected,omitempty"`
	CollectSource    *CollectSource        `json:"collectSource,omitempty" bson:"collect_source,omitempty"`
	TotalRewardCount int                   `json:"totalRewardCount,omitempty" bson:"total_reward_count,omitempty"`
	GiftCount        map[string]int        `json:"giftCount,omitempty" bson:"gift_count,omitempty"`

	//for fillter
	TimeCollectFrom *time.Time `json:"timeCollectFrom" bson:"-"`
	TimeCollectTo   *time.Time `json:"timeCollectTo" bson:"-"`
	FilterSku       string     `json:"filterSku" bson:"-"`

	IsUpdateAll       bool                        `json:"isUpdateAll,omitempty" bson:"-"`
	PrevStatus        *enum.CodeStatusValue       `json:"prevStatus,omitempty" bson:"-"`
	Usable            bool                        `json:"-" bson:"usable,omitempty"`
	KeyUsable         int64                       `json:"-" bson:"key_usable,omitempty"`
	VoucherStatus     string                      `json:"-" bson:"voucher_status,omitempty"`
	CustomerApplyType enum.CustomerApplyTypeValue `json:"-" bson:"customer_apply_type,omitempty"`
	ComplexQuery      []*bson.M                   `json:"-" bson:"$and,omitempty"`
	SystemNote        string                      `json:"-" bson:"system_note,omitempty"`
	LastID            primitive.ObjectID          `json:"lastID,omitempty" bson:"-"`
}

type CollectSource struct {
	Page        string    `json:"page,omitempty" bson:"page,omitempty"`
	FullLink    string    `json:"fullLink,omitempty" bson:"full_link,omitempty"`
	Screen      string    `json:"screen,omitempty" bson:"screen,omitempty"`
	Sku         string    `json:"sku,omitempty" bson:"sku,omitempty"`
	TimeCollect time.Time `json:"timeCollect,omitempty" bson:"time_collect,omitempty"`
}

var UserPromotionDB = &db.Instance{
	ColName:        "user_promotion",
	TemplateObject: &UserPromotion{},
}

var UserPromotionCacheDB = &db.Instance{
	ColName:        "user_promotion",
	TemplateObject: &UserPromotion{},
}

// InitUserPromotionModel is func init model
func InitUserPromotionModel(s *mongo.Database) {
	UserPromotionDB.ApplyDatabase(s)

	t := true
	UserPromotionDB.CreateIndex(
		bson.D{
			primitive.E{Key: "voucher_code", Value: 1},
			primitive.E{Key: "_id", Value: 1},
		},
		&options.IndexOptions{Background: &t},
	)
}

// InitUserPromotionModel is func init model
func InitUserPromotionCacheModel(s *mongo.Database) {
	UserPromotionCacheDB.ApplyDatabase(s)

	t := true
	UserPromotionCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "voucher_status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	UserPromotionCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "customer_apply_type", Value: 1},
		primitive.E{Key: "status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	UserPromotionCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "customer_id", Value: 1},
		primitive.E{Key: "status", Value: 1},
		primitive.E{Key: "voucher_status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	UserPromotionCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "voucher_code", Value: 1},
		primitive.E{Key: "_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}

var UserPromotionCacheReadDB = &db.Instance{
	ColName:        "user_promotion",
	TemplateObject: &UserPromotion{},
}

func InitUserPromotionCacheReadOnly(s *mongo.Database) {
	UserPromotionCacheReadDB.ApplyDatabase(s)
}
