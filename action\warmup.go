package action

import (
	"fmt"
	"strings"

	"sync"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var voucherCacheMutex sync.Mutex

func WarmUpVoucherByCode(code string, voucher *model.Voucher, source string) {
	if voucher == nil {
		qVoucher := model.VoucherDB.QueryOne(model.Voucher{Code: code})
		if qVoucher.Status != common.APIStatus.Ok {
			return
		}
		voucher = qVoucher.Data.([]*model.Voucher)[0]
	}
	voucher.Usable = utils.ParseBoolToPointer(true)

	if voucher.AppliedCustomers != nil {
		voucher.AppliedCustomerMap = make(map[int64]bool, len(*voucher.AppliedCustomers))
		for _, customer := range *voucher.AppliedCustomers {
			voucher.AppliedCustomerMap[customer] = true
		}
	}

	isWarmUpCustomerScope := false
	for _, scope := range voucher.Scopes {
		if scope.Type != nil && *scope.Type == enum.ScopeType.AREA {
			if voucher.RegionScopeMap == nil {
				voucher.RegionScopeMap = make(map[string]bool, 10)
			}
			if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.ALL {
				voucher.RegionScopeMap["ALL"] = true
				voucher.Filter = append(voucher.Filter, mapRegionCache["00"]...)
			} else if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.MANY {
				for _, region := range scope.AreaCodes {
					voucher.RegionScopeMap[region] = true
					if len(mapRegionCache[region]) > 0 {
						voucher.Filter = append(voucher.Filter, mapRegionCache[region]...)
					} else {
						voucher.Filter = append(voucher.Filter, region)
					}
				}
			}
		} else if scope.Type != nil && *scope.Type == enum.ScopeType.CUSTOMER_LEVEL {
			if voucher.LevelScopeMap == nil {
				voucher.LevelScopeMap = make(map[string]bool, 10)
			}
			if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.ALL {
				voucher.LevelScopeMap["ALL"] = true
				if mapLevelCache == nil || len(mapLevelCache["ALL"]) == 0 {
					WarmupLevelMasterdataCache()
				}
				voucher.Filter = append(voucher.Filter, mapLevelCache["ALL"]...)
			} else if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.MANY {
				for _, level := range scope.CustomerLevelCodes {
					voucher.LevelScopeMap[level] = true
					voucher.Filter = append(voucher.Filter, level)
				}
			}
		} else if scope.Type != nil && *scope.Type == enum.ScopeType.CUSTOMER_SCOPE {
			isWarmUpCustomerScope = true
			if voucher.CustomerScopeMap == nil {
				voucher.CustomerScopeMap = make(map[string]bool, 10)
			}
			if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.ALL {
				voucher.CustomerScopeMap["ALL"] = true
				voucher.Filter = append(voucher.Filter, mapScopeCache["ALL"]...)
			} else if scope.QuantityType != nil && *scope.QuantityType == enum.QuantityType.MANY {
				for _, data := range scope.CustomerScopes {
					voucher.CustomerScopeMap[data] = true
					voucher.Filter = append(voucher.Filter, data)
				}
			}
		}
	}
	if !isWarmUpCustomerScope {
		voucher.Filter = append(voucher.Filter, mapScopeCache["ALL"]...)
	}

	normCodeStr := strings.Replace(utils.NormalizeString(voucher.Code), " ", "-", -1)
	normDisplayNameStr := strings.Replace(utils.NormalizeString(voucher.DisplayName), " ", "-", -1)
	normShortNameStr := strings.Replace(utils.NormalizeString(voucher.ShortName), " ", "-", -1)
	voucher.HashTag = fmt.Sprintf("%d-%s-%s-%s", voucher.PromotionID, normCodeStr, normDisplayNameStr, normShortNameStr)
	productCodes, tagCodes, skuCodes, sellers, skusNotIn, tagCodesIn := getProductInfoByCondition(voucher.OrConditions, voucher.AndConditions, voucher.ApplyDiscount, voucher.SellerCodes, voucher.StoreCode)
	if voucher.FilterProduct == nil {
		voucher.FilterProduct = &model.FilterProduct{}
	}
	voucher.FilterProduct.Tags = &tagCodes
	voucher.FilterProduct.Skus = &skuCodes
	voucher.FilterProduct.Sellers = &sellers
	if len(sellers) == 0 && len(productCodes) > 0 {
		voucher.FilterProduct.ProductCodes = &productCodes
	}
	voucher.FilterProduct.SkuNotIn = &skusNotIn
	voucher.FilterProduct.TagIn = &tagCodesIn

	updater := voucher
	updater.RefProduct = setVoucherRefProduct(voucher)
	if len(voucher.RefProduct) > 0 {
		updater.ExistRefProduct = utils.ParseBoolToPointer(true)
	}
	updater.ID = primitive.NilObjectID

	voucherCacheMutex.Lock()
	defer voucherCacheMutex.Unlock()

	needUpdateVoucherStatus := false
	var currentVoucherCache *model.Voucher

	// Check if voucher exists for status comparison
	qVoucherCache := model.VoucherCacheDB.QueryOne(model.Voucher{Code: voucher.Code})
	if qVoucherCache.Status == common.APIStatus.Ok {
		currentVoucherCache = qVoucherCache.Data.([]*model.Voucher)[0]
		if currentVoucherCache.Status != nil && updater.Status != nil && *currentVoucherCache.Status != *updater.Status {
			needUpdateVoucherStatus = true
		}
	}

	err := model.VoucherCacheDB.Upsert(model.Voucher{Code: voucher.Code}, updater)
	if err.Status != common.APIStatus.Ok {
		fmt.Println("Error upsert voucher cache", err.Message)
	}

	if needUpdateVoucherStatus {
		lastID := primitive.NilObjectID
		for {
			query := bson.M{"voucher_code": voucher.Code}
			if lastID != primitive.NilObjectID {
				query["_id"] = bson.M{"$gt": lastID}
			}
			qUserPromotion := model.UserPromotionCacheDB.Query(query, 0, 100, &primitive.M{"_id": 1})
			if qUserPromotion.Status != common.APIStatus.Ok {
				break
			}
			for _, userPromotion := range qUserPromotion.Data.([]*model.UserPromotion) {
				lastID = userPromotion.ID
				if voucher.Status != nil {
					model.UserPromotionCacheDB.UpdateOne(model.UserPromotion{ID: userPromotion.ID}, model.UserPromotion{VoucherStatus: string(*voucher.Status)})
				}
			}
		}
	}
}

func WarmUpUserPromotion(code string, customerID int64, use *model.UserPromotion) *common.APIResponse {
	if use != nil {
		use.ID = primitive.NilObjectID
		model.UserPromotionCacheDB.Upsert(model.UserPromotion{VoucherCode: use.VoucherCode, CustomerID: use.CustomerID}, use)
		//fmt.Println(use.VoucherCode, upsertResult.Message)
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "OK",
		}
	}
	query := model.UserPromotion{VoucherCode: code, CustomerID: customerID}
	offset, limit := int64(0), int64(1000)
	voucherStatus := ""
	var voucherCustomerApplyType enum.CustomerApplyTypeValue
	for {
		qUses := model.UserPromotionDB.Query(query, offset*limit, limit, nil)
		if qUses.Status != common.APIStatus.Ok {
			break
		}
		if qVoucher := model.VoucherDB.QueryOne(model.Voucher{Code: code}); qVoucher.Status == common.APIStatus.Ok {
			if qVoucher.Data.([]*model.Voucher)[0].Status != nil {
				voucherCustomerApplyType = qVoucher.Data.([]*model.Voucher)[0].CustomerApplyType
				voucherStatus = string(*qVoucher.Data.([]*model.Voucher)[0].Status)
			}
		}
		for _, use := range qUses.Data.([]*model.UserPromotion) {
			use.VoucherStatus = voucherStatus
			use.CustomerApplyType = voucherCustomerApplyType
			model.UserPromotionCacheDB.Upsert(model.UserPromotion{VoucherCode: use.VoucherCode, CustomerID: use.CustomerID}, use)
		}
		offset++
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	}
}

func setVoucherRefProduct(voucher *model.Voucher) []string {
	refProducts := make([]string, 0)
	if data, ok := voucher.OrConditions[enum.ConditionType.PRODUCT]; ok {
		for _, c := range data.OrConditions {
			refProduct := c.ProductConditionField.ProductCode
			if c.ProductConditionField.SellerCode != nil {
				refProduct = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
			}
			refProducts = append(refProducts, refProduct)
		}
		for _, c := range data.AndConditions {
			refProduct := c.ProductConditionField.ProductCode
			if c.ProductConditionField.SellerCode != nil {
				refProduct = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
			}
			refProducts = append(refProducts, refProduct)
		}
	}
	if data, ok := voucher.AndConditions[enum.ConditionType.PRODUCT]; ok {
		for _, c := range data.AndConditions {
			refProduct := c.ProductConditionField.ProductCode
			if c.ProductConditionField.SellerCode != nil {
				refProduct = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
			}
			refProducts = append(refProducts, refProduct)
		}
		for _, c := range data.OrConditions {
			refProduct := c.ProductConditionField.ProductCode
			if c.ProductConditionField.SellerCode != nil {
				refProduct = fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode)
			}
			refProducts = append(refProducts, refProduct)
		}
	}
	return refProducts
}

func setIsExistConditionTag(voucher *model.Voucher) (bool, []string) {
	tags := make([]string, 0)
	isMatchTagCondition := false
	if _, ok := voucher.OrConditions[enum.ConditionType.PRODUCT_TAG]; ok {
		for _, c := range voucher.OrConditions[enum.ConditionType.PRODUCT_TAG].OrConditions {
			tags = append(tags, c.ProductTagConditionField.TagCode)
		}
		for _, c := range voucher.OrConditions[enum.ConditionType.PRODUCT_TAG].AndConditions {
			tags = append(tags, c.ProductTagConditionField.TagCode)
		}
		isMatchTagCondition = true
	}
	if _, ok := voucher.AndConditions[enum.ConditionType.PRODUCT_TAG]; ok {
		for _, c := range voucher.AndConditions[enum.ConditionType.PRODUCT_TAG].AndConditions {
			tags = append(tags, c.ProductTagConditionField.TagCode)
		}
		for _, c := range voucher.AndConditions[enum.ConditionType.PRODUCT_TAG].OrConditions {
			tags = append(tags, c.ProductTagConditionField.TagCode)
		}
		isMatchTagCondition = true
	}
	return isMatchTagCondition, tags
}

func setIsExistConditionProduct(voucher *model.Voucher) (bool, []string, []string) {
	products := make([]string, 0)
	skus := make([]string, 0)
	isMatchProductCondition := false
	if _, ok := voucher.OrConditions[enum.ConditionType.PRODUCT]; ok {
		for _, c := range voucher.OrConditions[enum.ConditionType.PRODUCT].OrConditions {
			products = append(products, c.ProductConditionField.ProductCode)
			if c.ProductConditionField.SellerCode != nil {
				skus = append(skus, fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode))
			}
		}
		for _, c := range voucher.OrConditions[enum.ConditionType.PRODUCT].AndConditions {
			products = append(products, c.ProductConditionField.ProductCode)
			if c.ProductConditionField.SellerCode != nil {
				skus = append(skus, fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode))
			}
		}
		isMatchProductCondition = true
	}
	if _, ok := voucher.AndConditions[enum.ConditionType.PRODUCT]; ok {
		for _, c := range voucher.AndConditions[enum.ConditionType.PRODUCT].AndConditions {
			products = append(products, c.ProductConditionField.ProductCode)
			if c.ProductConditionField.SellerCode != nil {
				skus = append(skus, fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode))
			}
		}
		for _, c := range voucher.AndConditions[enum.ConditionType.PRODUCT].OrConditions {
			products = append(products, c.ProductConditionField.ProductCode)
			if c.ProductConditionField.SellerCode != nil {
				skus = append(skus, fmt.Sprintf("%s.%s", *c.ProductConditionField.SellerCode, c.ProductConditionField.ProductCode))
			}
		}
		isMatchProductCondition = true
	}
	return isMatchProductCondition, products, skus
}

func isExistConditionPaymentMethod(voucher *model.Voucher) (bool, []string, []string) {
	paymentMethods := make([]string, 0)
	paymentMethodNames := make([]string, 0)
	isMatchPaymentMethod := false
	if _, ok := voucher.OrConditions[enum.ConditionType.PAYMENT_METHOD]; ok {
		for _, c := range voucher.OrConditions[enum.ConditionType.PAYMENT_METHOD].OrConditions {
			paymentMethods = append(paymentMethods, c.PaymentConditionField.PaymentMethod)
			paymentMethodNames = append(paymentMethodNames, c.PaymentConditionField.PaymentMethodName)
		}
		for _, c := range voucher.OrConditions[enum.ConditionType.PAYMENT_METHOD].AndConditions {
			paymentMethods = append(paymentMethods, c.PaymentConditionField.PaymentMethod)
			paymentMethodNames = append(paymentMethodNames, c.PaymentConditionField.PaymentMethodName)
		}
		isMatchPaymentMethod = true
	}
	if _, ok := voucher.AndConditions[enum.ConditionType.PAYMENT_METHOD]; ok {
		for _, c := range voucher.AndConditions[enum.ConditionType.PAYMENT_METHOD].AndConditions {
			paymentMethods = append(paymentMethods, c.PaymentConditionField.PaymentMethod)
			paymentMethodNames = append(paymentMethodNames, c.PaymentConditionField.PaymentMethodName)
		}
		for _, c := range voucher.AndConditions[enum.ConditionType.PAYMENT_METHOD].OrConditions {
			paymentMethods = append(paymentMethods, c.PaymentConditionField.PaymentMethod)
			paymentMethodNames = append(paymentMethodNames, c.PaymentConditionField.PaymentMethodName)
		}
		isMatchPaymentMethod = true
	}
	return isMatchPaymentMethod, paymentMethods, paymentMethodNames
}

func setIsOnlyConditionOrder(voucher *model.Voucher) bool {
	isOnlyConditionOrder := false
	if voucher.OrConditions != nil && len(voucher.OrConditions) == 1 {
		if _, ok := voucher.OrConditions[enum.ConditionType.ORDER_VALUE]; ok {
			isOnlyConditionOrder = true
		}
	}

	if voucher.AndConditions != nil && len(voucher.AndConditions) == 1 {
		if _, ok := voucher.AndConditions[enum.ConditionType.ORDER_VALUE]; ok {
			isOnlyConditionOrder = true
		}
	}

	return isOnlyConditionOrder
}
