package model

import "gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"

type PromotionScopeCondition struct {
	Scopes         []enum.ScopeTypeValue     `bson:"scopes"`
	AreaScope      map[string]int            `bson:"area_scope"`
	CustomerScope  map[string]int            `bson:"customer_scope"`
	Conditions     []enum.ConditionTypeValue `bson:"conditions"`
	OrderCondition map[string]OrderCondition `json:"order_condition"`
	CustomerCondition map[string]CustomerCondition `json:"customer_condition"`
}
