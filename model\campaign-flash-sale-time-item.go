package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type CampaignFlashSaleTimeItem struct {
	CampaignID    int64           `json:"-" bson:"campaign_id,omitempty"`
	Code          string          `json:"code,omitempty" bson:"code,omitempty"`
	Ref           string          `json:"ref,omitempty" bson:"ref,omitempty"`
	RefTable      string          `json:"-" bson:"ref_table,omitempty"`
	Name          string          `json:"name,omitempty" bson:"name,omitempty"`
	StartTime     *time.Time      `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime       *time.Time      `json:"endTime,omitempty" bson:"end_time,omitempty"`
	ProductIDs    *[]int64        `json:"productIDs,omitempty" bson:"product_ids,omitempty"`
	CategoryCodes *[]string       `json:"categoryCodes,omitempty" bson:"category_codes,omitempty"`
	Hour          []time.Duration `json:"-" bson:"hour,omitempty"`
	Day           int             `json:"-" bson:"day,omitempty"`
	ComplexQuery  []*bson.M       `json:"-" bson:"$and,omitempty"`
}

var CampaignFlashSaleTimeItemDB = &db.Instance{
	ColName:        "campaign_flash_sale_time_item",
	TemplateObject: &CampaignFlashSaleTimeItem{},
}

// InitCampaignFlashSaleTimeItemModel is func init model
func InitCampaignFlashSaleTimeItemModel(dbInst *db.Instance, s *mongo.Database) {
	dbInst.ApplyDatabase(s)

	// t := true
	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// dbInst.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
