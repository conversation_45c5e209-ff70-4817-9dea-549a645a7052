package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

func DailyCheckinCustomerGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
	)
	var query = model.CheckinCustomer{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PAYLOAD_INVALID",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.DailyCheckinCustomerList(&query, offset, limit, getTotal))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func AddCustomerToDailyCheckin(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload struct {
		CustomerIds []int64 `json:"customerIds"`
		CheckinCode string  `json:"checkinCode"`
		Status      string  `json:"status"`
	}

	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.AddCustomerToDailyCheckin(payload.CheckinCode, payload.CustomerIds, payload.Status))
}

func UpdateCustomerOfDailyCheckin(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload model.CheckinCustomer
	if err := req.GetContent(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&payload); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.UpdateCustomerOfDailyCheckin(payload))
}

func DeleteCustomerOfDailyCheckin(req sdk.APIRequest, resp sdk.APIResponder) error {
	code := req.GetParam("code")
	return resp.Respond(action.DeleteCustomerOfDailyCheckin(code))
}
