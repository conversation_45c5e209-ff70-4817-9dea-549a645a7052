package action

import (
	"encoding/json"
	"fmt"
	"strconv"
	"sync"

	"gitlab.com/thuocsi.vn/marketplace/promotion/conf"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

var (
	mapRegionCache       map[string][]string
	isSyncMapRegionCache bool
	mapLevelCache        map[string][]string
	mapScopeCache        = map[string][]string{
		"ALL": []string{"PHARMACY",
			"CLINIC",
			"DRUGSTORE",
			"HOSPITAL",
			"PHARMA_COMPANY",
			"DENTISTRY",
			"BEAUTY_SALON",
			"HEALTH_CENTER",
			"PHARMACIST",
		},
		"PHARMACY":       []string{"PHARMACY"},
		"PHARMACIST":     []string{"PHARMACIST"},
		"CLINIC":         []string{"CLIN<PERSON>"},
		"DRUGSTORE":      []string{"DRUGSTORE"},
		"HOSPITAL":       []string{"HOSPITAL"},
		"PHARMA_COMPANY": []string{"PHARMA_COMPANY"},
		"DENTISTRY":      []string{"DENTISTRY"},
		"BEAUTY_SALON":   []string{"BEAUTY_SALON"},
		"HEALTH_CENTER":  []string{"HEALTH_CENTER"},
	}
	mapVendorStoreByTag         map[string]string
	SortVoucherConfig           = model.SortVoucherConfig{}
	ConditionMessageConfigCache = model.ConditionMessageConfig{} // Customize message text voucher's condition
)

// WarmupRegionMasterdataCache ...
func WarmupRegionMasterdataCache() {
	if isSyncMapRegionCache {
		return
	}
	isSyncMapRegionCache = true
	defer func() {
		isSyncMapRegionCache = false
	}()
	tmpMapRegionCache := make(map[string][]string)
	if mapRegionCache == nil {
		mapRegionCache = make(map[string][]string)
	}
	regionResp := client.LocationClient.GetRegionList([]string{})
	if regionResp.Status != common.APIStatus.Ok || len(regionResp.Data) == 0 {
		// reset
		return
	}
	for _, region := range regionResp.Data {
		tmpMapRegionCache[region.Code] = region.ProvinceCodes
	}

	listProvinces := make([]string, 0)
	provinceResp := client.LocationClient.GetProvinceList()
	if provinceResp.Status == common.APIStatus.Ok {
		for _, province := range provinceResp.Data {
			listProvinces = append(listProvinces, province.Code)
		}
	}
	tmpMapRegionCache["00"] = listProvinces

	// changeRegion := CompareMapString(mapRegionCache, tmpMapRegionCache)
	// isEmpty := len(mapRegionCache) == 0
	mapRegionCache = tmpMapRegionCache
	// if len(changeRegion) > 0 && !isEmpty {
	// 	for _, region := range changeRegion {
	// 		WarnUpAllCampaignByLocation(region)
	// 	}
	// }
}

// WarmupLevelMasterdataCache ...
func WarmupLevelMasterdataCache() {
	if mapLevelCache == nil {
		mapLevelCache = make(map[string][]string)
	}

	levelCodes := []string{}

	levels, err := client.Services.Customer.GetListLevelCustomer()
	if err != nil {
		fmt.Printf("Failed to get customer levels for cache: %v\n", err)
		// Set default level codes if API call fails
		levelCodes = []string{
			"LEVEL_NEWBIE",
			"LEVEL_SILVER",
			"LEVEL_GOLD",
			"LEVEL_PLATINUM",
			"LEVEL_DIAMOND",
			"LEVEL_BLACKLIST",
			"LEVEL_GUEST",
		}
	} else {
		for _, level := range levels {
			levelCodes = append(levelCodes, level.Code)
		}
	}

	mapLevelCache["ALL"] = levelCodes
}

func uniqueSliceString(stringSlice []string) []string {
	keys := make(map[string]bool)
	list := []string{}
	for _, entry := range stringSlice {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

func getDuplicateTwoSliceString(slice1, slice2 []string) []string {
	elements := make(map[string]bool)

	// Store elements of the first array in the map
	for _, str := range slice1 {
		elements[str] = true
	}
	duplicate := make([]string, 0)
	for _, str2 := range slice2 {
		if elements[str2] {
			duplicate = append(duplicate, str2)
		}
	}
	return duplicate
}

func CompareMapString(curMap, newMap map[string][]string) []string {
	changeMap := make([]string, 0)
	for keyCur, valCur := range curMap {
		if newMap[keyCur] == nil || len(newMap[keyCur]) != len(valCur) {
			changeMap = append(changeMap, keyCur)
		}
	}

	for keyNew, valNew := range newMap {
		if curMap[keyNew] == nil || len(curMap[keyNew]) != len(valNew) {
			changeMap = append(changeMap, keyNew)
		}
	}
	return uniqueSliceString(changeMap)
}

func isNilOrDefaultValue(val interface{}) bool {
	switch v := val.(type) {
	case *int:
		if v == nil {
			return true
		} else {
			return *v == 0
		}
	case int:
		return v == 0
	case *float64:
		if v == nil {
			return true
		} else {
			return *v == 0
		}
	case float64:
		return v == 0
	case *int64:
		if v == nil {
			return true
		} else {
			return *v == 0
		}
	case int64:
		return v == 0
	case string:
		return v == ""
	case *string:
		if v == nil {
			return true
		} else {
			return *v == ""
		}
	case nil:
		return true
	}
	return false
}

func isSkipByCustomer(customer *model.Customer, mapSkip map[string]bool) bool {
	keyProvince := "PROVINCE_" + customer.ProvinceCode
	keyCustomerID := "CUSTOMER_" + strconv.Itoa(int(customer.CustomerID))
	keyCustomerLevel := "CUSTOMER_LEVEL_" + customer.Level
	keyCustomerStatus := "CUSTOMER_STATUS_" + customer.Status
	for _, tag := range customer.Tags {
		keyTag := "TAG_" + tag
		if data, ok := mapSkip[keyTag]; ok && data {
			return true
		}
	}
	if data, ok := mapSkip[keyProvince]; ok && data {
		return true
	}
	if data, ok := mapSkip[keyCustomerID]; ok && data {
		return true
	}
	if data, ok := mapSkip[keyCustomerLevel]; ok && data {
		return true
	}
	if data, ok := mapSkip[keyCustomerStatus]; ok && data {
		return true
	}
	return false
}

// WarmupConfigManagement ...
func WarmupConfigManagement() {
	confResp := client.ConfigManagerClient.GetAppValue("XKU72AXH") // customer app
	if confResp.Status == common.APIStatus.Ok {
		for _, con := range confResp.Data {
			appValue := con.Value
			keySortVoucher := "SORT_VOUCHER"
			if conf.Config.Env == "uat" {
				keySortVoucher = "SORT_VOUCHER_UAT"
			}
			if appValue.Key == keySortVoucher {
				tmpSortVoucherConfig := model.SortVoucherConfig{}
				err := json.Unmarshal([]byte(appValue.ValString), &tmpSortVoucherConfig)
				if err == nil {
					// mutex lock, update, unlock
					m := sync.Mutex{}
					m.Lock()
					SortVoucherConfig = tmpSortVoucherConfig
					m.Unlock()
				} else {
					// log error
					fmt.Println("Error when unmarshal SortVoucherConfig", err.Error())
				}
			}
			if appValue.Key == "CONDITION_MESSAGE_CONFIG" {
				conditionMessageConfig := model.ConditionMessageConfig{}
				err := json.Unmarshal([]byte(appValue.ValString), &conditionMessageConfig)
				if err == nil {
					ConditionMessageConfigCache = conditionMessageConfig
				}
			}
		}
	}
}

var muMapVendorStoreByTag = sync.RWMutex{}

func WarmupVendorStoreCache() {
	offset := 0
	limit := 100

	if mapVendorStoreByTag == nil {
		muMapVendorStoreByTag.Lock()
		mapVendorStoreByTag = make(map[string]string)
		muMapVendorStoreByTag.Unlock()
	}
	for {
		stores, err := client.Services.Seller.GetVendorStoreList(offset, limit)
		if err != nil {
			break
		}
		for _, store := range stores {
			muMapVendorStoreByTag.Lock()
			mapVendorStoreByTag[store.Code] = store.AllProductTag
			muMapVendorStoreByTag.Unlock()
		}
		offset += limit
	}
}
