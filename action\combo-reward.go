package action

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func GetListComboReward(query *model.ComboReward, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.ComboRewardDB.Query(query, offset, limit, &primitive.M{"_id": -1})

	if result.Status != common.APIStatus.Ok {
		return result
	}

	if getTotal {
		result.Total = model.ComboRewardDB.Count(query).Total
	}

	return result
}

func GetListComboRewardPiece(query *model.ComboRewardPiece, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.ComboRewardPieceDB.Query(query, offset, limit, &primitive.M{"combo_code": 1, "position": 1})

	if result.Status != common.APIStatus.Ok {
		return result
	}

	if getTotal {
		result.Total = model.ComboRewardPieceDB.Count(query).Total
	}

	return result
}

func CreateComboReward(acc *model.Account, comboReward *model.ComboReward) *common.APIResponse {
	comboReward.VersionUpdate = model.GenCodeWithTime()
	return model.ComboRewardDB.Create(comboReward)
}

func UpsertSetComboPiece(acc *model.Account, req *model.UpsertSetComboPieceRequest, method string) *common.APIResponse {
	isCreating := false
	if method == "CREATE" {
		isCreating = true
	}
	reqPieces, comboCode, comboType := req.Pieces, req.ComboCode, req.ComboType

	for _, piece := range reqPieces {
		piece.PieceCode = fmt.Sprintf("%s#%s", comboCode, piece.PieceCode)
	}

	newTotalPiece, ok := enum.MapComboTypeToNumber[comboType]

	if !ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Loại combo không hợp lệ",
			ErrorCode: "COMBO_REWARD_TYPE_INVALID",
		}
	}

	if newTotalPiece != len(reqPieces) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số mảnh ghép không hợp lệ",
			ErrorCode: "COMBO_REWARD_TYPE_INVALID",
		}
	}

	queryCR := model.ComboReward{
		Code: comboCode,
	}

	qComboReward := model.ComboRewardDB.QueryOne(queryCR)

	if qComboReward.Status != common.APIStatus.Ok {
		return qComboReward
	}

	comboData := qComboReward.Data.([]*model.ComboReward)[0]
	if req.ComboVersionUpdate != comboData.VersionUpdate {
		// Combo Reward is outdated
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng cập nhật phiên bản mới của Combo phần thưởng",
			ErrorCode: "VERSION_OUTDATED",
		}
	}

	now := time.Now()

	if now.After(*comboData.StartTime) && comboData.ComboType != nil && *comboData.ComboType != comboType {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Không thể cập nhật loại combo của Combo phần thưởng đã hoạt động",
			ErrorCode: "CANNOT_UPDATE_COMBO_TYPE_OF_COMBO_REWARD_STARTED",
		}
	}

	currentCountPiece := 0

	if comboData.ComboType != nil {
		currentCountPiece = enum.MapComboTypeToNumber[*comboData.ComboType]
	}

	if currentCountPiece > newTotalPiece {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Không thể giảm số lượng mảnh ghép",
			ErrorCode: "CANNOT_DECREASE_TOTAL_PIECE",
		}
	}

	getCurrPiecesResp := model.ComboRewardPieceDB.Query(model.ComboRewardPiece{
		ComboCode: comboCode,
	}, 0, 0, nil)

	if getCurrPiecesResp.Status == common.APIStatus.Ok {
		// check all current pieces in DB must be in request pieces
		// if not, don't allow to update
		if isCreating {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Bộ mảnh ghép đã tồn tại, không thể tạo mới",
				ErrorCode: "SET_COMBO_PIECES_IS_EXISTED",
			}
		}
		currPiecesData := getCurrPiecesResp.Data.([]*model.ComboRewardPiece)
		allVisited := true
		for _, currPiece := range currPiecesData {
			visited := false
			for _, reqPiece := range reqPieces {
				if reqPiece.PieceCode == currPiece.PieceCode {
					visited = true
					break
				}
			}

			if !visited {
				allVisited = false
				break
			}
		}

		if !allVisited {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Dữ liệu mảnh ghép đầu vào không hợp lệ",
				ErrorCode: "INVALID_COMBO_REWARD_PIECES",
			}
		}
	}

	updateComboResp := model.ComboRewardDB.UpdateOne(queryCR, model.ComboReward{
		ComboType:     &comboType,
		VersionUpdate: model.GenCodeWithTime(),
	})

	if updateComboResp.Status != common.APIStatus.Ok {
		return updateComboResp
	}

	if isCreating {
		for index, item := range reqPieces {
			expectQuantity := int64(1)
			if item.ExpectQuantity != 0 {
				expectQuantity = item.ExpectQuantity
			}
			model.ComboRewardPieceDB.Create(&model.ComboRewardPiece{
				ComboCode:      comboCode,
				PieceCode:      item.PieceCode,
				CreatedBy:      acc.AccountID,
				PieceName:      item.PieceName,
				ImageUrl:       item.ImageUrl,
				ExpectQuantity: expectQuantity,
				Position:       index + 1,
			})
		}

		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: fmt.Sprintf("Tạo mới bộ mảnh ghép của Combo %s thành công", comboData.Name),
		}
	} else {
		for index, item := range reqPieces {
			expectQuantity := int64(1)
			if item.ExpectQuantity != 0 {
				expectQuantity = item.ExpectQuantity
			}
			updatePieceResp := model.ComboRewardPieceDB.UpdateOne(model.ComboRewardPiece{
				ComboCode: comboCode,
				PieceCode: item.PieceCode,
			}, model.ComboRewardPiece{
				ComboCode:      comboCode,
				PieceCode:      item.PieceCode,
				CreatedBy:      acc.AccountID,
				PieceName:      item.PieceName,
				ImageUrl:       item.ImageUrl,
				ExpectQuantity: expectQuantity,
				Position:       index + 1,
			}, &options.FindOneAndUpdateOptions{Upsert: utils.ParseBoolToPointer(true)})

			if updatePieceResp.Status != common.APIStatus.Ok && updatePieceResp.Status != common.APIStatus.NotFound {
				return updatePieceResp
			}
		}

		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: fmt.Sprintf("Cập nhật bộ mảnh ghép của Combo %s thành công", comboData.Name),
		}
	}
}

func ToggleActiveComboReward(acc *model.Account, comboCode, versionUpdate string) *common.APIResponse {
	query := model.ComboReward{
		Code: comboCode,
	}

	qComboReward := model.ComboRewardDB.QueryOne(query)

	if qComboReward.Status != common.APIStatus.Ok {
		return qComboReward
	}
	comboData := qComboReward.Data.([]*model.ComboReward)[0]
	comboType := comboData.ComboType

	if versionUpdate != comboData.VersionUpdate {
		// Combo Reward is outdated
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng cập nhật phiên bản mới của Combo phần thưởng",
			ErrorCode: "VERSION_OUTDATED",
		}
	}

	if comboType == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chưa cập nhật loại Combo phần thưởng",
			ErrorCode: "COMBO_REWARD_TYPE_INVALID",
		}
	}

	expectTotalPiece, ok := enum.MapComboTypeToNumber[*comboType]

	if !ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Loại combo không hợp lệ ! Vui lòng cập nhật loại Combo phần thưởng",
			ErrorCode: "COMBO_REWARD_TYPE_INVALID",
		}
	}

	currentIsActive := comboData.IsActive
	newIsActive := false

	if currentIsActive == nil {
		newIsActive = true
	} else {
		newIsActive = !*currentIsActive
	}

	if newIsActive == true {
		// ComboReward just able to active when has correctly number of pieces
		queryPiece := model.ComboRewardPiece{
			ComboCode: comboCode,
		}
		qCountPieces := model.ComboRewardPieceDB.Count(queryPiece)

		if qCountPieces.Status != common.APIStatus.Ok {
			return qComboReward
		}

		countPieces := qCountPieces.Total

		if int(countPieces) != expectTotalPiece {
			return &common.APIResponse{
				Status:    common.APIStatus.Error,
				Message:   "Combo phần thưởng không có số lượng mảnh ghép phù hợp. Không thể active",
				ErrorCode: "COMBO_REWARD_PIECE_INVALID",
			}
		}
	}

	return model.ComboRewardDB.UpdateOne(query, model.ComboReward{
		IsActive:      utils.ParseBoolToPointer(newIsActive),
		VersionUpdate: model.GenCodeWithTime(),
	})
}

func UpdateComboReward(acc *model.Account, comboReward *model.ComboReward) *common.APIResponse {
	query := model.ComboReward{
		Code: comboReward.Code,
	}

	qComboReward := model.ComboRewardDB.QueryOne(query)

	if qComboReward.Status != common.APIStatus.Ok {
		return qComboReward
	}

	comboData := qComboReward.Data.([]*model.ComboReward)[0]

	if comboReward.VersionUpdate != comboData.VersionUpdate {
		// Combo Reward is outdated
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng cập nhật phiên bản mới của Combo phần thưởng",
			ErrorCode: "VERSION_OUTDATED",
		}
	}
	comboReward.VersionUpdate = model.GenCodeWithTime()

	return model.ComboRewardDB.UpdateOne(query, comboReward)
}

func UpdateComboRewardPiece(acc *model.Account, piece *model.ComboRewardPiece) *common.APIResponse {
	query := model.ComboRewardPiece{
		PieceCode: piece.PieceCode,
	}

	qPiece := model.ComboRewardPieceDB.QueryOne(query)

	if qPiece.Status != common.APIStatus.Ok {
		return qPiece
	}

	pieceData := qPiece.Data.([]*model.ComboRewardPiece)[0]

	if piece.ComboCode != "" {
		piece.ComboCode = pieceData.ComboCode
	}
	if piece.Position != 0 {
		piece.Position = pieceData.Position
	}

	// Don't allow to update ComboCode and Position

	return model.ComboRewardPieceDB.UpdateOne(query, piece)
}

func GetSelfComboReward(acc *model.Account, luckyWheelCode string) *common.APIResponse {
	queryCombo := model.ComboReward{
		LuckyWheelCode: luckyWheelCode,
		IsActive:       utils.ParseBoolToPointer(true),
	}
	qCombo := model.ComboRewardDB.Query(queryCombo, 0, 0, nil)
	if qCombo.Status != common.APIStatus.Ok {
		return qCombo
	}
	combos := qCombo.Data.([]*model.ComboReward)
	mapCombos := make(map[string]*model.ComboReward)
	comboCodes := make([]string, 0, len(combos))
	for _, c := range combos {
		comboCodes = append(comboCodes, c.Code)
		mapCombos[c.Code] = c
	}

	if len(comboCodes) > 0 {
		queryCusCombo := bson.M{
			"combo_code": bson.M{
				"$in": comboCodes,
			},
			"account_id": acc.AccountID,
		}
		mapCusCombo := make(map[string]*model.CustomerLuckyWheelCombo, 0)
		qCusCombo := model.CustomerLuckyWheelComboDB.Query(queryCusCombo, 0, 0, nil)
		if qCusCombo.Status == common.APIStatus.Ok {
			cusComboList := qCusCombo.Data.([]*model.CustomerLuckyWheelCombo)
			for _, c := range cusComboList {
				mapCusCombo[c.ComboCode] = c
			}
		}

		queryPieces := bson.M{
			"combo_code": bson.M{
				"$in": comboCodes,
			},
		}

		qPieces := model.ComboRewardPieceDB.Query(queryPieces, 0, 0, nil)

		if qPieces.Status == common.APIStatus.Ok {
			pieces := qPieces.Data.([]*model.ComboRewardPiece)
			for _, piece := range pieces {
				comboCode := piece.ComboCode
				if cusCombo, ok := mapCusCombo[comboCode]; ok {
					if cusPiece, ok := cusCombo.Pieces[piece.PieceCode]; ok {
						if combo, ok := mapCombos[comboCode]; ok {
							if cusPiece.Quantity > cusPiece.UsedQuantity {
								piece.CollectedQuantity = cusPiece.Quantity - cusPiece.UsedQuantity
							}
							if !isNilOrDefaultValue(combo.MaxQuantityPerCustomer) && cusCombo.UsedQuantity >= *combo.MaxQuantityPerCustomer {
								combo.IsMaxQuantityPerCustomerExceed = utils.ParseBoolToPointer(true)
							}
							combo.Pieces = append(combo.Pieces, piece)
						}
					} else {
						if combo, ok := mapCombos[comboCode]; ok {
							combo.Pieces = append(combo.Pieces, piece)
						}
					}
				} else {
					if combo, ok := mapCombos[comboCode]; ok {
						combo.Pieces = append(combo.Pieces, piece)
					}
				}
			}
		}
	}

	respCombo := make([]*model.ComboReward, 0)
	for _, c := range mapCombos {
		if !isNilOrDefaultValue(c.MaxQuantity) && c.UsedQuantity >= *c.MaxQuantity {
			c.IsMaxQuantityExceed = utils.ParseBoolToPointer(true)
		}

		if (c.IsMaxQuantityExceed == nil || *c.IsMaxQuantityExceed == false) && (c.IsMaxQuantityPerCustomerExceed == nil || *c.IsMaxQuantityPerCustomerExceed == false) {
			ableExchange := checkCollectEnoughPieces(c.Pieces, *c.ComboType)
			c.AbleExchange = &ableExchange
		} else {
			c.AbleExchange = utils.ParseBoolToPointer(false)
		}

		// if isMaxQuantityExceed or isMaxQuantityPerCustomerExceed is true, the customer can't change combo, set is complete to true
		if c.IsMaxQuantityPerCustomerExceed != nil && *c.IsMaxQuantityPerCustomerExceed {
			c.IsComplete = utils.ParseBoolToPointer(true)
		}

		// The customer just be able to change combo when still has slot and collect enough piece
		respCombo = append(respCombo, c)
	}

	sort.Slice(respCombo, func(i, j int) bool {
		// sort by is complete = false first, showPriority larger first
		comboI := respCombo[i]
		comboJ := respCombo[j]
		if comboI.IsComplete != comboJ.IsComplete {
			return comboI.IsComplete == nil || !*comboI.IsComplete
		} else if comboI.ShowPriority != comboJ.ShowPriority {
			return comboI.ShowPriority > comboJ.ShowPriority
		} else {
			if comboI.CreatedTime == nil || comboJ.CreatedTime == nil {
				return false
			}
			return comboI.CreatedTime.Before(*comboJ.CreatedTime)
		}
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    respCombo,
		Message: fmt.Sprintf("Get combo rewards list of lucky wheel %s success", luckyWheelCode),
	}
}

func CollectComboReward(acc *model.Account, comboCode, phone string) *common.APIResponse {
	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "CUSTOMER_NOT_FOUND",
			Message:   "Không tìm thấy thông tin khách hàng",
		}
	}
	queryCombo := model.ComboReward{
		Code: comboCode,
	}

	qComboReward := model.ComboRewardDB.QueryOne(queryCombo)

	if qComboReward.Status != common.APIStatus.Ok {
		return qComboReward
	}

	comboData := qComboReward.Data.([]*model.ComboReward)[0]
	if !isNilOrDefaultValue(comboData.MaxQuantity) && comboData.UsedQuantity >= *comboData.MaxQuantity {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Combo reward quantity exceed",
			ErrorCode: "QUANTITY_EXCEED",
		}
	}

	queryPieces := bson.M{
		"combo_code": comboCode,
	}

	qPieces := model.ComboRewardPieceDB.Query(queryPieces, 0, 0, &primitive.M{"index": 1})

	if qPieces.Status != common.APIStatus.Ok {
		return qPieces
	}

	pieces := qPieces.Data.([]*model.ComboRewardPiece)
	pieceCodes := make([]string, 0, len(pieces))
	for _, p := range pieces {
		pieceCodes = append(pieceCodes, p.PieceCode)
	}

	queryCusCombo := bson.M{
		"combo_code": comboCode,
		"account_id": acc.AccountID,
	}
	newVersionUpdate := model.GenCodeWithTime()
	afterOption := options.After
	updateResp := model.CustomerLuckyWheelComboDB.UpdateOneWithOption(queryCusCombo, bson.M{
		"$set": bson.M{
			"version_update": newVersionUpdate,
		},
	}, &options.FindOneAndUpdateOptions{
		ReturnDocument: &afterOption,
	})

	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	cusCombo := updateResp.Data.([]*model.CustomerLuckyWheelCombo)[0]
	if !isNilOrDefaultValue(comboData.MaxQuantityPerCustomer) && cusCombo.UsedQuantity >= *comboData.MaxQuantityPerCustomer {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Combo reward quantity per customer exceed",
			ErrorCode: "QUANTITY_PER_CUSTOMER_EXCEED",
		}
	}
	//TODO: implement MaxQuantityPerDay later

	mapCusPiece := cusCombo.Pieces
	incUsedQuantity := bson.M{"used_quantity": 1}

	for _, cp := range mapCusPiece {
		for _, p := range pieces {
			if cp.PieceCode == p.PieceCode {
				if cp.Quantity-cp.UsedQuantity >= p.ExpectQuantity {
					p.CollectedQuantity = cp.Quantity - cp.UsedQuantity
					incUsedQuantity["pieces."+p.PieceCode+".used_quantity"] = p.ExpectQuantity
				}
				break
			}
		}
	}

	if ableExchange := checkCollectEnoughPieces(pieces, *comboData.ComboType); !ableExchange {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "You don't collect enough pieces",
			ErrorCode: "NOT_ENOUGH_PIECES",
		}
	}

	updateCb := model.ComboRewardDB.UpdateOneWithOption(queryCombo, bson.M{
		"$inc": bson.M{"used_quantity": 1},
	})
	if updateCb.Status != common.APIStatus.Ok {
		return updateCb
	}

	queryClc := model.CustomerLuckyWheelCombo{
		ComboCode:     comboCode,
		AccountID:     acc.AccountID,
		VersionUpdate: newVersionUpdate,
	}
	updateQResp := model.CustomerLuckyWheelComboDB.UpdateOneWithOption(queryClc, bson.M{
		"$inc": incUsedQuantity,
	})

	queryUpdateCustomerLuckyWheel := bson.M{"lucky_wheel_code": comboData.LuckyWheelCode}
	if phone != "" {
		queryUpdateCustomerLuckyWheel["customer_phone"] = phone
	} else {
		queryUpdateCustomerLuckyWheel["account_id"] = acc.AccountID
	}

	if updateQResp.Status == common.APIStatus.Ok {
		if comboData.Rewards != nil {
			for _, reward := range *comboData.Rewards {
				switch reward.TypeReward {
				case enum.ComboRewardItem.VOUCHER:
					patternVoucher := "LUCKY_WHEEL_#"
					if reward.VoucherPattern != nil {
						patternVoucher = *reward.VoucherPattern
					}
					voucherID, voucherCode := model.GenVoucherID()
					voucherCode = strings.ReplaceAll(patternVoucher, "#", voucherCode)
					voucherRes := CreateVoucherByPromotionID(nil, customer.CustomerID, reward.PromotionID, voucherCode, voucherID, reward.NumberOfDayUseVoucher)
					if voucherRes.Status == common.APIStatus.Ok {
						voucher := voucherRes.Data.([]*model.Voucher)[0]
						model.CustomerLuckyWheelComboDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
							"$push": bson.M{"rewards": model.CustomerLuckyWheelComboReward{
								VoucherCode: voucher.Code,
							},
							}})
					}
				case enum.ComboRewardItem.TURNS:
					model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
						"$inc": bson.M{"quantity": reward.TurnsRotation},
						"$push": bson.M{"reward": model.CustomerLuckyWheelComboReward{
							TurnsRotation: reward.TurnsRotation,
						}},
					})
				case enum.ComboRewardItem.TICKET_PATTERN:
					model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
						"$push": bson.M{"reward": model.CustomerLuckyWheelComboReward{
							Ticket: reward.TicketPattern,
						}},
					})
				case enum.ComboRewardItem.POINTS:
					errIncrPoint := client.Services.Customer.UpdatePoint(acc.AccountID, reward.Points, "LUCKY_WHEEL")
					if errIncrPoint == nil {
						model.CustomerLuckyWheelDB.UpdateOneWithOption(queryClc, bson.M{
							"$push": bson.M{"reward": model.CustomerLuckyWheelComboReward{
								Points: reward.Points,
							}},
						})
					}
				case enum.ComboRewardItem.OTHER:
					model.CustomerLuckyWheelDB.UpdateOneWithOption(queryUpdateCustomerLuckyWheel, bson.M{
						"$push": bson.M{"reward": model.CustomerLuckyWheelComboReward{
							RewardDescription: reward.RewardDescription,
						}},
					})
				}
			}
			model.LuckyWheelComboLogDB.Create(model.LuckyWheelComboLog{
				AccountID:      acc.AccountID,
				LuckyWheelCode: comboData.LuckyWheelCode,
				ComboCode:      comboCode,
				CustomerPhone:  phone,
				ComboImageUrl:  comboData.ImageUrl,
				IsHasGift:      comboData.Rewards != nil && len(*comboData.Rewards) > 0,
				Message:        comboData.TitleReward,
			})

			go func() {
				client.Services.Notification.CreateNotification(
					&model.Notification{
						Username:     customer.Username,
						UserID:       customer.AccountID,
						ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
						Topic:        "ANNOUNCEMENT",
						Title:        fmt.Sprintf("Chúc mừng bạn đã hoàn thành mảnh ghép %s và nhận được %s !", comboData.Name, comboData.TitleReward),
						Link:         "#",
						Tags:         []enum.NotificationTagEnum{enum.NotificationTag.PROMOTION, enum.NotificationTag.IMPORTANT},
					})
			}()
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: fmt.Sprintf("Chúc mừng bạn đã hoàn thành mảnh ghép %s !", comboData.Name),
	}
}

func checkCollectEnoughPieces(pieces []*model.ComboRewardPiece, comboType enum.ComboRewardEnumType) bool {
	collectedPositions := make([]int, 0)
	for _, p := range pieces {
		if p.CollectedQuantity >= p.ExpectQuantity {
			collectedPositions = append(collectedPositions, p.Position)
		}
	}
	if totalPiece, ok := enum.MapComboTypeToNumber[comboType]; ok {
		flag := true
		for index := 1; index <= totalPiece; index++ {
			visited := false
			for _, position := range collectedPositions {
				if position == index {
					visited = true
					break
				}
			}

			if !visited {
				flag = false
				break
			}
		}
		// check collected positions covered all positions of combo

		return flag
	}

	return false
}
