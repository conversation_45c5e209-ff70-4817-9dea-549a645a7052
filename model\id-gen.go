package model

import (
	"math"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	GAMIFICATION        = "GAMIFICATION"
	GAMIFICATION_DETAIL = "GAMIFICATION_DETAIL"
	GAMIFICATION_RESULT = "GAMIFICATION_RESULT"
)

// IdGen DB entity for gen code
type IdGen struct {
	ID    string `json:"id,omitempty" bson:"_id,omitempty"`
	Value int64  `json:"value,omitempty" bson:"value,omitempty"`
}

// IdGenDB DB model for gen code
var IdGenDB = &db.Instance{
	ColName:        "_id_gen",
	TemplateObject: &IdGen{},
}

func InitIdGenModel(s *mongo.Database) {

	IdGenDB.ApplyDatabase(s)

	gamificationGen := IdGen{
		ID:    GAMIFICATION,
		Value: 0,
	}

	gamificationDetailGen := IdGen{
		ID:    GAMIFICATION_DETAIL,
		Value: 0,
	}

	gamificationResultGen := IdGen{
		ID:    GAMIFICATION_RESULT,
		Value: 0,
	}

	promotionGen := IdGen{
		ID:    "PROMOTION_ID",
		Value: 0,
	}

	voucherGen := IdGen{
		ID:    "VOUCHER_ID",
		Value: 0,
	}

	campaignGen := IdGen{
		ID:    "CAMPAIGN_ID",
		Value: 2000,
	}

	ticketGen := IdGen{
		ID:    "TICKET_ID",
		Value: 10,
	}

	gamificationSpinnerGen := IdGen{
		ID:    "GAMIFICATION_SPINNER_ID",
		Value: 0,
	}

	IdGenDB.Create(gamificationGen)
	IdGenDB.Create(gamificationDetailGen)
	IdGenDB.Create(gamificationResultGen)
	IdGenDB.Create(promotionGen)
	IdGenDB.Create(voucherGen)
	IdGenDB.Create(campaignGen)
	IdGenDB.Create(ticketGen)
	IdGenDB.Create(gamificationSpinnerGen)
}

// convertToCode convert id from int to string
func convertToCode(number int64, length int64, template string) string {
	var result = ""
	var i = int64(0)
	var ln = int64(len(template))
	var capacity = int64(math.Pow(float64(ln), float64(length)))
	number = number % capacity
	for i < length {
		var cur = number % ln
		if i > 0 {
			cur = (cur + int64(result[i-1])) % ln
		}
		result = result + string(template[cur])
		number = number / ln
		i++
	}
	return result
}

func GenGamification() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: GAMIFICATION,
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 6, "346789QWERTYUPADFGHJKLXCVBNM")
}

func GenGamificationDetail() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: GAMIFICATION_DETAIL,
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 6, "346789QWERTYUPADFGHJKLXCVBNM")
}

func GenGamificationResult() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: GAMIFICATION_RESULT,
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 6, "346789QWERTYUPADFGHJKLXCVBNM")
}

// GenCode gen unique code in range, map from id
func GenCode(objectType string) string {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: objectType,
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return convertToCode(val.Value, 10, "346789QWERTYUPADFGHJKLXCVBNM")
}

func GenId(objectType string) int64 {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: objectType,
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value
}

func GenCampaignID() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: "CAMPAIGN_ID",
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 5, "346789HJKLXCVBQWERTYUPADFG")
}

func GenTicketID() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: "TICKET_ID",
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 8, "346789HJKLXCVBQWERTYUPADFG")
}

func GenCodeWithTime(index ...int) string {
	now := time.Now()
	key := now.UnixNano()
	if len(index) > 0 {
		key += int64(index[0])
	}
	return convertToCode(key, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GenShortCodeWithTime(index ...int) string {
	now := time.Now()
	key := now.UnixNano()
	if len(index) > 0 {
		key += int64(index[0])
	}
	return convertToCode(key, 4, "1246789HJKLXCVBQWERTYUPADFG")
}

func GenVoucherGroupTypeID() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: "VOUCHER_GROUP_TYPE_ID",
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 5, "346789HJKLXCVBQWERTYUPADFG")
}

func GenVoucherGroupConnectionID() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: "VOUCHER_GROUP_CONNECTION_ID",
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 5, "346789HJKLXCVBQWERTYUPADFG")
}

func GenVoucherID() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: "VOUCHER_ID",
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 5, "346789HJKLXCVBQWERTYUPADFG")
}

func GenLuckyWheel() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: "GAMIFICATION_SPINNER_ID",
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 6, "346789QWERTYUPADFGHJKLXCVBNM")
}

func GenCheckInConfigID() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: "CHECK_IN_CONFIG_ID",
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 6, "346789QWERTYUPADFGHJKLXCVBNM")
}

func GenCheckInDateRewardID() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: "CHECK_IN_DATE_ID",
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 6, "346789QWERTYUPADFGHJKLXCVBNM")
}

func GenCheckInLogID() (int64, string) {
	increResult := IdGenDB.IncreOne(IdGen{
		ID: "CHECK_IN_LOG_ID",
	}, "value", 1)
	val := increResult.Data.([]*IdGen)[0]
	return val.Value, convertToCode(val.Value, 6, "346789QWERTYUPADFGHJKLXCVBNM")
}

func GenVersion() string {
	now := time.Now()
	key := now.UnixNano()
	return convertToCode(key, 3, "1246789HJKLXCVBQWERTYUPADFG")
}
