package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type SkuSaleInfo struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreateBy        *string            `json:"createBy,omitempty" bson:"create_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SkuCode             string                         `json:"skuCode,omitempty" bson:"sku_code,omitempty"`
	Name                string                         `json:"name,omitempty" bson:"name,omitempty"`
	DealCode            *string                        `json:"dealCode,omitempty" bson:"deal_code,omitempty"`
	CampaignCode        *string                        `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	CampaignProductCode *string                        `json:"campaignProductCode,omitempty" bson:"campaign_product_code,omitempty"`
	CampaignType        enum.CampaignValueType         `json:"campaignType,omitempty" bson:"campaign_type,omitempty"`
	LocationCodes       *[]string                      `json:"locationCodes,omitempty" bson:"location_codes,omitempty"`
	CustomerScopeCodes  *[]string                      `json:"customerScopeCodes,omitempty" bson:"customer_scope_codes,omitempty"`
	StartTime           time.Time                      `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime             time.Time                      `json:"endTime,omitempty" bson:"end_time,omitempty"`
	SaleType            enum.SaleTypeValue             `json:"saleType,omitempty" bson:"sale_type,omitempty"`
	IsActive            *bool                          `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Status              enum.CampaignProductStatusType `json:"status,omitempty" bson:"status,omitempty"`
}

type SkuMain struct {
	Code     string               `json:"code,omitempty" bson:"code,omitempty"`
	Status   *enum.SkuStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	IsActive *bool                `json:"isActive,omitempty" bson:"is_active,omitempty"`
	SkuItems []SkuItemInfo        `json:"skuItems,omitempty" bson:"sku_items,omitempty"`
}

type SkuItemInfo struct {
	LocationCodes    *[]string            `json:"locationCodes,omitempty" bson:"location_codes,omitempty"`
	LocationCodesRaw *[]string            `json:"-" bson:"location_codes_raw,omitempty"`
	Tag              *[]string            `json:"tag,omitempty" bson:"tag,omitempty"`
	ItemCode         string               `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	Status           *enum.SkuStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	IsActive         *bool                `json:"isActive,omitempty" bson:"is_active,omitempty"`
	ApprovedDate     *time.Time           `json:"approvedDate,omitempty" bson:"approved_date,omitempty"` // the time that sku was approved to sell on the app
	LocationMap      map[string]int       `json:"locationMap" bson:"location_code_map,omitempty"`
	StatusPriority   int                  `json:"statusPriority,omitempty" bson:"status_priority,omitempty"` // cache only
	SellerPrivateSKU string               `json:"sellerPrivateSKU,omitempty" bson:"seller_private_sku,omitempty"`
}

var SkuCacheDB = &db.Instance{
	ColName:        "sku",
	TemplateObject: &Sku{},
}

var SkuMainCacheDB = &db.Instance{
	ColName:        "sku_main_v2",
	TemplateObject: &SkuMain{},
}

var SkuSaleInfoCacheDB = &db.Instance{
	ColName:        "sku_sale_info",
	TemplateObject: &SkuSaleInfo{},
}

// InitSkuModel is func init model SKU
func InitSkuModel(dbInst *db.Instance, s *mongo.Database) {
	dbInst.ApplyDatabase(s)
}

// InitSkuSaleInfoModel is func init model SKU
func InitSkuSaleInfoModel(dbInst *db.Instance, s *mongo.Database) {
	dbInst.ApplyDatabase(s)

	t := true
	_ = dbInst.CreateIndex(bson.D{
		primitive.E{Key: "sku_code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		// Unique:     &t,
	})

	_ = dbInst.CreateIndex(bson.D{
		primitive.E{Key: "deal_code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = dbInst.CreateIndex(bson.D{
		primitive.E{Key: "campaign_code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Sparse:     &t,
	})

	_ = dbInst.CreateIndex(bson.D{
		primitive.E{Key: "is_active", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Sparse:     &t,
	})
}
