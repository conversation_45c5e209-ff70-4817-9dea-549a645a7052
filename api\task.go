package api

import (
	"fmt"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
	"time"
)

func PushSyncCustomerVoucherJob(req sdk.APIRequest, resp sdk.APIResponder) error {
	data := struct {
		AccountID int `json:"accountID" bson:"account_id"`
	}{}
	err := req.GetContent(&data)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}
	isNewSyncRecord := false
	qLastSync := model.CustomerVoucherSyncTimeDB.QueryOne(bson.M{"account_id": data.AccountID})
	if qLastSync.Status == common.APIStatus.Ok {
		lastSync := qLastSync.Data.([]*model.CustomerVoucherSyncTime)[0]
		if lastSync.LastSyncTime != nil {
			if time.Since(*lastSync.LastSyncTime) < 15*time.Minute {
				return resp.Respond(&common.APIResponse{
					Status:  common.APIStatus.Ok,
					Message: "Success",
				})
			}
		}
	} else {
		isNewSyncRecord = true
	}
	now := time.Now()
	//readyTime := now.Add(15 * time.Minute)
	readyTime := now.Add(5 * time.Second)
	_ = model.SyncCustomerVoucherJob.Push(data, &job.JobItemMetadata{
		Keys:      []string{fmt.Sprintf("%d", data.AccountID)},
		Topic:     "default",
		ReadyTime: &readyTime,
		UniqueKey: fmt.Sprintf("%d", data.AccountID),
	})
	if isNewSyncRecord {
		_ = model.CustomerVoucherSyncTimeDB.Create(&model.CustomerVoucherSyncTime{
			AccountID:    data.AccountID,
			LastSyncTime: &now,
		})
	} else {
		_ = model.CustomerVoucherSyncTimeDB.UpdateOne(bson.M{"account_id": data.AccountID}, model.CustomerVoucherSyncTime{LastSyncTime: &now})
	}
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
	})
}
