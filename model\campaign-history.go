package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CampaignHistory struct {
	ID                  primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime         *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CustomerID          int64              `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	OrderID             int64              `json:"orderId,omitempty" bson:"order_id,omitempty"`
	Quantity            int                `json:"quantity,omitempty" bson:"quantity,omitempty"`
	CampaignCode        string             `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	CampaignProductCode string             `json:"campaignProductCode,omitempty" bson:"campaign_product_code,omitempty"`
	Sku                 string             `json:"sku,omitempty" bson:"sku,omitempty"`
}

var CampaignHistoryDB = &db.Instance{
	ColName:        "campaign_history",
	TemplateObject: &CampaignHistory{},
}

// InitCampaignHistoryModel is func init model
func InitCampaignHistoryModel(s *mongo.Database) {
	CampaignHistoryDB.ApplyDatabase(s)

	// t := true

	// CampaignHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// CampaignHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "campaign_product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// CampaignHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// CampaignHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "sku", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
