package enum

// SkuStatusValue ..
type SkuStatusValue string

// nolint
type skuStatus struct {
	NORMAL          SkuStatusValue
	LIMIT           SkuStatusValue // gioi han
	OUT_OF_STOCK    SkuStatusValue // tam het hang
	SUSPENDED       SkuStatusValue // ngung kinh doanh
	STOP_PRODUCING  SkuStatusValue // Ngưng sản xuất
	NEAR_EXPIRATION SkuStatusValue // can date
	GIFT            SkuStatusValue // qua tang
}

// SkuStatus ...
var SkuStatus = &skuStatus{
	"NORMAL",
	"LIMIT",
	"OUT_OF_STOCK",
	"SUSPENDED",
	"STOP_PRODUCING",
	"NEAR_EXPIRATION",
	"GIFT",
}

// SkuStatusName ...
var SkuStatusName = map[SkuStatusValue]string{
	SkuStatus.NORMAL:          "Đang bán",
	SkuStatus.LIMIT:           "<PERSON><PERSON> giới hạn",
	SkuStatus.OUT_OF_STOCK:    "Tạm hết hàng",
	SkuStatus.SUSPENDED:       "Ngưng bán",
	SkuStatus.STOP_PRODUCING:  "Ngưng sản xuất",
	SkuStatus.NEAR_EXPIRATION: "Gần hết hạn",
	SkuStatus.GIFT:            "Quà tặng",
}

var SortPrioritySkuStatus = map[SkuStatusValue]int{
	SkuStatus.NORMAL:          1,
	SkuStatus.LIMIT:           1,
	SkuStatus.OUT_OF_STOCK:    -1,
	SkuStatus.SUSPENDED:       -1,
	SkuStatus.STOP_PRODUCING:  -1,
	SkuStatus.NEAR_EXPIRATION: 1,
	SkuStatus.GIFT:            -1,
}

type SaleTypeValue string
type saleTypeValue struct {
	DEAL     SaleTypeValue
	CAMPAIGN SaleTypeValue
}

var SaleType = &saleTypeValue{
	"DEAL",
	"CAMPAIGN",
}
