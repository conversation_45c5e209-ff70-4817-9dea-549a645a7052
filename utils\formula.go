package utils

import (
	"github.com/Knetic/govaluate"
)

// ParseFormulaToInterface ...
func ParseFormulaToInterface(formula string, params map[string]interface{}) (interface{}, error) {
	expression, err := govaluate.NewEvaluableExpression(formula)
	if err != nil {
		return nil, err
	}
	// Automatically set missing parameters to default (0)
	params = setMissingParamsToDefault(expression, params, 0)

	result, err := expression.Evaluate(params)
	if err != nil {
		// if err == No parameter 'scope_me' found.
		return nil, err
	}

	return result, nil
}

// Helper function to set missing parameters to default (e.g., 0)
func setMissingParamsToDefault(expression *govaluate.EvaluableExpression, params map[string]interface{}, defaultValue interface{}) map[string]interface{} {
	// Extract variables used in the expression
	variables := expression.Vars()

	// Check for missing variables and set default values
	for _, variable := range variables {
		if _, exists := params[variable]; !exists {
			params[variable] = defaultValue
		}
	}

	return params
}
