package model

import (
	"errors"
	"strings"
	"unicode"

	"github.com/go-playground/locales/en"
	ut "github.com/go-playground/universal-translator"
	validator "github.com/go-playground/validator/v10"
	en_translations "github.com/go-playground/validator/v10/translations/en"
)

var (
	uni     *ut.UniversalTranslator
	Checker *CustomValidator
)

type CustomValidator struct {
	Validator *validator.Validate
	Trans     ut.Translator
}

// Validate is func check valid struct
func (cv *CustomValidator) Validate(i interface{}) error {
	err := cv.Validator.Struct(i)
	if err != nil {

		errs := err.(validator.ValidationErrors)

		for _, e := range errs {
			return errors.New(e.Translate(cv.Trans))
		}
	}
	return nil
}

func InitCustomValidator() *CustomValidator {
	en := en.New()
	uni = ut.New(en, en)
	trans, _ := uni.GetTranslator("en")
	validate := validator.New()
	en_translations.RegisterDefaultTranslations(validate, trans)

	// TODO
	validate.RegisterTranslation("required", trans, func(ut ut.Translator) error {
		return ut.Add("required", "Vui lòng nhập {0}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("required", lowerFirst(fe.Field()))
		return t
	})

	validate.RegisterTranslation("numeric", trans, func(ut ut.Translator) error {
		return ut.Add("numeric", "Vui lòng nhập {0} dưới dạng kí tự số từ 0 đến 9", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("numeric", lowerFirst(fe.Field()))
		return t
	})

	validate.RegisterTranslation("min", trans, func(ut ut.Translator) error {
		return ut.Add("min", "Vui lòng nhập giá trị {0} lớn hơn {1} kí tự", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("min", lowerFirst(fe.Field()), fe.Param())
		return t
	})

	validate.RegisterTranslation("max", trans, func(ut ut.Translator) error {
		return ut.Add("max", "Vui lòng nhập giá trị {0} nhỏ hơn {1} kí tự", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("max", lowerFirst(fe.Field()), fe.Param())
		return t
	})

	validate.RegisterTranslation("email", trans, func(ut ut.Translator) error {
		return ut.Add("email", "{0} không hợp lệ.", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("email", lowerFirst(fe.Field()))
		return t
	})

	validate.RegisterTranslation("excludesall", trans, func(ut ut.Translator) error {
		return ut.Add("excludesall", "Vui lòng nhập {0} không tồn tại kí tự {1}.", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("excludesall", lowerFirst(fe.Field()), fe.Param())
		return t
	})

	return &CustomValidator{Validator: validate, Trans: trans}
}

func lowerFirst(str string) string {
	for i := range str {
		if i+1 < len(str) && unicode.IsUpper(rune(str[i+1])) {
			continue
		}
		return strings.ToLower(str[:i+1]) + str[i+1:]
	}
	return strings.ToLower(str)
}

func init() {
	Checker = InitCustomValidator()
}
