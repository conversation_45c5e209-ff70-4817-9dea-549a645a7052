package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	// VGC means Voucher Group Connection

	// VGC_SPIT_SIGN is sign to split codes, quantity. Avoid choose sign that can be used in code, eg: "_" => FAIL
	VGC_SPIT_SIGN          = "-"
	VGC_CASE_FORMAT        = "%s" + VGC_SPIT_SIGN + "%d" + VGC_SPIT_SIGN + "%s" + VGC_SPIT_SIGN + "%d"
	VGC_CASE_REGEX_PATTERN = `(\w+)` + VGC_SPIT_SIGN + `(\d+)` + VGC_SPIT_SIGN + `(\w+)` + VGC_SPIT_SIGN + `(\d+)`
)

type VoucherGroupConnection struct {
	ID             *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ConnectionCode string              `json:"connectionCode,omitempty" bson:"connection_code,omitempty"`
	ConnectionId   int64               `json:"connectionId,omitempty" bson:"connection_id,omitempty"`
	FirstCode      string              `json:"firstCode,omitempty" bson:"first_code,omitempty" validate:"required"`
	SecondCode     string              `json:"secondCode,omitempty" bson:"second_code,omitempty" validate:"required"`
	FirstQuantity  *int                `json:"firstQuantity,omitempty" bson:"first_quantity,omitempty" validate:"required,min=1"`
	SecondQuantity *int                `json:"secondQuantity,omitempty" bson:"second_quantity,omitempty" validate:"required,min=1"`
	IsActive       *bool               `json:"isActive,omitempty" bson:"is_active,omitempty"`

	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       int64      `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	UpdatedBy       int64      `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
}

type VoucherGroupConnectionQuery struct {
	ConnectionCode   string `json:"connectionCode,omitempty" bson:"connection_code,omitempty"`
	ConnectionId     int64  `json:"connectionId,omitempty" bson:"connection_id,omitempty"`
	FirstCode        string `json:"firstCode,omitempty" bson:"first_code,omitempty" validate:"required"`
	SecondCode       string `json:"secondCode,omitempty" bson:"second_code,omitempty" validate:"required"`
	GetTotal         bool   `bson:"-"`
	Limit            int64  `bson:"-"`
	Offset           int64  `bson:"-"`
	SearchText       string `bson:"-"`
	VoucherGroupCode string `json:"voucherGroupCode,omitempty" bson:"-"`
	IsActive         *bool  `json:"isActive,omitempty" bson:"is_active,omitempty"`

	ComplexOrQueries []*bson.M `json:"-" bson:"$or,omitempty"`
}

var VoucherGroupConnectionDB = &db.Instance{
	ColName:        "voucher_group_connection",
	TemplateObject: &VoucherGroupConnection{},
}

// InitVoucherTypeModel is func init model
func InitVoucherGroupConnectionModel(s *mongo.Database) {
	VoucherGroupConnectionDB.ApplyDatabase(s)
}
