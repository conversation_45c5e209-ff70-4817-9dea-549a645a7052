# Changelog

## 0.6.0

- Added information on the model of mobile devices. See [746647ad73b5](https://github.com/mssola/user_agent/commit/746647ad73b5ad8648175bbd07319c0a8ac559c6).
- Added support for PhantomJS. See [6b5e6f6ebfa8](https://github.com/mssola/user_agent/commit/6b5e6f6ebfa87464ccdb42bac5448cbf46ce1ba1).

## 0.5.4

- Add detection of <PERSON><PERSON> Co<PERSON> Browser. See [897eb45aec23](https://github.com/mssola/user_agent/commit/897eb45aec2330e7566c48c9e54192aae84bd8e9).
- Add detection of Headless Chrome. See [897eb45aec23](https://github.com/mssola/user_agent/commit/897eb45aec2330e7566c48c9e54192aae84bd8e9).
- Add detection of iOS WebViews. See [897eb45aec23](https://github.com/mssola/user_agent/commit/897eb45aec2330e7566c48c9e54192aae84bd8e9).

## 0.5.3

- Fix detection of Firefox on iPad. See [42e4a8f39125](https://github.com/mssola/user_agent/commit/42e4a8f39125a6680fb5367a4602963f1351e069).
- Fix detection of Linux ARM-based Android. See [3b0e113c8047](https://github.com/mssola/user_agent/commit/3b0e113c804708c01de00c27aae07d2acfee40d8).
- Add detection of Chromium Edge on Windows. See [ea81f1e9d61c](https://github.com/mssola/user_agent/commit/ea81f1e9d61c094df4156690a8f4d5481b0d6c4a).
- Add detection of OkHttp. See [6b33e248e796](https://github.com/mssola/user_agent/commit/6b33e248e7969cf3e76128a34d33be88d4eb0dc8).

## 0.5.2

- Detect Electron. See [commit](https://github.com/mssola/user_agent/commit/1a36963d74c0efca7de80dc7518a0958c66b3c4f).
- Add support for both http and https site urls. See [commit](https://github.com/mssola/user_agent/commit/d78bf2c5886a0ab7e1cf90b68c808fe3e3ab6f8c).
- Add more support for BingBot. See [commit](https://github.com/mssola/user_agent/commit/c6402a7b8aefdc4acfbf1e7f3b43eac0b266e49e).
- Add a test case for Firefox focus on iOS. See [commit](https://github.com/mssola/user_agent/commit/a1e9c19d5a6887a17cef1d249118ccbd45cf4c0b).
- Detect iMessage-Preview. See [commit](https://github.com/mssola/user_agent/commit/e8f5e19ded9711ee1f4b43218b9d57d00ef5c26a).

## 0.5.1

- add Firefox for iOS. See [commit](https://github.com/mssola/user_agent/commit/00a868fa17e7).
- Add go.mod. See [commit](https://github.com/mssola/user_agent/commit/8c16c37f4e07).
- Use CodeLingo to Address Further Issues. See [commit](https://github.com/mssola/user_agent/commit/7e313fc62553).
- Fix function comments based on best practices from Effective Go. See [commit](https://github.com/mssola/user_agent/commit/95b0c164394f).
- test: mobile Yandex Browser. See [commit](https://github.com/mssola/user_agent/commit/1df9e04ee4f5).
- Add Yandex browser. See [commit](https://github.com/mssola/user_agent/commit/6eb76c60b5e8).
- Updating license notice. See [commit](https://github.com/mssola/user_agent/commit/8b3999083770).
- Detect Chrome for iOS correctly. See [commit](https://github.com/mssola/user_agent/commit/82f141dea4a8).
- Facebook App Handling. See [commit](https://github.com/mssola/user_agent/commit/5723c361ed97).
- Add a new google bot user agent format. See [commit](https://github.com/mssola/user_agent/commit/57c32981bd5f).

## 0.5.0

### Newly supported and improvements

- Added support for Microsoft Edge. See [commit](https://github.com/mssola/user_agent/commit/f659b9863849).
- Precompile regular expressions. See [commit](https://github.com/mssola/user_agent/commit/783ec61292ae).
- Added support for Dalvik user agent parsing. See [commit](https://github.com/mssola/user_agent/commit/78413629666f).
- Improved bot support (also e25e612b37a4). See [commit](https://github.com/mssola/user_agent/commit/0319fcf00bfd).
- Add Chromium support and Ubuntu specific tests. See [commit](https://github.com/mssola/user_agent/commit/6e7843e05771).
- Add OSInfo function to user agent (also 7286ca6abc28). See [commit](https://github.com/mssola/user_agent/commit/3335cae017e7).
- Detect updated UA for Googlebot. See [commit](https://github.com/mssola/user_agent/commit/6fe362d7cd64).
- Adds the Adsense bot (mobile). See [commit](https://github.com/mssola/user_agent/commit/1438bfba89d7).

### Fixes

- Fixed bug when extracting windows 10. See [commit](https://github.com/mssola/user_agent/commit/8d86c2cf88bf).
- Fixed bug on mobile Firefox browsers running on Android OS versions that report their version number inline.. See [commit](https://github.com/mssola/user_agent/commit/9d00ff9e4202).

### Other

- Improved testing infrastructure. See [commit](https://github.com/mssola/user_agent/commit/63395b193f8812526305bec75ea7117262a124aa).

## Older releases

See the description on each release
[here](https://github.com/mssola/user_agent/releases).
