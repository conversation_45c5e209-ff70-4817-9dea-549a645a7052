package api

import (
	"fmt"
	"reflect"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// VoucherCheckWithCart ...
func VoucherCheckWithCart(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.VoucherCheckRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if input.GetVoucherAutoApply {
		newRedeemApplyResult := make([]*model.PromoApplyResult, 0)
		newRedeemCode := make([]string, 0)
		redeemAuto := make(map[string]string)
		for _, v := range input.Cart.RedeemApplyResult {
			if !v.AutoApply {
				newRedeemApplyResult = append(newRedeemApplyResult, v)
			}
			if v.AutoApply && v.Code != "" {
				redeemAuto[v.Code] = v.Code
			}
		}
		for _, v := range input.VoucherCodes {
			if _, ok := redeemAuto[v]; !ok {
				newRedeemCode = append(newRedeemCode, v)
			}
		}
		input.Cart.RedeemApplyResult = newRedeemApplyResult
		input.VoucherCodes = newRedeemCode
	}

	input.Cart.SourceDetail = input.SourceDetail

	if input.SystemDisplay == "" {
		input.SystemDisplay = "BUYMED"
	} else if input.SystemDisplay == "ALL" {
		input.SystemDisplay = ""
	}

	if !input.GetVoucherAutoApply && !input.GetVoucherAutoByPayment {
		return resp.Respond(action.CheckVoucherNoAuto(&input))
	}

	return resp.Respond(action.CheckVoucherWithAuto(&input))
}

// VoucherActiveList ...
func VoucherActiveList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.MeVoucherListRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	isCartEmpty := false
	if input.Cart == nil {
		input.Cart = &model.Cart{}
		isCartEmpty = true
	} else {
		isCartEmpty = reflect.DeepEqual(*input.Cart, model.Cart{}) || len(input.Cart.CartItems) == 0
	}
	input.Cart.IsCartEmpty = isCartEmpty && input.Cart.TotalItem == 0
	// filter cart
	sellerMap := make(map[string]bool)
	if input.Cart.CartItems != nil && input.Cart.TotalItem > 0 {
		allCartItems := make([]model.CartItemInternal, 0)
		mapCartItems := make(map[string]model.CartItemInternal)
		newCartItems := make([]model.CartItemInternal, 0)
		input.Cart.Price = 0
		for _, item := range input.Cart.CartItems {
			if item.ProductSKU == "" {
				continue
			}
			allCartItems = append(allCartItems, item)
			mapCartItems[item.ProductSKU] = item
			if item.IsSelected == nil || (item.IsSelected != nil && *item.IsSelected) {
				newCartItems = append(newCartItems, item)
				if item.Total == 0 {
					item.Total = item.UnitPrice * item.Quantity
				}
				input.Cart.Price += item.Total
				if _, ok := sellerMap[item.SellerCode]; !ok {
					sellerMap[item.SellerCode] = true
					input.Cart.SellerCodes = append(input.Cart.SellerCodes, item.SellerCode)
				}
			}
		}
		input.Cart.AllCartItems = allCartItems
		input.Cart.MapCartItems = mapCartItems
		input.Cart.CartItems = newCartItems
		input.Cart.TotalItem = len(newCartItems)
	}
	if input.SourceDetail == nil {
		usInfo := getUAInfo(req.GetHeader("User-Agent"))
		sourceDetail := &model.OrderSourceDetail{
			Os:             usInfo.OSName,
			OsVersion:      usInfo.OSVersion,
			Browser:        usInfo.ClientName,
			BrowserVersion: usInfo.ClientVersion,
			Platform:       usInfo.Platform,
		}
		input.SourceDetail = sourceDetail
	}

	// Save current device,platform detail to cart model for validation
	input.Cart.SourceDetail = input.SourceDetail
	input.Cart.RedeemCode = []string{}
	input.Cart.PaymentMethod = "" // allow payment method always valid
	if len(input.Cart.RedeemApplyResult) > 0 {
		for _, voucher := range input.Cart.RedeemApplyResult {
			if voucher.Code != "" {
				input.Cart.RedeemCode = append(input.Cart.RedeemCode, voucher.Code)
			}
		}
	}
	if input.Cart.TotalItem == 0 {
		input.Cart.TotalItem = len(input.Cart.CartItems)
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if input.Offset < 0 {
		input.Offset = 0
	}

	if input.Limit <= 0 {
		input.Limit = 20
	}
	//input.Limit = 1000
	var query = model.Voucher{}
	if input.Search != "" {
		searchItem := parserQ(input.Search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", searchItem), Options: ""},
					"type":     enum.VoucherType.PUBLIC,
				},
				{
					"code": strings.ToUpper(input.Search),
					"type": enum.VoucherType.PRIVATE,
				},
			},
		})
	} else if len(input.Cart.RedeemCode) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{"type": enum.VoucherType.PUBLIC},
				{"code": &bson.M{"$in": input.Cart.RedeemCode}}, // Get private voucher but in cart
			},
		})
	} else {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"type": enum.VoucherType.PUBLIC,
		})
	}

	if input.AccountID == 0 && getActionSource(req) != nil {
		acc := getActionSource(req)
		input.AccountID = acc.AccountID
	}

	query.SystemDisplay = input.SystemDisplay
	if query.SystemDisplay == "" {
		query.SystemDisplay = "BUYMED"
	} else if query.SystemDisplay == "ALL" {
		query.SystemDisplay = ""
	}

	return resp.Respond(action.VoucherActiveList(&query, &input))
}

// SelfVoucherActiveList ...
func SelfVoucherActiveList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = int64(sdk.ParseInt(req.GetParam("offset"), 0))
		limit    = int64(sdk.ParseInt(req.GetParam("limit"), 20))
		getTotal = req.GetParam("getTotal") == "true"
		search   = req.GetParam("search")
		scope    = req.GetParam("scope")

		systemDisplay = req.GetParam("systemDisplay")
	)

	usInfo := getUAInfo(req.GetHeader("User-Agent"))
	sourceDetail := &model.OrderSourceDetail{
		Os:             usInfo.OSName,
		OsVersion:      usInfo.OSVersion,
		Browser:        usInfo.ClientName,
		BrowserVersion: usInfo.ClientVersion,
		Platform:       usInfo.Platform,
	}

	var query = model.Voucher{
		SystemDisplay: systemDisplay,
	}
	if search != "" {
		searchItem := parserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", searchItem), Options: ""},
					"type":     enum.VoucherType.PUBLIC,
				},
				{
					"code": strings.ToUpper(search),
					"type": enum.VoucherType.PRIVATE,
				},
			},
		})
	} else {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"type": enum.VoucherType.PUBLIC,
		})
	}

	if query.SystemDisplay == "" {
		query.SystemDisplay = "BUYMED"
	}

	return resp.Respond(action.SelfVoucherActiveList(getActionSource(req).AccountID, &query, offset, limit, getTotal, scope,
		sourceDetail))
}

// get voucher condition message
func VoucherConditionMessage(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = struct {
		Cart          model.Cart               `json:"cart,omitempty" validate:"required"`
		AccountID     int64                    `json:"accountId"`
		SystemDisplay string                   `json:"systemDisplay,omitempty" bson:"-"`
		SourceDetail  *model.OrderSourceDetail `json:"sourceDetail,omitempty" bson:"-"`
		Codes         []string                 `json:"codes,omitempty"`
	}{}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if len(input.Codes) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy mã voucher",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	isCartEmpty := reflect.DeepEqual(input.Cart, model.Cart{}) || len(input.Cart.CartItems) == 0
	input.Cart.IsCartEmpty = isCartEmpty && input.Cart.TotalItem == 0
	// filter cart
	sellerMap := make(map[string]bool)
	if input.Cart.CartItems != nil && input.Cart.TotalItem > 0 {
		newCartItems := make([]model.CartItemInternal, 0)
		input.Cart.Price = 0
		for _, item := range input.Cart.CartItems {
			if item.ProductSKU == "" {
				continue
			}
			if item.IsSelected == nil || (item.IsSelected != nil && *item.IsSelected) {
				newCartItems = append(newCartItems, item)
				if item.Total == 0 {
					item.Total = item.UnitPrice * item.Quantity
				}
				input.Cart.Price += item.Total
				if _, ok := sellerMap[item.SellerCode]; !ok {
					sellerMap[item.SellerCode] = true
					input.Cart.SellerCodes = append(input.Cart.SellerCodes, item.SellerCode)
				}
			}
		}
		input.Cart.CartItems = newCartItems
		input.Cart.TotalItem = len(newCartItems)
	}
	if input.SourceDetail == nil {
		usInfo := getUAInfo(req.GetHeader("User-Agent"))
		sourceDetail := &model.OrderSourceDetail{
			Os:             usInfo.OSName,
			OsVersion:      usInfo.OSVersion,
			Browser:        usInfo.ClientName,
			BrowserVersion: usInfo.ClientVersion,
			Platform:       usInfo.Platform,
		}
		input.SourceDetail = sourceDetail
	}

	// Save current device,platform detail to cart model for validation
	input.Cart.SourceDetail = input.SourceDetail
	input.Cart.RedeemCode = []string{}
	if len(input.Cart.RedeemApplyResult) > 0 {
		for _, voucher := range input.Cart.RedeemApplyResult {
			if voucher.Code != "" {
				input.Cart.RedeemCode = append(input.Cart.RedeemCode, voucher.Code)
			}
		}
	}
	if input.Cart.TotalItem == 0 {
		input.Cart.TotalItem = len(input.Cart.CartItems)
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	var query = model.Voucher{}
	if input.AccountID == 0 && getActionSource(req) != nil {
		acc := getActionSource(req)
		input.AccountID = acc.AccountID
	}
	query.SystemDisplay = input.SystemDisplay

	return resp.Respond(action.VoucherConditionMessageList(
		input.AccountID,
		&query,
		&input.Cart,
		input.Codes,
	))
}
