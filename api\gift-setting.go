package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

// GiftSettingCreate is func to create Gift Setting
func GiftSettingCreate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input *model.GiftSetting
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	var UserInfo = new(model.ActionSource)
	var source = req.GetHeader("X-Source")
	if source != "" {
		err := json.Unmarshal([]byte(source), &UserInfo)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Token error." + err.Error(),
			})
		}
	}

	if UserInfo.Account == nil || UserInfo.Account.AccountID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "ACCOUNT_NOT_FOUND",
			Message:   "Không tìm thấy thông tin tài khoản.",
		})
	}

	return resp.Respond(action.CreateGiftSetting(UserInfo.Account.Username, input))
}

// GiftSettingGet is func to get gift setting
func GiftSettingGet(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GetGiftSetting())
}

// GetSelfGiftInfo is func
func GetSelfGiftInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GetSelfGiftInfo(getActionSource(req)))
}
