package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type VoucherCustomer struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"-" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"-" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"-" bson:"last_updated_time,omitempty"`

	VoucherID   int64  `json:"voucherId,omitempty" bson:"voucher_id,omitempty"`
	VoucherCode string `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	CustomerID  int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
}
