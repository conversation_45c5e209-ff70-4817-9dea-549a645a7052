package api

import (
	"encoding/json"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

// TrackingCreate is func create a tracking event
func TrackingCreate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Tracking
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.<PERSON>rror(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.<PERSON>(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.TrackingCreate(getActionSource(req), &input))
}

// TrackingEventCreate is func create a tracking event
func TrackingEventCreate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.TrackingEvent
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.TrackingEventCreate(getActionSource(req), &input))
}

func TrackingEventGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	query := model.TrackingEvent{}

	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: "INVALID",
			})
		}
	}

	return resp.Respond(action.TrackingEventGetList(&query, offset, limit, getTotal))
}

func TrackingGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	query := model.Tracking{}

	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: "INVALID",
			})
		}
	}

	return resp.Respond(action.TrackingGetList(&query, offset, limit, getTotal))
}

func TrackingGetStatistic(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")
	)

	query := model.Tracking{}

	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: "INVALID",
			})
		}
	}

	return resp.Respond(action.TrackingStatistic(&query))
}
