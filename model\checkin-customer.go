package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CheckinCustomer struct {
	ID              primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	CreatedTime     *time.Time         `bson:"created_time,omitempty" json:"createdTime,omitempty"`
	LastUpdatedTime *time.Time         `bson:"last_updated_time,omitempty" json:"lastUpdatedTime,omitempty"`

	Code        string `json:"code,omitempty" bson:"code,omitempty"`
	CustomerID  int64  `bson:"customer_id,omitempty" json:"customerId,omitempty"`
	AccountID   int64  `bson:"account_id,omitempty" json:"accountId,omitempty"`
	CheckinCode string `bson:"checkin_code,omitempty" json:"checkinCode,omitempty"`

	JoinType string     `json:"joinType,omitempty" bson:"join_type,omitempty"`
	JoinTime *time.Time `json:"joinTime,omitempty" bson:"join_time,omitempty"`
	Status   string     `json:"status,omitempty" bson:"status,omitempty"`

	Rewards []CustomerCheckinReward `json:"rewards,omitempty" bson:"rewards,omitempty"`

	ComplexQuery []*bson.M `bson:"$and,omitempty" json:"-"`
}

type CustomerCheckinReward struct {
	ItemCode          string `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	VoucherCode       string `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	Ticket            string `json:"ticket,omitempty" bson:"ticket,omitempty"`
	TurnsRotation     int64  `json:"turnsRotation,omitempty" bson:"turns_rotation,omitempty"`
	Points            int64  `json:"points,omitempty" bson:"points,omitempty"`
	RewardDescription string `json:"rewardDescription,omitempty" bson:"reward_description,omitempty"`
}

var CheckinCustomerDB = &db.Instance{
	ColName:        "checkin_customer",
	TemplateObject: &CheckinCustomer{},
}

func InitCheckinCustomerModel(s *mongo.Database) {
	CheckinCustomerDB.ApplyDatabase(s)
}
