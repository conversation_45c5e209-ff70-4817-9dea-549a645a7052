package order

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathOrderSummation        = "/marketplace/order/v2/order/summation"
	pathGetOrder              = "/marketplace/order/v2/order"
	pathCountOrderValue       = "/marketplace/order/v2/order/count-value"
	pathCountOrderSellerValue = "/marketplace/order/v2/order/count-value-by-seller"
	pathCreateDealApplyResult = "/marketplace/order/v2/deal-apply-result"
	pathGetOrderList          = "/marketplace/order/v2/order/list"
)

// OrderResponse is resp from order client
type OrderSummationResponse struct {
	model.BaseAPIResponse
	Data []*model.SummationOrderInfo `json:"data"`
}

// orderInfoResponse ...
type orderInfoResponse struct {
	model.BaseAPIResponse
	Data []*model.DealApplyRequest `json:"data"`
}

// Client is model define CustomerClient
type Client struct {
	orderClient *client.RestClient
	headers     map[string]string
}

// NewServiceClient is func create new service client
func NewServiceClient(apiHost, apiKey string, s *mongo.Database) *Client {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	client := &Client{
		orderClient: client.NewRESTClient(
			apiHost,
			"log_order",
			25*time.Second,
			1,
			50*time.Millisecond,
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	// client.orderClient.SetDBLog(s)
	return client
}

// GetOrderByID ...
func (cli *Client) GetOrderSummation(customerID int64, info string) *common.APIResponse {

	params := map[string]string{
		"customerId": strconv.FormatInt(customerID, 10),
		"info":       info,
	}

	res, err := cli.orderClient.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathOrderSummation, nil)
	if err != nil {
		return &common.APIResponse{Status: common.APIStatus.Error, Message: "Failed to call api Get order summation info: " + err.Error()}
	}

	var result *OrderSummationResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{Status: common.APIStatus.Error, Message: "Failed to parse Get order summation info: " + err.Error()}
	}

	return &common.APIResponse{
		Status:    result.Status,
		Message:   result.Message,
		Data:      result.Data,
		ErrorCode: result.ErrorCode,
	}
}

func (cli *Client) GetOrder(orderId int64) (*model.Order, error) {
	params := map[string]string{
		"q": fmt.Sprintf("{\"orderId\": %d}", orderId),
	}

	result, err := cli.orderClient.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetOrder, nil)
	if err != nil {
		return nil, err
	}

	var resp model.OrderGetResponse
	err = json.Unmarshal([]byte(result.Body), &resp)
	if err != nil {
		return nil, err
	}

	if resp.Status != common.APIStatus.Ok || len(resp.Data) == 0 {
		return nil, fmt.Errorf(resp.Message)
	}
	return resp.Data[0], nil
}

// CountOrderValue ...
func (cli *Client) CountOrderValue(body *model.RequestCountOrderPoint) ([]*model.OrderCountPoint, error) {

	params := map[string]string{}
	path := pathCountOrderValue
	if body.SellerCode != "" {
		path = pathCountOrderSellerValue
	}
	if body.GamificationCode == "NBT3CY" {
		path = "/marketplace/order/v2/order/count-value-gamification-tour-dl"
	}
	res, err := cli.orderClient.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, body, path, nil)
	if err != nil {
		return nil, err
	}

	var result *model.CountOrderPointResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

// UpdateDealApplyResult ...
func (cli *Client) UpdateDealApplyResult(in model.DealApplyRequest) (*model.DealApplyRequest, error) {
	res, err := cli.orderClient.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, in, pathCreateDealApplyResult, nil)
	if err != nil {
		return nil, err
	}
	var result *orderInfoResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// GetOrdersByOrderIds ...
func (cli *Client) GetOrdersByOrderIds(ids []int64) ([]*model.Order, error) {

	params := map[string]string{}
	var payload = struct {
		Ids []int64 `json:"ids"`
	}{
		Ids: ids,
	}

	res, err := cli.orderClient.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, payload, pathGetOrderList, nil)
	if err != nil {
		return nil, err
	}

	var result *model.OrderGetResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

// GetLastOrderSuccessByAccountID ...
func (cli *Client) GetLastOrderSuccessByAccountID(accountId int64) (*model.Order, error) {
	params := map[string]string{
		"q":     fmt.Sprintf("{\"accountId\": %d, \"statusNotIn\": [\"CANCEL\"]}", accountId),
		"limit": "1",
	}

	result, err := cli.orderClient.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetOrderList, nil)
	if err != nil {
		return nil, err
	}

	var resp model.OrderGetResponse
	err = json.Unmarshal([]byte(result.Body), &resp)
	if err != nil {
		return nil, err
	}

	if resp.Status != common.APIStatus.Ok || len(resp.Data) == 0 {
		return nil, fmt.Errorf(resp.Message)
	}
	return resp.Data[0], nil
}
