package action

import (
	"fmt"
	"sort"
	"strings"
	"sync"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
)

var productGiftCache = sdk.NewLCacheRefreshMode(100, 60*60, false)

func GetVoucherSuggestions(cart *model.Cart) *common.APIResponse {
	if cart == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "MISSING_CART",
			Message:   "`cart` must not be nil",
		}
	}

	s := &suggestion{
		InUse:    make(map[string]bool),
		Customer: &model.Customer{},
		Cart:     &model.Cart{},
		Setting:  &model.Setting{},

		SellerOfGroupMap: make(map[string]string),
		GroupOfSellerMap: make(map[string]string),

		ProductNameByCodeMap: make(map[string]string),
		UsedMap:              make(map[string]*model.UserPromotion),
	}

	// Init SellerItemSelected
	for _, v := range cart.RedeemApplyResult {
		if v.CanUse {
			s.InUse[v.Code] = true
		}
	}
	s.SellerItemSelected = make(map[string]int)

	// Init ProductNameByCodeMap, SellerCodes, SellerGroups, GroupOfSellerMap, SellerOfGroupMap, Cart
	mapSellerCode := make(map[string]struct{})
	mapSellerGroup := make(map[string]struct{})
	cartItemsSelected := make([]model.CartItemInternal, 0)
	for _, item := range cart.CartItems {
		s.ProductNameByCodeMap[item.ProductCode] = item.ProductName
		if item.GroupCode != "" {
			if _, ok := mapSellerGroup[item.GroupCode]; !ok {
				s.SellerGroups = append(s.SellerGroups, item.GroupCode)
				mapSellerGroup[item.GroupCode] = struct{}{}
			}
			if item.StoreCode != "" {
				s.GroupOfSellerMap[item.StoreCode] = item.GroupCode
				s.SellerOfGroupMap[item.GroupCode] = item.StoreCode
			} else {
				s.GroupOfSellerMap[item.SellerCode] = item.GroupCode
				s.SellerOfGroupMap[item.GroupCode] = item.SellerCode
			}
			if item.IsSelected != nil && *item.IsSelected {
				s.SellerItemSelected[item.GroupCode] = 1
			}
		}
		if _, ok := mapSellerCode[item.SellerCode]; !ok {
			mapSellerCode[item.SellerCode] = struct{}{}
			s.SellerCodes = append(s.SellerCodes, item.SellerCode)
		}
		if item.IsSelected != nil && *item.IsSelected {
			cartItemsSelected = append(cartItemsSelected, item)
			// s.SellerItemSelected[item.SellerCode] = 1
		}
		if item.StoreCode != "" {
			// delete(s.SellerItemSelected, item.SellerCode)
			if _, ok := mapSellerCode[item.StoreCode]; !ok {
				mapSellerCode[item.StoreCode] = struct{}{}
				s.SellerCodes = append(s.SellerCodes, item.StoreCode)
			}
			if item.IsSelected != nil && *item.IsSelected {
				// s.SellerItemSelected[item.StoreCode] = 1
			}
		}
	}
	cart.CartItems = cartItemsSelected
	s.Cart = cart

	var (
		wg          sync.WaitGroup
		customerErr error
	)

	// Init Customer
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		customer, err := client.Services.Customer.GetCustomerByAccountID(cart.AccountID)
		customerErr = err
		if err == nil {
			s.Customer = customer
		}
	})

	// Init Setting
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		qSetting := model.SettingDB.QueryOne(bson.M{
			"system_display": enum.SystemDisplay.Buymed,
		})
		if qSetting.Status == common.APIStatus.Ok {
			s.Setting = qSetting.Data.([]*model.Setting)[0]
		}
	})

	wg.Wait()

	if customerErr != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy thông tin khách hàng",
		}
	}

	// Init UsedMap, ActiveVouchers, VoucherDefault
	var (
		activeVouchers    []*model.Voucher
		voucherDefault    *model.Voucher
		errVoucherDefault *errRes
	)
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		queryUsed := model.UserPromotion{
			CustomerID: s.Customer.CustomerID,
			ComplexQuery: []*bson.M{
				{
					"voucher_status": enum.VoucherStatus.ACTIVE,
				},
				{
					"$or": []bson.M{
						{
							"customer_apply_type": enum.CustomerApplyType.ALL,
						},
						{
							"$and": []bson.M{
								{
									"customer_apply_type": enum.CustomerApplyType.MANY,
								},
								{
									"status": bson.M{"$ne": enum.CodeStatus.DELETED},
								},
							},
						},
					},
				},
			},
		}
		usedRes := model.UserPromotionCacheDB.Query(queryUsed, 0, 0, nil)

		if usedRes.Status == common.APIStatus.Ok {
			if s.UsedMap == nil {
				s.UsedMap = make(map[string]*model.UserPromotion)
			}
			for _, v := range usedRes.Data.([]*model.UserPromotion) {
				s.UsedMap[v.VoucherCode] = v
			}
		}
	})

	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		qRes := model.VoucherCacheReadDB.Query(bson.M{
			"status":              enum.VoucherStatus.ACTIVE,
			"promotion_organizer": bson.M{"$ne": enum.PromotionOrganizer.MARKETING},
			"system_display":      enum.SystemDisplay.Buymed,
			"seller_codes":        bson.M{"$in": s.SellerCodes},
			"filter":              bson.M{"$all": []string{s.Customer.ProvinceCode, s.Customer.Level, s.Customer.Scope}},
			// "customer_apply_type": enum.CustomerApplyType.ALL,
		}, 0, 0, nil)
		if qRes.Status == common.APIStatus.Ok {
			activeVouchers = qRes.Data.([]*model.Voucher)
		}
	})

	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		voucherDefault, errVoucherDefault = s.GetVoucherDefault()
	})

	wg.Wait()

	vouchers := s.GetApplicableVouchers(s.UsedMap, activeVouchers)

	suggestDataRes := s.AnalyticSuggestion(voucherDefault, errVoucherDefault, vouchers)

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   suggestDataRes,
	}
}

func GetVoucherProductSuggestions(cart *model.Cart) *common.APIResponse {
	s := &suggestion{
		InUse:    make(map[string]bool),
		Customer: &model.Customer{},
		Cart:     &model.Cart{},
		Setting:  &model.Setting{},

		SellerOfGroupMap: make(map[string]string),
		GroupOfSellerMap: make(map[string]string),

		ProductNameByCodeMap: make(map[string]string),
		UsedMap:              make(map[string]*model.UserPromotion),
	}
	if cart != nil {
		for _, v := range cart.RedeemApplyResult {
			if v.CanUse {
				s.InUse[v.Code] = true
			}
		}
		s.SellerItemSelected = make(map[string]int)
		mapSellerCode := make(map[string]bool)
		mapSellerGroup := make(map[string]bool)

		cartItemsSelected := make([]model.CartItemInternal, 0)
		for _, item := range cart.CartItems {
			s.ProductNameByCodeMap[item.ProductCode] = item.ProductName
			if item.GroupCode != "" {
				if !mapSellerGroup[item.GroupCode] {
					s.SellerGroups = append(s.SellerGroups, item.GroupCode)
					mapSellerGroup[item.GroupCode] = true
				}
				if item.StoreCode != "" {
					s.GroupOfSellerMap[item.StoreCode] = item.GroupCode
					s.SellerOfGroupMap[item.GroupCode] = item.StoreCode
				} else {
					s.GroupOfSellerMap[item.SellerCode] = item.GroupCode
					s.SellerOfGroupMap[item.GroupCode] = item.SellerCode
				}
				if item.IsSelected != nil && *item.IsSelected {
					s.SellerItemSelected[item.GroupCode] = 1
				}
			}
			if _, ok := mapSellerCode[item.SellerCode]; !ok {
				mapSellerCode[item.SellerCode] = true
				s.SellerCodes = append(s.SellerCodes, item.SellerCode)
			}
			if item.IsSelected != nil && *item.IsSelected {
				cartItemsSelected = append(cartItemsSelected, item)
				// s.SellerItemSelected[item.SellerCode] = 1
			}
			if item.StoreCode != "" {
				// delete(s.SellerItemSelected, item.SellerCode)
				if _, ok := mapSellerCode[item.StoreCode]; !ok {
					mapSellerCode[item.StoreCode] = true
					s.SellerCodes = append(s.SellerCodes, item.StoreCode)
				}
				if item.IsSelected != nil && *item.IsSelected {
					// s.SellerItemSelected[item.StoreCode] = 1
				}
			}
		}
		cart.CartItems = cartItemsSelected
		s.Cart = cart
	}
	if customer, err := client.Services.Customer.GetCustomerByAccountID(cart.AccountID); err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không tìm thấy thông tin khách hàng",
		}
	} else {
		s.Customer = customer
	}
	if qSetting := model.SettingDB.QueryOne(bson.M{"system_display": enum.SystemDisplay.Buymed}); qSetting.Status == common.APIStatus.Ok {
		s.Setting = qSetting.Data.([]*model.Setting)[0]
	}
	vouchers := s.GetVoucherProducts()
	suggestDataRes := s.AnalyticProductSuggestion(vouchers)
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   suggestDataRes,
	}
}

type SuggestionI interface {
	GetVouchers() []*model.Voucher
	GetVoucherProducts() []*model.Voucher
	GetVoucherDefault() (*model.Voucher, *errRes)
	AnalyticSuggestion(voucherDefault *model.Voucher, errVoucherDefault *errRes, vouchers []*model.Voucher) []*suggestData
	AnalyticProductSuggestion(vouchers []*model.Voucher) []*suggestData
	GetApplicableVouchers(usedMap map[string]*model.UserPromotion, activeVouchers []*model.Voucher) []*model.Voucher
}

func NewSuggestion() SuggestionI {
	return &suggestion{
		InUse: make(map[string]bool),
	}
}

type suggestion struct {
	InUse              map[string]bool
	Customer           *model.Customer
	Cart               *model.Cart
	SellerCodes        []string
	SellerItemSelected map[string]int
	Setting            *model.Setting

	SellerGroups         []string
	GroupOfSellerMap     map[string]string
	SellerOfGroupMap     map[string]string
	ProductNameByCodeMap map[string]string

	UsedMap map[string]*model.UserPromotion
}

type suggestData struct {
	Label      string            `json:"label"`
	Param      map[string]string `json:"param"`
	SellerCode string            `json:"sellerCode"`
	StoreCode  string            `json:"storeCode"`
	InUse      bool              `json:"inUse"`
	Owner      string            `json:"owner"`
	SkuCode    string            `json:"skuCode"`
}

func (s suggestion) GetVoucherDefault() (*model.Voucher, *errRes) {
	//query := bson.M{
	//	"voucher_group_code": "BANK",
	//	"status":             enum.VoucherStatus.ACTIVE,
	//}
	//qRes := model.VoucherCacheDB.QueryOne(query)
	//if qRes.Status == common.APIStatus.Ok {
	//	voucher := qRes.Data.([]*model.Voucher)[0]
	//	errVoucherDefault := isValidVoucher(voucher.Code, voucher, nil, s.Cart, s.Customer, s.Setting)
	//	return voucher, &errVoucherDefault
	//}
	return nil, nil
}

var (
	LabelAvailable              = "Áp dụng ngay mã {{shortName}} - {{displayName}}"
	LabelSuggestAbsoluteNoError = "Mã giảm giá lên đến {{discount}}đ"
	LabelSuggestAbsolute        = "{{errorMessage}} để được giảm {{discount}}đ"
	LabelSuggestPercentNoError  = "Mã giảm giá {{percent}}% - giảm tối đa {{maxDiscount}}"
	LabelSuggestPercent         = "{{errorMessage}} để được giảm {{percent}}%"
	LabelSuggestGiftNoError     = "Mã quà tặng vô cùng hấp dẫn"
	LabelSuggestGift            = "{{errorMessage}} để nhận quà tặng"
	LabelInuse                  = "Đã áp dụng mã {{shortName}} - {{displayName}}"
)

func (s suggestion) AnalyticSuggestion(voucherDefault *model.Voucher, errVoucherDefault *errRes, vouchers []*model.Voucher) []*suggestData {
	res := make([]*suggestData, 0)
	// rule analytic:
	// 1. if seller haven't voucher, suggest default voucher
	// 1.a if default voucher haven't used, suggest use default voucher if valid
	// 1.b if default voucher haven't used, suggest buy more to get default voucher
	// 1.c if default voucher have used, show used message
	// 2. if seller have voucher, suggest voucher of seller
	// 2.1 voucher of seller haven't used, suggest voucher of seller by sort rule
	// 2.2 voucher of seller have used, suggest next voucher of seller by sort rule

	// sort rule:
	// 1. sort type of voucher: order first, product second and tag last
	// 2. sort by number of sku on each type of voucher

	voucherSeller := make(map[string][]*errRes)
	voucherMap := make(map[string]*model.Voucher)
	voucherTypeMap := make(map[string]enum.ConditionTypeValue)
	errData := make([]*errRes, 0)
	for _, v := range vouchers {
		checkVoucherRes := isValidVoucher(v.Code, v, s.UsedMap[v.Code], s.Cart, s.Customer, s.Setting, nil)
		if !checkVoucherRes.suggestInfo.isValidVoucherBase {
			continue
		}
		if checkVoucherRes.suggestInfo.suggestErrorCode == "" {
			checkVoucherRes.suggestInfo.suggestErrorCode = checkVoucherRes.ErrorCode
		}
		errData = append(errData, &checkVoucherRes)
		voucherMap[v.Code] = v
		voucherTypeMap[v.Code] = getTypeOfVoucher(v)
	}

	for _, v := range errData {
		if v.StoreCode != nil && *v.StoreCode != "" {
			if s.GroupOfSellerMap[*v.StoreCode] != "" {
				voucherSeller[s.GroupOfSellerMap[*v.StoreCode]] = append(voucherSeller[s.GroupOfSellerMap[*v.StoreCode]], v)
			}
		} else {
			if len(v.SellerCodes) > 0 {
				sellerCode := (v.SellerCodes)[0]
				if s.GroupOfSellerMap[sellerCode] != "" {
					voucherSeller[s.GroupOfSellerMap[sellerCode]] = append(voucherSeller[s.GroupOfSellerMap[sellerCode]], v)
				}
			}
		}
	}

	for _, group := range s.SellerGroups {
		seller := s.SellerOfGroupMap[group]
		if codes, ok := voucherSeller[group]; ok && len(codes) > 0 {
			sortByDiscount(codes, voucherMap)

			if s.SellerItemSelected[group] == 0 {
				err := codes[0]
				voucher := voucherMap[err.VoucherCode]
				sellerCode, storeCode, owner := group, "", seller

				if voucher.StoreCode != nil && *voucher.StoreCode != "" {
					storeCode = *voucher.StoreCode
					owner = storeCode
				}

				rewardType := voucher.Rewards[0].Type
				sgData := &suggestData{
					Owner:      owner,
					SellerCode: sellerCode,
					StoreCode:  storeCode,
					InUse:      false,
					Param: map[string]string{
						"voucherCode":  voucher.Code,
						"shortName":    voucher.ShortName,
						"displayName":  voucher.DisplayName,
						"errorMessage": err.ErrorMessage,
					},
				}

				if *rewardType == enum.RewardType.ABSOLUTE {
					sgData.Label = fmt.Sprintf("Mã giảm giá lên đến %s", toVNDText(int(voucher.Rewards[0].AbsoluteDiscount)))
					res = append(res, sgData)
				} else if *rewardType == enum.RewardType.PERCENTAGE {
					sgData.Param["percent"] = toPercentText(voucher.Rewards[0].PercentageDiscount)
					sgData.Param["maxDiscount"] = toVNDText(int(voucher.Rewards[0].MaxDiscount))
					sgData.Label = "Mã giảm giá {{percent}} - giảm tối đa {{maxDiscount}}"
					res = append(res, sgData)
				} else if *rewardType == enum.RewardType.GIFT {
					sgData.Label = "Mã quà tặng vô cùng hấp dẫn"
					res = append(res, sgData)
				}
			} else { // has selected item
				hasAppliedVoucher := false
				hasAppliedMultipleVoucher := false
				hasAppliedHighestVoucher := false
				appliedVoucherCount := 0
				var appliedVoucher *errRes
				for idx, err := range codes {
					if s.InUse[err.VoucherCode] {
						appliedVoucherCount++
						if hasAppliedVoucher {
							hasAppliedMultipleVoucher = true
						}
						hasAppliedVoucher = true
						appliedVoucher = err
						if idx == 0 {
							hasAppliedHighestVoucher = true
						}
					}
				}
				// // khách chưa apply voucher
				if !hasAppliedVoucher {
					nearestCodes := sortByTypeAndDistance(codes, voucherMap, s.InUse)
					canUseMap := getCanUseMap(nearestCodes, s.Setting, s.InUse, voucherMap, s.Cart)

					// hiện voucher dễ đạt được nhất
					var nearest *errRes
					for _, err := range nearestCodes {
						if !s.InUse[err.VoucherCode] && canUseMap[err.VoucherCode] {
							nearest = err
							break
						}
					}

					if nearest != nil {
						nearestVoucher := voucherMap[nearest.VoucherCode]
						sellerCode, storeCode, owner := group, "", seller

						if nearestVoucher.StoreCode != nil && *nearestVoucher.StoreCode != "" {
							storeCode = *nearestVoucher.StoreCode
							owner = storeCode
						}

						sgData := &suggestData{
							InUse:      false,
							SellerCode: sellerCode,
							StoreCode:  storeCode,
							Owner:      owner,
							Param:      map[string]string{},
						}
						sgData.Label = getLabelSuggest(nearestVoucher, nearest, s.ProductNameByCodeMap)
						if sgData.Label != "" {
							sgData.Param["voucherCode"] = nearestVoucher.Code
							sgData.Param["shortName"] = nearestVoucher.Code
							sgData.Param["displayName"] = nearestVoucher.Code
							sgData.Param["errorMessage"] = nearest.ErrorMessage

							res = append(res, sgData)
						}
					}
				} else if hasAppliedVoucher && !hasAppliedMultipleVoucher && !hasAppliedHighestVoucher {
					applied := voucherMap[appliedVoucher.VoucherCode]
					sellerCode, storeCode, owner := group, "", seller

					sgData := &suggestData{
						Label:      fmt.Sprintf("Đã áp dụng mã giảm giá %s", applied.ShortName),
						InUse:      true,
						SellerCode: sellerCode,
						StoreCode:  storeCode,
						Owner:      owner,
						Param: map[string]string{
							"voucherCode": applied.Code,
							"shortName":   applied.ShortName,
							"displayName": applied.DisplayName,
							// "errorMessage": nearest.ErrorMessage,
						},
					}

					nearestCodes := sortByTypeAndDistance(codes, voucherMap, s.InUse)
					canUseMap := getCanUseMap(nearestCodes, s.Setting, s.InUse, voucherMap, s.Cart)
					// hiện voucher dễ đạt được nhất
					var nearest *errRes
					for _, err := range nearestCodes {
						if !s.InUse[err.VoucherCode] && canUseMap[err.VoucherCode] {
							nearest = err
							break
						}
					}
					if nearest != nil {
						labelSuggest := getLabelSuggest(voucherMap[nearest.VoucherCode], nearest, s.ProductNameByCodeMap)
						if labelSuggest != "" {
							nearestVoucher := voucherMap[nearest.VoucherCode]
							if nearestVoucher.StoreCode != nil && *nearestVoucher.StoreCode != "" {
								sgData.StoreCode = *nearestVoucher.StoreCode
								sgData.Owner = *nearestVoucher.StoreCode
							}
							sgData.Label += ". " + labelSuggest
							sgData.InUse = false
							sgData.Param["voucherCode"] = nearestVoucher.Code
							sgData.Param["shortName"] = nearestVoucher.ShortName
							sgData.Param["displayName"] = nearestVoucher.DisplayName
							sgData.Param["errorMessage"] = nearest.ErrorMessage
						}
					}
					res = append(res, sgData)

				} else if hasAppliedVoucher && !hasAppliedMultipleVoucher && hasAppliedHighestVoucher {
					highest := codes[0]
					highestVoucher := voucherMap[highest.VoucherCode]
					sellerCode, storeCode, owner := group, "", seller

					if highestVoucher.StoreCode != nil && *highestVoucher.StoreCode != "" {
						storeCode = *highestVoucher.StoreCode
						owner = storeCode
					}
					res = append(res, &suggestData{
						InUse:      true,
						Label:      "Đã áp dụng mã giảm giá {{shortName}}",
						SellerCode: sellerCode,
						StoreCode:  storeCode,
						Owner:      owner,
						Param: map[string]string{
							"voucherCode":  highestVoucher.Code,
							"shortName":    highestVoucher.ShortName,
							"displayName":  highestVoucher.DisplayName,
							"errorMessage": highest.ErrorMessage,
						},
					})

				} else if hasAppliedMultipleVoucher && !hasAppliedHighestVoucher {
					label := fmt.Sprintf("Đã áp dụng %d mã giảm giá", appliedVoucherCount)
					highest := codes[0]
					highestVoucher := voucherMap[highest.VoucherCode]
					labelSuggest := getLabelSuggest(highestVoucher, highest, s.ProductNameByCodeMap)
					if labelSuggest != "" {
						label += ". " + labelSuggest
					}

					sellerCode, storeCode, owner := group, "", seller

					if highestVoucher.StoreCode != nil && *highestVoucher.StoreCode != "" {
						storeCode = *highestVoucher.StoreCode
						owner = storeCode
					}
					sgData := &suggestData{
						Label:      label,
						InUse:      false,
						SellerCode: sellerCode,
						StoreCode:  storeCode,
						Owner:      owner,
						Param: map[string]string{
							"voucherCode":  highestVoucher.Code,
							"shortName":    highestVoucher.ShortName,
							"displayName":  highestVoucher.DisplayName,
							"errorMessage": highest.ErrorMessage,
						},
					}

					res = append(res, sgData)
				} else if hasAppliedMultipleVoucher && hasAppliedHighestVoucher {
					highest := codes[0]
					highestVoucher := voucherMap[highest.VoucherCode]
					sellerCode, storeCode, owner := group, "", seller

					if highestVoucher.StoreCode != nil && *highestVoucher.StoreCode != "" {
						storeCode = *highestVoucher.StoreCode
						owner = storeCode
					}
					res = append(res, &suggestData{
						Label:      fmt.Sprintf("Đã áp dụng %d mã giảm giá", appliedVoucherCount),
						InUse:      true,
						SellerCode: sellerCode,
						Owner:      owner,
						StoreCode:  storeCode,
						Param: map[string]string{
							"voucherCode":  highestVoucher.Code,
							"shortName":    highestVoucher.ShortName,
							"displayName":  highestVoucher.DisplayName,
							"errorMessage": highest.ErrorMessage,
						},
					})
				}
			}

		} else if voucherDefault != nil && errVoucherDefault != nil {
			sellerCode, storeCode, owner := group, "", ""

			if voucherDefault.StoreCode != nil && *voucherDefault.StoreCode != "" {
				storeCode = *voucherDefault.StoreCode
				owner = ""
			}
			if _, ok := s.InUse[voucherDefault.Code]; !ok {
				label := ""
				errorMessage := ""
				hasSelectedItem := s.SellerItemSelected[group] > 0
				paymentMethodName := ""
				if ok, _, paymentMethodNames := isExistConditionPaymentMethod(voucherDefault); ok && len(paymentMethodNames) > 0 {
					paymentMethodName = paymentMethodNames[0]
				}
				if hasSelectedItem && errVoucherDefault.suggestInfo.suggestErrorCode == enum.VoucherErrorCode.NOT_ENOUGH_ORDER_VALUE {
					errorMessage = errVoucherDefault.ErrorMessage
					label = fmt.Sprintf("Mua giá trị đơn thêm %s để áp dụng mã giảm giá %s - %s", toVNDText(errVoucherDefault.suggestInfo.suggestAmount), toPercentText(voucherDefault.Rewards[0].PercentageDiscount), paymentMethodName)
				} else if hasSelectedItem && errVoucherDefault.suggestInfo.suggestErrorCode == enum.VoucherErrorCode.NOT_ENOUGH_SKU_TYPES {
					errorMessage = errVoucherDefault.ErrorMessage
					label = fmt.Sprintf("Mua thêm %d sản phẩm để áp dụng mã giảm giá %s - %s", errVoucherDefault.suggestInfo.suggestQuantity, toPercentText(voucherDefault.Rewards[0].PercentageDiscount), paymentMethodName)
				} else {
					label = fmt.Sprintf("Chọn mã %s để được giảm giá ngay", voucherDefault.ShortName)
				}
				res = append(res, &suggestData{
					Label:      label,
					InUse:      false,
					SellerCode: sellerCode,
					StoreCode:  storeCode,
					Owner:      owner,
					Param: map[string]string{
						"voucherCode":  voucherDefault.Code,
						"shortName":    voucherDefault.ShortName,
						"displayName":  voucherDefault.DisplayName,
						"errorMessage": errorMessage,
					},
				})
			} else {
				res = append(res, &suggestData{
					Label:      fmt.Sprintf("Đã áp dụng mã giảm giá %s - %s", voucherDefault.ShortName, voucherDefault.DisplayName),
					InUse:      true,
					SellerCode: sellerCode,
					StoreCode:  storeCode,
					Owner:      owner,
					Param: map[string]string{
						"voucherCode":  voucherDefault.Code,
						"shortName":    voucherDefault.ShortName,
						"displayName":  voucherDefault.DisplayName,
						"errorMessage": voucherDefault.ErrorMessage,
					},
				})
			}
		}
	}

	return res
}

func (s suggestion) AnalyticProductSuggestion(vouchers []*model.Voucher) []*suggestData {
	res := make([]*suggestData, 0)
	voucherMap := make(map[string]*model.Voucher)
	voucherTypeMap := make(map[string]enum.ConditionTypeValue)
	errData := make([]*errRes, 0)
	mapSkuVouchers := make(map[string][]*model.VoucherViewWebOnly)
	for _, v := range vouchers {
		checkVoucherRes := isValidVoucher(v.Code, v, s.UsedMap[v.Code], s.Cart, s.Customer, s.Setting, nil)
		// convert gift to discount
		if checkVoucherRes.Discount == 0 && len(checkVoucherRes.Gifts) > 0 {
			giftsDiscount := 0
			for _, reward := range checkVoucherRes.Gifts {
				giftsDiscount += int(reward.GiftValue)
			}
			v.Discount = giftsDiscount
			checkVoucherRes.Discount = giftsDiscount
		}

		if !checkVoucherRes.suggestInfo.isValidVoucherBase {
			continue
		}
		if checkVoucherRes.CanUse || checkVoucherRes.ErrorMessage == "" {
			continue
		}
		if checkVoucherRes.suggestInfo.suggestErrorCode == "" {
			checkVoucherRes.suggestInfo.suggestErrorCode = checkVoucherRes.ErrorCode
		}
		errData = append(errData, &checkVoucherRes)
		voucherMap[v.Code] = v
		voucherTypeMap[v.Code] = getTypeOfVoucher(v)
		viewVoucher := setViewData(v, nil)
		viewVoucher.ErrorMessage = checkVoucherRes.ErrorMessage
		viewVoucher.MapSkuMessage = checkVoucherRes.mapSkuMessage
		for sku, _ := range viewVoucher.OrSkus {
			if _, ok := mapSkuVouchers[sku]; !ok {
				mapSkuVouchers[sku] = make([]*model.VoucherViewWebOnly, 0)
			}
			mapSkuVouchers[sku] = append(mapSkuVouchers[sku], viewVoucher)
		}
		for sku, _ := range viewVoucher.AndSkus {
			if _, ok := mapSkuVouchers[sku]; !ok {
				mapSkuVouchers[sku] = make([]*model.VoucherViewWebOnly, 0)
			}
			mapSkuVouchers[sku] = append(mapSkuVouchers[sku], viewVoucher)
		}
	}
	getQuantityAndMinPrice := func(v *model.VoucherViewWebOnly, sku string) (int, int, int) {
		for k, v1 := range v.OrSkus {
			if k == sku {
				return v1.Quantity, v1.MinPrice, v.Discount
			}
		}
		for k, v1 := range v.AndSkus {
			if k == sku {
				return v1.Quantity, v1.MinPrice, v.Discount
			}
		}
		return 0, 0, 0
	}
	for sku, vouchers := range mapSkuVouchers {
		var voucherSelected *model.VoucherViewWebOnly
		for _, v := range vouchers {
			if voucherSelected == nil {
				voucherSelected = v
			} else {
				// compare near quantity, near min price, bigger discount
				quantity, minPrice, discount := getQuantityAndMinPrice(v, sku)
				quantitySelected, minPriceSelected, discountSelected := getQuantityAndMinPrice(voucherSelected, sku)
				if quantity < quantitySelected {
					voucherSelected = v
					continue
				}
				if quantity == quantitySelected && minPrice < minPriceSelected {
					voucherSelected = v
					continue
				}
				if quantity == quantitySelected && minPrice == minPriceSelected && discount > discountSelected {
					voucherSelected = v
					continue
				}
			}
		}
		if voucherSelected != nil {
			message := voucherSelected.ErrorMessage
			if voucherSelected.MapSkuMessage != nil {
				key := strings.Replace(sku, ".", "_", -1)
				if m, ok := voucherSelected.MapSkuMessage[key]; ok {
					m = strings.ToLower(string(m[0])) + m[1:]
					if voucherSelected.DiscountPercent > 0 {
						m = fmt.Sprintf("Giảm %s%% khi %s", fmt.Sprint(voucherSelected.DiscountPercent), m)
					} else if voucherSelected.Discount > 0 && len(voucherSelected.Gifts) == 0 {
						// because gift was discount, so we need to check if gift is empty to show message
						m = fmt.Sprintf("Giảm ngay %sđ khi %s", utils.FormatVNDCurrency(fmt.Sprint(voucherSelected.Discount)), m)
					}
					if len(voucherSelected.Gifts) > 0 {
						m = fmt.Sprintf("Nhận ngay quà tặng khi %s", m)
					}
					message = m
				}
			}
			sgData := &suggestData{
				InUse:   false,
				SkuCode: sku,
				Label:   message,
				Param: map[string]string{
					"voucherCode":  voucherSelected.Code,
					"shortName":    voucherSelected.ShortName,
					"displayName":  voucherSelected.DisplayName,
					"errorMessage": message,
				},
			}
			res = append(res, sgData)
		}
	}
	return res
}

func (s *suggestion) GetApplicableVouchers(usedMap map[string]*model.UserPromotion, activeVouchers []*model.Voucher) []*model.Voucher {

	var (
		vouchers  []*model.Voucher // list voucher active
		skuInCart = make(map[string]bool)
	)
	if s.Cart != nil {
		for _, item := range s.Cart.CartItems {
			skuInCart[item.ProductSKU] = true
		}
	}
	for _, v := range activeVouchers {
		if use, ok := usedMap[v.Code]; ok {
			if v.MaxUsagePerCustomer != nil && *v.MaxUsagePerCustomer != 0 && use.Amount != nil && *use.Amount >= *v.MaxUsagePerCustomer {
				continue
			}
		} else if v.CustomerApplyType == enum.CustomerApplyType.MANY {
			continue
		}
		isMatchSku := false
		voucherView := setViewData(v, nil)
		if voucherView.OrSkus != nil && len(voucherView.OrSkus) > 0 {
			for key, _ := range voucherView.OrSkus {
				if skuInCart[key] {
					isMatchSku = true
					break
				}
			}
		}
		if !isMatchSku && voucherView.AndSkus != nil && len(voucherView.AndSkus) == 1 {
			for key, _ := range voucherView.AndSkus {
				if skuInCart[key] {
					isMatchSku = true
					break
				}
			}
		}
		if !isMatchSku {
			vouchers = append(vouchers, v)
		}

	}

	return vouchers
}

func (s *suggestion) GetVouchers() []*model.Voucher {
	var (
		vouchers  []*model.Voucher // list voucher active
		skuInCart = make(map[string]bool)
	)
	if s.Cart != nil {
		for _, item := range s.Cart.CartItems {
			skuInCart[item.ProductSKU] = true
		}
	}
	queryUsed := model.UserPromotion{
		CustomerID: s.Customer.CustomerID,
		ComplexQuery: []*bson.M{
			{
				"voucher_status": enum.VoucherStatus.ACTIVE,
			},
			{
				"$or": []bson.M{
					{
						"customer_apply_type": enum.CustomerApplyType.ALL,
					},
					{
						"$and": []bson.M{
							{
								"customer_apply_type": enum.CustomerApplyType.MANY,
							},
							{
								"status": bson.M{"$ne": enum.CodeStatus.DELETED},
							},
						},
					},
				},
			},
		},
	}
	usedRes := model.UserPromotionCacheDB.Query(queryUsed, 0, 0, nil)
	if usedRes.Status == common.APIStatus.Ok {
		if s.UsedMap == nil {
			s.UsedMap = make(map[string]*model.UserPromotion)
		}
		for _, v := range usedRes.Data.([]*model.UserPromotion) {
			s.UsedMap[v.VoucherCode] = v
		}
	}
	query := bson.M{
		"status":              enum.VoucherStatus.ACTIVE,
		"promotion_organizer": bson.M{"$ne": enum.PromotionOrganizer.MARKETING},
		"system_display":      enum.SystemDisplay.Buymed,
		"seller_codes":        bson.M{"$in": s.SellerCodes},
		"filter":              bson.M{"$all": []string{s.Customer.ProvinceCode, s.Customer.Level, s.Customer.Scope}},
		// "customer_apply_type": enum.CustomerApplyType.ALL,
	}
	qRes := model.VoucherCacheReadDB.Query(query, 0, 0, nil)
	if qRes.Status == common.APIStatus.Ok {
		for _, v := range qRes.Data.([]*model.Voucher) {
			if use, ok := s.UsedMap[v.Code]; ok {
				if v.MaxUsagePerCustomer != nil && *v.MaxUsagePerCustomer != 0 && use.Amount != nil && *use.Amount >= *v.MaxUsagePerCustomer {
					continue
				}
			} else if v.CustomerApplyType == enum.CustomerApplyType.MANY {
				continue
			}
			isMatchSku := false
			voucherView := setViewData(v, nil)
			if voucherView.OrSkus != nil && len(voucherView.OrSkus) > 0 {
				for key, _ := range voucherView.OrSkus {
					if skuInCart[key] {
						isMatchSku = true
						break
					}
				}
			}
			if !isMatchSku && voucherView.AndSkus != nil && len(voucherView.AndSkus) == 1 {
				for key, _ := range voucherView.AndSkus {
					if skuInCart[key] {
						isMatchSku = true
						break
					}
				}
			}
			if !isMatchSku {
				vouchers = append(vouchers, v)
			}
		}
	}
	return vouchers
}

func (s suggestion) GetVoucherProducts() []*model.Voucher {
	var (
		vouchers  []*model.Voucher // list voucher active
		skuInCart = make(map[string]bool)
		skus      = make([]string, 0)
	)
	if s.Cart != nil {
		for _, item := range s.Cart.CartItems {
			skuInCart[item.ProductSKU] = true
			skus = append(skus, item.ProductSKU)
		}
	}
	queryUsed := model.UserPromotion{
		CustomerID: s.Customer.CustomerID,
		ComplexQuery: []*bson.M{
			{
				"voucher_status": enum.VoucherStatus.ACTIVE,
			},
			{
				"$or": []bson.M{
					{
						"customer_apply_type": enum.CustomerApplyType.ALL,
					},
					{
						"$and": []bson.M{
							{
								"customer_apply_type": enum.CustomerApplyType.MANY,
							},
							{
								"status": bson.M{"$ne": enum.CodeStatus.DELETED},
							},
						},
					},
				},
			},
		},
	}
	usedRes := model.UserPromotionCacheDB.Query(queryUsed, 0, 0, nil)
	if usedRes.Status == common.APIStatus.Ok {
		if s.UsedMap == nil {
			s.UsedMap = make(map[string]*model.UserPromotion)
		}
		for _, v := range usedRes.Data.([]*model.UserPromotion) {
			s.UsedMap[v.VoucherCode] = v
		}
	}
	query := bson.M{
		"status":         enum.VoucherStatus.ACTIVE,
		"system_display": enum.SystemDisplay.Buymed,
		"ref_product":    bson.M{"$in": skus},
		"filter":         bson.M{"$all": []string{s.Customer.ProvinceCode, s.Customer.Level, s.Customer.Scope}},
	}
	qRes := model.VoucherCacheReadDB.Query(query, 0, 0, nil)
	if qRes.Status == common.APIStatus.Ok {
		for _, v := range qRes.Data.([]*model.Voucher) {
			if use, ok := s.UsedMap[v.Code]; ok {
				if v.MaxUsagePerCustomer != nil && *v.MaxUsagePerCustomer != 0 && use.Amount != nil && *use.Amount >= *v.MaxUsagePerCustomer {
					continue
				}
			} else if v.CustomerApplyType == enum.CustomerApplyType.MANY {
				continue
			}

			// mapping voucher product name to condition
			productCodes, _, _, _, _, _ := getProductInfoByCondition(v.OrConditions, v.AndConditions, v.ApplyDiscount, v.SellerCodes, v.StoreCode)
			productMap := make(map[string]*model.Product)
			if len(productCodes) > 0 {
				products, _ := client.Services.Product.GetProducts(productCodes)
				for _, product := range products {
					productMap[product.Code] = product
				}
				// set product name in conditions.
				setDataNameToCondition(v.OrConditions, v.AndConditions, productMap, nil)
			}

			isMatchSku := false
			voucherView := setViewData(v, nil)
			if voucherView.OrSkus != nil && len(voucherView.OrSkus) > 0 {
				for key, _ := range voucherView.OrSkus {
					if skuInCart[key] {
						isMatchSku = true
						break
					}
				}
			}
			if !isMatchSku && voucherView.AndSkus != nil && len(voucherView.AndSkus) == 1 {
				for key, _ := range voucherView.AndSkus {
					if skuInCart[key] {
						isMatchSku = true
						break
					}
				}
			}
			if isMatchSku {
				vouchers = append(vouchers, v)
			}
		}
	}

	return vouchers
}

func getTypeOfVoucher(voucher *model.Voucher) enum.ConditionTypeValue {
	if len(voucher.AndConditions) > 0 {
		if _, ok := voucher.AndConditions[enum.ConditionType.PRODUCT_TAG]; ok {
			return enum.ConditionType.PRODUCT_TAG
		}
		if _, ok := voucher.AndConditions[enum.ConditionType.PRODUCT]; ok {
			return enum.ConditionType.PRODUCT
		}
		if _, ok := voucher.AndConditions[enum.ConditionType.ORDER_VALUE]; ok {
			return enum.ConditionType.ORDER_VALUE
		}
		if _, ok := voucher.OrConditions[enum.ConditionType.PRODUCT_TAG]; ok {
			return enum.ConditionType.PRODUCT_TAG
		}
		if _, ok := voucher.OrConditions[enum.ConditionType.PRODUCT]; ok {
			return enum.ConditionType.PRODUCT
		}
		if _, ok := voucher.OrConditions[enum.ConditionType.ORDER_VALUE]; ok {
			return enum.ConditionType.ORDER_VALUE
		}
	}
	return ""
}

func toVNDText(value int) string {
	return utils.FormatVNDCurrency(fmt.Sprintf("%d", value)) + "đ"
}
func toPercentText(value float64) string {
	var textDiscountPercent string
	if value == float64(int(value)) {
		// Nếu giá trị là số nguyên, không cần phần thập phân
		textDiscountPercent = fmt.Sprintf("%.0f%%", value)
	} else {
		// Nếu không, giữ lại một hoặc hai chữ số thập phân
		textDiscountPercent = fmt.Sprintf("%.2f%%", value)
		textDiscountPercent = strings.Replace(textDiscountPercent, ".00", "", -1)
	}
	return textDiscountPercent
}
func getLabelSuggest(voucher *model.Voucher, res *errRes, productNameMap map[string]string) string {
	reward := voucher.Rewards[0]
	rewardType := *reward.Type
	if res.suggestInfo.suggestErrorCode == "" {
		switch rewardType {
		case enum.RewardType.ABSOLUTE:
			return "Chọn mã {{shortName}} để được giảm giá ngay"
		case enum.RewardType.PERCENTAGE:
			return "Chọn mã {{shortName}} để được giảm giá ngay"
		case enum.RewardType.GIFT:
			return "Chọn mã {{shortName}} để được tặng quà"
		}
	}
	suggestText := res.suggestInfo.suggestTemplate
	switch rewardType {
	case enum.RewardType.ABSOLUTE:
		suggestText = strings.Replace(suggestText, "{reward}", "giảm "+toVNDText(int(reward.AbsoluteDiscount)), -1)
	case enum.RewardType.PERCENTAGE:
		suggestText = strings.Replace(suggestText, "{reward}", "giảm "+toPercentText(reward.PercentageDiscount), -1)
	case enum.RewardType.GIFT:
		suggestText = strings.Replace(suggestText, "{reward}", "tặng "+getGiftsText(reward.Gifts), -1)
	}

	if res.suggestInfo.suggestProductCode != "" {
		if productName := productNameMap[res.suggestInfo.suggestProductCode]; productName != "" {
			suggestText = strings.Replace(suggestText, "{productName}", productName, -1)
		} else {
			suggestText = ""
		}
	}
	return suggestText
}

func getGiftDiscount(gifts []model.Gift) int {
	total := 0
	for _, gift := range gifts {
		total += int(gift.GiftValue)
	}
	return total
}
func getDiscount(voucher *model.Voucher) int {
	if voucher != nil && len(voucher.Rewards) > 0 {
		if voucher.Rewards[0].Type != nil {
			if *voucher.Rewards[0].Type == enum.RewardType.PERCENTAGE {
				return int(voucher.Rewards[0].MaxDiscount)
			} else if *voucher.Rewards[0].Type == enum.RewardType.ABSOLUTE {
				return int(voucher.Rewards[0].AbsoluteDiscount)
			} else if *voucher.Rewards[0].Type == enum.RewardType.GIFT {
				return getGiftDiscount(voucher.Rewards[0].Gifts)
			}
		}
	}
	return 0
}
func getGiftsText(gifts []model.Gift) string {
	text := fmt.Sprintf("%d quà tặng", len(gifts))
	if len(gifts) > 0 {
		quantityMap := make(map[string]int, len(gifts))
		productCodes := make([]string, 0, len(gifts))
		for _, gift := range gifts {
			if gift.Sku != "" {
				parts := strings.Split(gift.Sku, ".")
				// Kiểm tra xem có ít nhất 2 phần không
				if len(parts) > 1 {
					// Lấy phần thứ 2
					productCode := parts[1]
					productCodes = append(productCodes, productCode)
					quantityMap[productCode] = int(gift.Quantity)
				}
			}
		}
		if len(productCodes) > 0 {
			productMap := make(map[string]*model.Product)
			remainCodes := make([]string, 0, len(productCodes))
			for _, code := range productCodes {
				if val, ok := productGiftCache.Get(code); ok {
					productMap[code] = val.(*model.Product)
				} else {
					remainCodes = append(remainCodes, code)
				}
			}

			if len(remainCodes) > 0 {
				products, err := client.Services.Product.GetProducts(remainCodes)
				if err != nil {
					return text
				}
				for _, product := range products {
					productMap[product.Code] = product
					productGiftCache.Put(product.Code, product)
				}
			}

			if len(productMap) == 0 {
				return text
			}

			textParts := make([]string, 0, len(productCodes))
			for _, productCode := range productCodes {
				if product, ok := productMap[productCode]; ok {
					textParts = append(textParts, fmt.Sprintf("%d %s", quantityMap[productCode], product.Name))
				}
			}
			if len(textParts) > 0 {
				text = strings.Join(textParts, ", ")
			}
		}
	}
	return text
}

func sortByTypeAndDistance(codes []*errRes, voucherMap map[string]*model.Voucher, inUseMap map[string]bool) []*errRes {
	rs := make([]*errRes, 0, len(codes))
	sortByDiscount(codes, voucherMap)
	for _, code := range codes {
		if !inUseMap[code.VoucherCode] {
			rs = append(rs, code)
		} else {
			break
		}
	}
	sort.Slice(rs, func(i, j int) bool {
		typeI := getTypeOfVoucher(voucherMap[rs[i].VoucherCode])
		typeJ := getTypeOfVoucher(voucherMap[rs[j].VoucherCode])
		if typeI != typeJ {
			if typeI == enum.ConditionType.ORDER_VALUE {
				return true
			}
			if typeJ == enum.ConditionType.ORDER_VALUE {
				return false
			}
			if typeI == enum.ConditionType.PRODUCT {
				return true
			}
			if typeJ == enum.ConditionType.PRODUCT {
				return false
			}
			if typeI == enum.ConditionType.PRODUCT_TAG {
				return true
			}
			if typeJ == enum.ConditionType.PRODUCT_TAG {
				return false
			}
		}

		return rs[i].suggestInfo.suggestDistance > rs[j].suggestInfo.suggestDistance
	})
	return rs
}

func sortByDiscount(codes []*errRes, voucherMap map[string]*model.Voucher) {
	sort.Slice(codes, func(i, j int) bool {
		itemI := codes[i]
		itemJ := codes[j]
		discountI := getDiscount(voucherMap[itemI.VoucherCode])
		discountJ := getDiscount(voucherMap[itemJ.VoucherCode])
		return discountI > discountJ
	})
}

func getCanUseMap(data []*errRes, setting *model.Setting, inUseMap map[string]bool, voucherMap map[string]*model.Voucher, cart *model.Cart) map[string]bool {
	canUseMap := make(map[string]bool)
	// checkVoucherGroupMapInUse(data, setting, nil, voucherMap, cart, false, true, true)
	nonUses := make([]*model.VoucherViewWebOnly, 0)
	for _, v := range data {
		viewData := setViewData(voucherMap[v.VoucherCode], cart)
		viewData.CanUse = true
		nonUses = append(nonUses, viewData)
	}

	inUses := make([]*model.VoucherViewWebOnly, 0)
	for _, v := range data {
		if inUseMap[v.VoucherCode] {
			inUses = append(inUses, setViewData(voucherMap[v.VoucherCode], cart))
		}
	}
	_, nonUsesNew := checkVoucherGroupMap(inUses, nonUses, cart, setting)
	for _, v := range nonUsesNew {
		canUseMap[v.Code] = v.CanUse
	}
	return canUseMap
}
