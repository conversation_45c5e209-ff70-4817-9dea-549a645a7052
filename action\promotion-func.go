package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
)

type result struct {
	cart           *model.Cart                                     // cart info
	cartItemMap    map[string]model.CartItemInternal               // data map cart item
	orderSummation *model.SummationOrderInfo                       // order info
	promoCondition map[enum.ConditionTypeValue]model.PromotionType // condition
	customer       *model.Customer                                 // customer info
	message        string                                          // message lỗi
	mapSkuMessage  map[string]string                               // message lỗi theo sku
	errorCode      enum.VoucherErrorCodeType                       // error code
	isValid        bool                                            // voucher is valid ?
	operator       string                                          // AND || OR
	rewardCount    int                                             // số phần thưởng được cộng dồn
	applyDiscount  *model.ApplyDiscountOptions
	organization   enum.PromotionOrganizerValue // tổ chức tổ chức chương trình
	sellerCodes    []string                     // danh sách mã seller
	storeCode      *string                      // mã cửa hàng
	sellerName     string                       // tên seller
	point          int                          // điểm valid

	suggestInfo        suggestInfo
	hideDisplay        bool
	customerExperience *model.CustomerExperience
}

type suggestInfo struct {
	isValidVoucherBase   bool
	isValidVoucherScope  bool
	isValidNumberVoucher bool

	suggestAmount      int
	suggestQuantity    int
	suggestProductCode string
	suggestDistance    float64
	suggestDiscount    int
	suggestErrorCode   enum.VoucherErrorCodeType
	suggestTemplate    string
}

type condition interface {
	execute(*result)
	validate(*result, []model.PromotionCondition, string)
	setNext(condition)
	getRewardCount(*result, []model.PromotionCondition, string)
}

// order
type orderCondition struct {
	next condition
}

func (o *orderCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if o.next != nil && r.isValid {
				r.message = ""
				o.next.execute(r)
			}
			if o.next != nil && r.message == "" {
				o.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if o.next != nil {
				o.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.ORDER_VALUE]; ok {
		if r.cart.SkuValue == nil {
			makeMapItem(r.cart, r.cart.CartItems)
		}
		if len(data.AndConditions) > 0 {
			o.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
			if r.isValid {
				o.getRewardCount(r, data.AndConditions, enum.Operator.AND)
			}
		}
		if len(data.OrConditions) > 0 {
			o.validate(r, data.OrConditions, enum.Operator.OR)
			if r.isValid {
				o.getRewardCount(r, data.OrConditions, enum.Operator.OR)
				return
			}
		}
	}
}

func (o *orderCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {
	for _, c := range conditions {
		r.isValid = true
		cartPrice := r.cart.Price
		totalItem := r.cart.TotalItem
		skusStr := ""
		// new & repeat sku conditions
		lastMonthSKUs := make([]string, 0)
		cartSKUs := make([]string, 0)
		newSKUs := 0
		repeatSKUs := 0

		if c.OrderConditionField.NewSkuQuantity != nil || c.OrderConditionField.RepeatSkuQuantity != nil {
			for sku := range r.cart.SkuValue {
				cartSKUs = append(cartSKUs, sku)
			}
		}

		if r.organization == enum.PromotionOrganizer.SELLER_CENTER || r.organization == enum.PromotionOrganizer.INTERNAL_SELLER {
			cartPrice = 0
			totalItem = 0
			cartSKUs = make([]string, 0)
			if !isNilOrDefaultValue(r.storeCode) {
				if r.cart.StoreValue == nil {
					makeMapItem(r.cart, r.cart.CartItems)
				}
				if d, ok := r.cart.StoreValue[*r.storeCode]; ok {
					cartPrice = d.Total
					totalItem = d.TotalItem
					cartSKUs = d.SKUs
				}
			} else {
				if r.cart.SellerValue == nil {
					makeMapItem(r.cart, r.cart.CartItems)
				}
				for _, seller := range r.sellerCodes {
					if _, ok := r.cart.SellerValue[seller]; ok {
						cartPrice += r.cart.SellerValue[seller].Total
						totalItem += r.cart.SellerValue[seller].TotalItem
						cartSKUs = append(cartSKUs, r.cart.SellerValue[seller].SKUs...)
					}
				}
			}

		} else {
			if r.applyDiscount != nil && r.applyDiscount.NotInSkus != nil {
				for _, sku := range *r.applyDiscount.NotInSkus {
					if _, ok := r.cart.SkuValue[sku]; ok {
						cartPrice -= r.cart.SkuValue[sku].Total
						if skuName := r.applyDiscount.SkuName[sku]; skuName != "" {
							if skusStr == "" {
								skusStr += skuName
							} else {
								skusStr += ", " + skuName
							}
						}
					}
				}
			}
			if r.applyDiscount != nil && r.applyDiscount.Skus != nil && len(*r.applyDiscount.Skus) > 0 {
				cartPrice = 0
				totalItem = 0
				for _, sku := range *r.applyDiscount.Skus {
					if _, ok := r.cart.SkuValue[sku]; ok {
						cartPrice += r.cart.SkuValue[sku].Total
						totalItem += 1
						cartSKUs = append(cartSKUs, sku)
					}
				}
			}
		}
		if c.OrderConditionField.MinTotalPrice != nil && *c.OrderConditionField.MinTotalPrice > int(cartPrice) {
			r.message = fmt.Sprintf("Mua thêm %sđ để sử dụng mã", utils.FormatVNDCurrency(fmt.Sprint(*c.OrderConditionField.MinTotalPrice-int(cartPrice))))
			r.suggestInfo.suggestAmount = *c.OrderConditionField.MinTotalPrice - int(cartPrice)
			r.suggestInfo.suggestDistance += float64(cartPrice) / float64(*c.OrderConditionField.MinTotalPrice) * 100
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_ORDER_VALUE
			r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %sđ từ nhà bán hàng để được {reward}", utils.FormatVNDCurrency(fmt.Sprint(*c.OrderConditionField.MinTotalPrice-int(cartPrice))))
			if r.sellerName != "" {
				r.message = fmt.Sprintf("Mua thêm %sđ từ %s", utils.FormatVNDCurrency(fmt.Sprint(*c.OrderConditionField.MinTotalPrice-int(cartPrice))), r.sellerName)
			}
			if skusStr != "" {
				r.message += ", không áp dụng cho sản phẩm " + skusStr
			}
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_ORDER_VALUE
			r.isValid = false
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		} else {
			r.point = r.point + 2
			r.suggestInfo.suggestDistance += 100
		}
		if !isNilOrDefaultValue(c.OrderConditionField.MinSkuQuantity) && *c.OrderConditionField.MinSkuQuantity > totalItem {
			r.message = "Mua thêm " + fmt.Sprintf("%d", *c.OrderConditionField.MinSkuQuantity-totalItem) + " loại sản phẩm để sử dụng mã"
			r.suggestInfo.suggestQuantity = *c.OrderConditionField.MinSkuQuantity - totalItem
			r.suggestInfo.suggestDistance += float64(totalItem) / float64(*c.OrderConditionField.MinSkuQuantity) * 100
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_TYPES
			r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %d sản phẩm khác từ nhà bán hàng để được {reward}", *c.OrderConditionField.MinSkuQuantity-totalItem)
			if r.sellerName != "" {
				r.message = "Mua thêm " + fmt.Sprintf("%d", *c.OrderConditionField.MinSkuQuantity-totalItem) + " loại sản phẩm từ " + r.sellerName
			}
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			r.isValid = false
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		} else {
			r.point = r.point + 1
			r.suggestInfo.suggestDistance += 100
		}

		// TODO: Handle condition newSKU and repeatSKU
		// Retrive last month's SKUs from customer experience data
		if r.customerExperience != nil && len(r.customerExperience.SkusData) > 0 {
			target := "purchased_1"
			if data, ok := r.customerExperience.SkusData[target]; ok {
				lastMonthSKUs = data
			}
		}
		totalLastMonthSKU := len(lastMonthSKUs)
		if totalItem > 0 {
			diffSKUs := getDuplicateTwoSliceString(lastMonthSKUs, cartSKUs)
			totalDiffSKU := len(diffSKUs)
			newSKUs = totalItem - totalDiffSKU
			repeatSKUs = totalDiffSKU
		}

		// Check if more new SKUs are needed
		if !isNilOrDefaultValue(c.OrderConditionField.NewSkuQuantity) && newSKUs < *c.OrderConditionField.NewSkuQuantity {
			needBuyMore := *c.OrderConditionField.NewSkuQuantity - newSKUs
			r.message = "Mua thêm " + fmt.Sprintf("%d", needBuyMore) + " loại sản phẩm chưa từng mua"
			r.suggestInfo.suggestQuantity = needBuyMore
			r.suggestInfo.suggestDistance += float64(newSKUs) / float64(*c.OrderConditionField.NewSkuQuantity) * 100
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_TYPES
			r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %d sản phẩm chưa từng mua từ nhà bán hàng để được {reward}", needBuyMore)
			if r.sellerName != "" {
				r.message = "Mua thêm " + fmt.Sprintf("%d", needBuyMore) + " loại sản phẩm chưa từng mua từ " + r.sellerName
			}
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			r.isValid = false
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}
		// Check if more repeat SKUs are needed
		if !isNilOrDefaultValue(c.OrderConditionField.RepeatSkuQuantity) && totalLastMonthSKU > 0 && repeatSKUs < *c.OrderConditionField.RepeatSkuQuantity {
			needBuyMore := *c.OrderConditionField.RepeatSkuQuantity - repeatSKUs
			r.message = "Mua thêm " + fmt.Sprintf("%d", needBuyMore) + " loại sản phẩm đã từng mua"
			r.suggestInfo.suggestQuantity = needBuyMore
			r.suggestInfo.suggestDistance += float64(repeatSKUs) / float64(*c.OrderConditionField.RepeatSkuQuantity) * 100
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_TYPES
			r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %d sản phẩm đã từng mua từ nhà bán hàng để được {reward}", needBuyMore)
			if r.sellerName != "" {
				r.message = "Mua thêm " + fmt.Sprintf("%d", needBuyMore) + " loại sản phẩm đã từng mua từ " + r.sellerName
			}
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			r.isValid = false
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (o *orderCondition) getRewardCount(r *result, conditions []model.PromotionCondition, operator string) {
	var newRewardCountAnd int
	var newRewardCountOr int
	for _, c := range conditions {
		if c.OrderConditionField.MinTotalPrice != nil && *c.OrderConditionField.MinTotalPrice != 0 {
			newRewardCount := int(r.cart.Price) / *c.OrderConditionField.MinTotalPrice
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if c.OrderConditionField.MinTotalPrice != nil && !(*c.OrderConditionField.MinTotalPrice > int(r.cart.Price)) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}
		if !isNilOrDefaultValue(c.OrderConditionField.MinSkuQuantity) && *c.OrderConditionField.MinSkuQuantity != 0 {
			newRewardCount := r.cart.TotalItem / *c.OrderConditionField.MinSkuQuantity
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if !(*c.OrderConditionField.MinSkuQuantity > r.cart.TotalItem) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}
	}
	var newRewardCount int
	newRewardCount = newRewardCountOr
	if newRewardCountAnd != 0 && newRewardCountAnd < newRewardCountOr || newRewardCount == 0 {
		newRewardCount = newRewardCountAnd
	}
	if r.rewardCount == 0 || newRewardCount < r.rewardCount {
		r.rewardCount = newRewardCount
	}
}

func (o *orderCondition) setNext(c condition) {
	o.next = c
}

// customer
type customerCondition struct {
	next condition
}

func (c *customerCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {

	for _, c := range conditions {
		r.isValid = true

		// After checkout and Change payment-method, cart.ValidateOrder will true
		// In that case, no need to do anything else
		if r.cart.ValidateOrder {
			return
		}

		if c.CustomerConditionField.MaxOrderCount != nil && *c.CustomerConditionField.MaxOrderCount < r.customer.OrderCount {
			r.isValid = false
			r.message = "Mã giảm giá này hiện không hoạt động"
			r.hideDisplay = true
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if c.CustomerConditionField.MinOrderCount != nil && *c.CustomerConditionField.MinOrderCount > r.customer.OrderCount {
			r.isValid = false
			r.message = "Mua thêm " + fmt.Sprintf("%d", *c.CustomerConditionField.MinOrderCount-r.customer.OrderCount) + " đơn hàng nữa để sử dụng mã"
			r.hideDisplay = true
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if c.CustomerConditionField.IndexOrder != nil && *c.CustomerConditionField.IndexOrder != r.customer.OrderCount+1 {
			r.isValid = false
			r.message = "Mua thêm " + fmt.Sprintf("%d", *c.CustomerConditionField.IndexOrder-r.customer.OrderCount-1) + " đơn hàng để sử dụng mã"
			r.hideDisplay = true
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if c.CustomerConditionField.MinDayNoOrder != nil {
			now := time.Now()
			lastOrderTime := now
			if r.customer.LastOrderTime != nil {
				lastOrderTime = *r.customer.LastOrderTime
			} else if r.customer.ConfirmedTime != nil {
				lastOrderTime = *r.customer.ConfirmedTime
			} else if r.customer.CreatedTime != nil {
				lastOrderTime = *r.customer.CreatedTime
			}
			lastOrderTime = lastOrderTime.Add(time.Duration(*c.CustomerConditionField.MinDayNoOrder) * 24 * time.Hour)
			if now.Before(lastOrderTime) {
				r.isValid = false
				r.message = "Bạn không đủ điều kiện sử dụng mã"
				r.hideDisplay = true
				if operator == enum.Operator.AND {
					return
				} else {
					continue
				}
			}
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (c *customerCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if c.next != nil && r.isValid {
				r.message = ""
				c.next.execute(r)
			}
			if c.next != nil && r.message == "" {
				c.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if c.next != nil {
				c.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.CUSTOMER_HISTORY]; ok {
		// todo check and conditions
		if len(data.AndConditions) > 0 {
			c.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
		}
		if len(data.OrConditions) > 0 {
			c.validate(r, data.OrConditions, enum.Operator.OR)
			if r.isValid {
				return
			}
		}
	}
}

func (c *customerCondition) setNext(c2 condition) {
	c.next = c2
}

func (c *customerCondition) getRewardCount(r *result, conditions []model.PromotionCondition, operator string) {
	// no action
}

// product
type productCondition struct {
	next condition
}

func (p *productCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {
	for _, c := range conditions {
		if r.mapSkuMessage == nil {
			r.mapSkuMessage = make(map[string]string)
		}
		r.isValid = true
		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", c.ProductConditionField.ProductCode)
		if c.ProductConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *c.ProductConditionField.SellerCode)
		}

		if c.ProductConditionField.ProductName == "" {
			c.ProductConditionField.ProductName = "sản phẩm theo điều kiện"
		}

		if _, ok := r.cart.ProductValue[keyValidate]; !ok {
			r.isValid = false
			r.suggestInfo.suggestQuantity = *c.ProductConditionField.MinQuantity
			r.suggestInfo.suggestDistance += float64(0) / float64(*c.ProductConditionField.MinQuantity) * 100
			// r.suggestInfo.suggestProductCode = c.ProductConditionField.ProductCode
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			if operator == enum.Operator.OR {
				r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %s từ nhà bán hàng để được {reward}", c.ProductConditionField.ProductName)
			}
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			r.message = fmt.Sprintf("Mua thêm %s", c.ProductConditionField.ProductName)
			if c.ProductConditionField.MinQuantity != nil {
				if operator == enum.Operator.AND {
					r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %s từ nhà bán hàng để được {reward}", c.ProductConditionField.ProductName)
				}
				r.message = fmt.Sprintf("Mua thêm %d %s", *c.ProductConditionField.MinQuantity, c.ProductConditionField.ProductName)
			}
			r.mapSkuMessage[keyValidate] = r.message
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		} else {
			r.point = r.point + 3
			r.suggestInfo.suggestDistance += 100
		}
		item := r.cart.ProductValue[keyValidate]
		if c.ProductConditionField.MinQuantity != nil && int(item.Quantity) < *c.ProductConditionField.MinQuantity {
			r.isValid = false
			r.suggestInfo.suggestQuantity = *c.ProductConditionField.MinQuantity - int(item.Quantity)
			r.suggestInfo.suggestDistance += float64(item.Quantity) / float64(*c.ProductConditionField.MinQuantity) * 100
			r.suggestInfo.suggestProductCode = c.ProductConditionField.ProductCode
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %d sản phẩm {productName} từ nhà bán hàng để được {reward}", *c.ProductConditionField.MinQuantity-int(item.Quantity))
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			r.message = fmt.Sprintf("Mua thêm %d %s", *c.ProductConditionField.MinQuantity-int(item.Quantity), c.ProductConditionField.ProductName)
			r.mapSkuMessage[keyValidate] = r.message
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		} else {
			r.point = r.point + 2
			r.suggestInfo.suggestDistance += 100
		}
		if c.ProductConditionField.MinTotalPrice != nil && int(item.Total) < *c.ProductConditionField.MinTotalPrice {
			r.isValid = false
			r.message = fmt.Sprintf("Mua thêm %sđ cho %s", utils.FormatVNDCurrency(fmt.Sprint(*c.ProductConditionField.MinTotalPrice-int(item.Total))), c.ProductConditionField.ProductName)
			r.suggestInfo.suggestAmount = *c.ProductConditionField.MinTotalPrice - int(item.Total)
			r.suggestInfo.suggestDistance += float64(item.Total) / float64(*c.ProductConditionField.MinTotalPrice) * 100
			r.suggestInfo.suggestProductCode = c.ProductConditionField.ProductCode
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_TOTAL_PRICE
			r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %sđ cho %s để được {reward}", utils.FormatVNDCurrency(fmt.Sprint(*c.ProductConditionField.MinTotalPrice-int(item.Total))), c.ProductConditionField.ProductName)
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_ORDER_VALUE
			r.mapSkuMessage[keyValidate] = r.message
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		} else {
			r.point = r.point + 1
			r.suggestInfo.suggestDistance += 100
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (p *productCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if p.next != nil && r.isValid {
				r.message = ""
				p.next.execute(r)
			}
			if p.next != nil && r.message == "" {
				p.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if p.next != nil {
				p.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.PRODUCT]; ok && r.cart != nil {
		// todo map product
		if r.cart.ProductValue == nil {
			makeMapItem(r.cart, r.cart.CartItems)
		}
		if len(data.AndConditions) > 0 {
			p.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
			if r.isValid {
				p.getRewardCount(r, data.AndConditions, enum.Operator.AND)
			}
		}
		if len(data.OrConditions) > 0 {
			p.validate(r, data.OrConditions, enum.Operator.OR)
			if r.isValid {
				p.getRewardCount(r, data.OrConditions, enum.Operator.OR)
				return
			}
		}
	}
}

func (p *productCondition) getRewardCount(r *result, conditions []model.PromotionCondition, operator string) {
	var newRewardCountAnd int
	var newRewardCountOr int
	for _, c := range conditions {
		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", c.ProductConditionField.ProductCode)
		if c.ProductConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *c.ProductConditionField.SellerCode)
		}
		item := r.cart.ProductValue[keyValidate]
		if c.ProductConditionField.MinQuantity != nil && *c.ProductConditionField.MinQuantity != 0 {
			newRewardCount := int(item.Quantity) / *c.ProductConditionField.MinQuantity
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if c.ProductConditionField.MinQuantity != nil && !(int(item.Quantity) < *c.ProductConditionField.MinQuantity) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}

		if c.ProductConditionField.MinTotalPrice != nil && *c.ProductConditionField.MinTotalPrice != 0 {
			newRewardCount := int(item.Total) / *c.ProductConditionField.MinTotalPrice
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if c.ProductConditionField.MinTotalPrice != nil && !(int(item.Total) < *c.ProductConditionField.MinTotalPrice) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}

	}
	var newRewardCount int
	newRewardCount = newRewardCountOr
	if newRewardCountAnd != 0 && newRewardCountAnd < newRewardCountOr || newRewardCount == 0 {
		newRewardCount = newRewardCountAnd
	}
	if r.rewardCount == 0 || newRewardCount < r.rewardCount {
		r.rewardCount = newRewardCount
	}
}

func (p *productCondition) setNext(c condition) {
	p.next = c
}

type productTagCondition struct {
	next condition
}

func (p *productTagCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {
	for _, c := range conditions {
		r.isValid = true
		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", c.ProductTagConditionField.TagCode)
		if c.ProductTagConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *c.ProductTagConditionField.SellerCode)
		}

		if c.ProductTagConditionField.TagName == "" {
			c.ProductTagConditionField.TagName = "sản phẩm theo điều kiện"
		}

		if _, ok := r.cart.TagValue[keyValidate]; !ok {
			r.isValid = false
			r.message = fmt.Sprintf("Mua thêm sản phẩm trong nhóm %s", c.ProductTagConditionField.TagName)
			r.suggestInfo.suggestQuantity = *c.ProductTagConditionField.MinQuantity
			r.suggestInfo.suggestDistance += float64(0) / float64(*c.ProductTagConditionField.MinQuantity) * 100
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			if c.ProductTagConditionField.MinQuantity != nil {
				r.message = fmt.Sprintf("Mua thêm %d sản phẩm trong nhóm %s", *c.ProductTagConditionField.MinQuantity, c.ProductTagConditionField.TagName)
				r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm sản phẩm trong nhóm %s để được {reward}", c.ProductTagConditionField.TagName)
			}
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		} else {
			r.point = r.point + 4
			r.suggestInfo.suggestDistance += 100
		}
		item := r.cart.TagValue[keyValidate]
		if c.ProductTagConditionField.MinQuantity != nil && int(item.Quantity) < *c.ProductTagConditionField.MinQuantity {
			r.isValid = false
			r.message = fmt.Sprintf("Mua thêm %d sản phẩm trong nhóm %s", *c.ProductTagConditionField.MinQuantity-int(item.Quantity), c.ProductTagConditionField.TagName)
			r.suggestInfo.suggestQuantity = *c.ProductTagConditionField.MinQuantity - int(item.Quantity)
			r.suggestInfo.suggestDistance += float64(item.Quantity) / float64(*c.ProductTagConditionField.MinQuantity) * 100
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %d sản phẩm trong nhóm %s để được {reward}", *c.ProductTagConditionField.MinQuantity-int(item.Quantity), c.ProductTagConditionField.TagName)
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		} else {
			r.point = r.point + 3
			r.suggestInfo.suggestDistance += 100
		}

		if c.ProductTagConditionField.MinTotalPrice != nil && int(item.Total) < *c.ProductTagConditionField.MinTotalPrice {
			r.isValid = false
			r.message = fmt.Sprintf("Mua thêm %sđ trong nhóm %s", utils.FormatVNDCurrency(fmt.Sprint(*c.ProductTagConditionField.MinTotalPrice-int(item.Total))), c.ProductTagConditionField.TagName)
			r.suggestInfo.suggestAmount = *c.ProductTagConditionField.MinTotalPrice - int(item.Total)
			r.suggestInfo.suggestDistance += float64(item.Total) / float64(*c.ProductTagConditionField.MinTotalPrice) * 100
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_TOTAL_PRICE
			r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %sđ trong nhóm %s để được {reward}", utils.FormatVNDCurrency(fmt.Sprint(*c.ProductTagConditionField.MinTotalPrice-int(item.Total))), c.ProductTagConditionField.TagName)
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_ORDER_VALUE
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		} else {
			r.point = r.point + 2
			r.suggestInfo.suggestDistance += 100
		}
		if c.ProductTagConditionField.SkuQuantity != nil && item.TotalItem < *c.ProductTagConditionField.SkuQuantity {
			r.isValid = false
			r.message = fmt.Sprintf("Mua thêm %d loại sản phẩm trong nhóm %s", *c.ProductTagConditionField.SkuQuantity-int(item.TotalItem), c.ProductTagConditionField.TagName)
			r.suggestInfo.suggestQuantity = *c.ProductTagConditionField.SkuQuantity - int(item.TotalItem)
			r.suggestInfo.suggestDistance += float64(item.TotalItem) / float64(*c.ProductTagConditionField.SkuQuantity) * 100
			r.suggestInfo.suggestErrorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_TYPES
			r.suggestInfo.suggestTemplate = fmt.Sprintf("Mua thêm %d sản phẩm trong nhóm %s từ nhà bán hàng để được {reward}", *c.ProductTagConditionField.SkuQuantity-int(item.TotalItem), c.ProductTagConditionField.TagName)
			r.errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY

			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		} else {
			r.point = r.point + 1
			r.suggestInfo.suggestDistance += 100
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (p *productTagCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if p.next != nil && r.isValid {
				r.message = ""
				p.next.execute(r)
			}
			if p.next != nil && r.message == "" {
				p.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if p.next != nil {
				p.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.PRODUCT_TAG]; ok && r.cart != nil {
		// todo map product
		if r.cart.TagValue == nil {
			makeMapItem(r.cart, r.cart.CartItems)
		}
		if len(data.AndConditions) > 0 {
			p.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
			if r.isValid {
				p.getRewardCount(r, data.AndConditions, enum.Operator.AND)
			}
		}
		if len(data.OrConditions) > 0 {
			p.validate(r, data.OrConditions, enum.Operator.OR)
			if r.isValid {
				p.getRewardCount(r, data.OrConditions, enum.Operator.OR)
				return
			}
		}
	}
}

func (p *productTagCondition) getRewardCount(r *result, conditions []model.PromotionCondition, operator string) {
	var newRewardCountAnd int
	var newRewardCountOr int
	for _, c := range conditions {
		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", c.ProductTagConditionField.TagCode)
		if c.ProductTagConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *c.ProductTagConditionField.SellerCode)
		}
		item := r.cart.TagValue[keyValidate]
		if c.ProductTagConditionField.MinQuantity != nil && *c.ProductTagConditionField.MinQuantity != 0 {
			newRewardCount := int(item.Quantity) / *c.ProductTagConditionField.MinQuantity
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if c.ProductTagConditionField.MinQuantity != nil && !(int(item.Quantity) < *c.ProductTagConditionField.MinQuantity) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}

		if c.ProductTagConditionField.MinTotalPrice != nil && *c.ProductTagConditionField.MinTotalPrice != 0 {
			newRewardCount := int(item.Total) / *c.ProductTagConditionField.MinTotalPrice
			if operator == enum.Operator.AND {
				if newRewardCount < newRewardCountAnd || newRewardCountAnd == 0 {
					newRewardCountAnd = newRewardCount
				}
			} else if c.ProductTagConditionField.MinTotalPrice != nil && !(int(item.Total) < *c.ProductTagConditionField.MinTotalPrice) {
				if newRewardCount > newRewardCountOr || newRewardCountOr == 0 {
					newRewardCountOr = newRewardCount
				}
			}
		}

	}
	var newRewardCount int
	newRewardCount = newRewardCountOr
	if newRewardCountAnd != 0 && newRewardCountAnd < newRewardCountOr || newRewardCount == 0 {
		newRewardCount = newRewardCountAnd
	}
	if r.rewardCount == 0 || newRewardCount < r.rewardCount {
		r.rewardCount = newRewardCount
	}
}

func (p *productTagCondition) setNext(c condition) {
	p.next = c
}

type productBlackListCondition struct {
	next condition
}

func (p *productBlackListCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if p.next != nil && r.isValid {
				r.message = ""
				p.next.execute(r)
			}
			if p.next != nil && r.message == "" {
				p.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if p.next != nil {
				p.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.PRODUCT_BLACKLIST]; ok && r.cart != nil {
		// todo map product
		if r.cart.ProductValue == nil {
			makeMapItem(r.cart, r.cart.CartItems)
		}
		if len(data.AndConditions) > 0 {
			p.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
			if r.isValid {
				p.getRewardCount(r, data.AndConditions, enum.Operator.AND)
			}
		}
	}
}

func (p *productBlackListCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {
	for _, c := range conditions {
		r.isValid = true
		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", c.ProductBlackListConditionField.ProductCode)
		if c.ProductBlackListConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *c.ProductBlackListConditionField.SellerCode)
		}

		if _, ok := r.cart.ProductValue[keyValidate]; ok {
			r.isValid = false
			r.message = "Không áp dụng khi mua sản phẩm " + c.ProductBlackListConditionField.ProductName
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (p *productBlackListCondition) setNext(c condition) {
	p.next = c
}

func (p *productBlackListCondition) getRewardCount(r *result, conditions []model.PromotionCondition, s string) {
	// do not need to implement for this condition
}

type paymentCondition struct {
	next condition
}

func (p *paymentCondition) execute(r *result) {
	defer func() {
		if r.operator == "AND" {
			if p.next != nil && r.isValid {
				r.message = ""
				p.next.execute(r)
			}
			if p.next != nil && r.message == "" {
				p.next.execute(r)
			}
		} else {
			if r.isValid {
				r.message = ""
				return
			} else if p.next != nil {
				p.next.execute(r)
			}
		}
	}()
	if data, ok := r.promoCondition[enum.ConditionType.PAYMENT_METHOD]; ok {
		// todo check and conditions
		if len(data.AndConditions) > 0 {
			p.validate(r, data.AndConditions, enum.Operator.AND)
			if !r.isValid {
				return
			}
		}
		if len(data.OrConditions) > 0 {
			p.validate(r, data.OrConditions, enum.Operator.OR)
			if r.isValid {
				return
			}
		}
	}
}

func (p *paymentCondition) validate(r *result, conditions []model.PromotionCondition, operator string) {
	for _, c := range conditions {
		r.isValid = true
		if r.cart.PaymentMethod != "" && c.PaymentConditionField.PaymentMethod != r.cart.PaymentMethod {
			r.isValid = false
			r.message = fmt.Sprintf("Mã giảm giá chỉ áp dụng cho hình thức %s", c.PaymentConditionField.PaymentMethodName)
			r.errorCode = enum.VoucherErrorCode.INVALID_PAYMENT_METHOD
			if operator == enum.Operator.AND {
				return
			} else {
				continue
			}
		}

		if operator == enum.Operator.OR && r.isValid {
			return
		}
	}
}

func (p *paymentCondition) setNext(c condition) {
	p.setNext(c)
}

func (p *paymentCondition) getRewardCount(r *result, conditions []model.PromotionCondition, s string) {
	// don't need to implement
}

func makeMapItem(cart *model.Cart, items []model.CartItemInternal) {
	cart.TagValue = make(map[string]struct {
		Quantity  int64
		Total     int64
		TotalItem int
	})
	cart.ProductValue = make(map[string]struct {
		Quantity int64
		Total    int64
	})
	cart.SkuValue = make(map[string]struct {
		Quantity int64
		Total    int64
	})
	cart.SellerValue = make(map[string]struct {
		Quantity  int64
		Total     int64
		TotalItem int
		SKUs      []string
	})
	cart.StoreValue = make(map[string]struct {
		Quantity  int64
		Total     int64
		TotalItem int
		SKUs      []string
	})
	for _, item := range items {
		key1 := fmt.Sprintf("%s_%s", item.SellerCode, item.ProductCode)
		key2 := fmt.Sprintf("%s_%s", "SELLER_CODE", item.ProductCode)
		skuKey := item.ProductSKU
		sellerKey := item.SellerCode
		storeKey := item.StoreCode
		if data, ok := cart.ProductValue[key1]; ok {
			data.Total = data.Total + item.Total
			data.Quantity = data.Quantity + item.Quantity
			cart.ProductValue[key1] = data

		} else {
			cart.ProductValue[key1] = struct {
				Quantity int64
				Total    int64
			}{Quantity: item.Quantity, Total: item.Total}
		}
		if data, ok := cart.SkuValue[skuKey]; ok {
			data.Total = data.Total + item.Total
			data.Quantity = data.Quantity + item.Quantity
			cart.SkuValue[skuKey] = data
		} else {
			cart.SkuValue[skuKey] = struct {
				Quantity int64
				Total    int64
			}{Quantity: item.Quantity, Total: item.Total}
		}
		if data, ok := cart.ProductValue[key2]; ok {
			data.Total = data.Total + item.Total
			data.Quantity = data.Quantity + item.Quantity
			cart.ProductValue[key2] = data

		} else {
			cart.ProductValue[key2] = struct {
				Quantity int64
				Total    int64
			}{Quantity: item.Quantity, Total: item.Total}
		}
		if data, ok := cart.SellerValue[sellerKey]; ok {
			data.Total = data.Total + item.Total
			data.Quantity = data.Quantity + item.Quantity
			data.TotalItem = data.TotalItem + 1
			data.SKUs = append(data.SKUs, skuKey)
			cart.SellerValue[sellerKey] = data
		} else {
			cart.SellerValue[sellerKey] = struct {
				Quantity  int64
				Total     int64
				TotalItem int
				SKUs      []string
			}{Quantity: item.Quantity, Total: item.Total, TotalItem: 1, SKUs: []string{skuKey}}
			//cart.SellerCodes = append(cart.SellerCodes, sellerKey)
		}
		if data, ok := cart.StoreValue[storeKey]; ok {
			data.Total = data.Total + item.Total
			data.Quantity = data.Quantity + item.Quantity
			data.TotalItem = data.TotalItem + 1
			data.SKUs = append(data.SKUs, skuKey)
			cart.StoreValue[storeKey] = data
		} else {
			cart.StoreValue[storeKey] = struct {
				Quantity  int64
				Total     int64
				TotalItem int
				SKUs      []string
			}{Quantity: item.Quantity, Total: item.Total, TotalItem: 1, SKUs: []string{skuKey}}
		}
		for _, tag := range item.ProductTags {
			keyTag1 := fmt.Sprintf("%s_%s", item.SellerCode, tag)
			keyTag2 := fmt.Sprintf("%s_%s", "SELLER_CODE", tag)
			if data, ok := cart.TagValue[keyTag1]; ok {
				data.Total = data.Total + item.Total
				data.Quantity = data.Quantity + item.Quantity
				data.TotalItem = data.TotalItem + 1
				cart.TagValue[keyTag1] = data
			} else {
				cart.TagValue[keyTag1] = struct {
					Quantity  int64
					Total     int64
					TotalItem int
				}{Quantity: item.Quantity, Total: item.Total, TotalItem: 1}
			}
			if data, ok := cart.TagValue[keyTag2]; ok {
				data.Total = data.Total + item.Total
				data.Quantity = data.Quantity + item.Quantity
				data.TotalItem = data.TotalItem + 1
				cart.TagValue[keyTag2] = data
			} else {
				cart.TagValue[keyTag2] = struct {
					Quantity  int64
					Total     int64
					TotalItem int
				}{Quantity: item.Quantity, Total: item.Total, TotalItem: 1}
			}
		}
	}
}

func Run(in result) (bool, string) {
	customerCondition := &customerCondition{}
	productCondition := &productCondition{}
	orderCondition := &orderCondition{}
	productTagCondition := &productTagCondition{}

	productTagCondition.setNext(customerCondition)
	productCondition.setNext(productTagCondition)
	orderCondition.setNext(productCondition)
	orderCondition.execute(&in)
	return in.isValid, in.message
}
