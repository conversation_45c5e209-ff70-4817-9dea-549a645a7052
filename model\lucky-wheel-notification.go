package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

// LuckyWheelNotification is struct to define model
type LuckyWheelNotification struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code  string                       `json:"code,omitempty" bson:"code,omitempty"`
	Scope *LuckyWheelNotificationScope `json:"scope,omitempty" bson:"scope,omitempty"`

	Message        string     `json:"message,omitempty" bson:"message,omitempty"`
	StartTime      *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime        *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	IsActive       *bool      `json:"isActive,omitempty" bson:"is_active,omitempty"`
	RepeatDuration *int64     `json:"repeatDuration,omitempty" bson:"repeat_duration,omitempty"` // thời gian lặp lại thông báo (giây)
	Duration       *int64     `json:"duration,omitempty" bson:"duration,omitempty"`              // thời gian hiển thị thông báo (giây)
	LuckyWheelCode string     `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
}

type LuckyWheelNotificationScope struct {
	ProvinceCodes     []string `json:"provinceCodes,omitempty" bson:"province_codes,omitempty"`
	RegionCodes       []string `json:"regionCodes,omitempty" bson:"region_codes,omitempty"`
	CustomerLevels    []string `json:"levels,omitempty" bson:"levels,omitempty"`
	CustomerScopes    []string `json:"scopes,omitempty" bson:"scopes,omitempty"`
	CustomerApplyType string   `json:"customerApplyType,omitempty" bson:"customer_apply_type,omitempty"` // ALL || MANY
}

// LuckyWheelNotificationDB is func init model
var LuckyWheelNotificationDB = &db.Instance{
	ColName:        "lucky_wheel_notification",
	TemplateObject: &LuckyWheelNotification{},
}

// InitLuckyWheelNotificationModel is func init model
func InitLuckyWheelNotificationModel(s *mongo.Database) {
	LuckyWheelNotificationDB.ApplyDatabase(s)
}
