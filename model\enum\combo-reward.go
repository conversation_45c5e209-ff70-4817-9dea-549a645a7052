package enum

type ComboRewardEnumType string

type comboRewardType struct {
	FOUR ComboRewardEnumType
	NINE ComboRewardEnumType
}

var ComboRewardType = &comboRewardType{
	FOUR: "FOUR",
	NINE: "NINE",
}

var MapComboTypeToNumber = map[ComboRewardEnumType]int{
	"FOUR": 4,
	"NINE": 9,
}

type ComboRewardItemType string

type comboRewardItem struct {
	VOUCHER        ComboRewardItemType
	POINTS         ComboRewardItemType
	TURNS          ComboRewardItemType
	TICKET_PATTERN ComboRewardItemType
	PIECE          ComboRewardItemType
	OTHER          ComboRewardItemType
}

var ComboRewardItem = &comboRewardItem{
	VOUCHER:        "VOUCHER",
	POINTS:         "POINTS",
	TURNS:          "TURNS",
	TICKET_PATTERN: "TICKET_PATTERN",
	PIECE:          "PIECE",
	OTHER:          "OTHER",
}
