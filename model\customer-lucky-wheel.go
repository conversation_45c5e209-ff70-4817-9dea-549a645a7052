package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type CustomerLuckyWheel struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CustomerID       int64             `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	AccountID        int64             `json:"accountId,omitempty" bson:"account_id,omitempty"`
	CustomerPhone    string            `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`
	LuckyWheelCode   string            `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	Quantity         int64             `json:"quantity,omitempty" bson:"quantity,omitempty"`
	UsedQuantity     int64             `json:"usedQuantity,omitempty" bson:"used_quantity,omitempty"`
	UsedItemQuantity *map[string]int64 `json:"usedItemQuantity,omitempty" bson:"used_item_quantity,omitempty"`
	Point            int64             `json:"point,omitempty" bson:"point,omitempty"`
	ExchangedItem    *map[string]int64 `json:"exchangedItem,omitempty" bson:"exchanged_item,omitempty"`

	UsedFreeQuantity int64      `json:"usedFreeQuantity,omitempty" bson:"used_free_quantity,omitempty"`
	TurnType         string     `json:"turnType,omitempty" bson:"turn_type,omitempty"`
	LastFreeTime     *time.Time `json:"lastFreeTime,omitempty" bson:"last_free_time,omitempty"`

	Rewards []CustomerLuckyWheelReward `json:"rewards,omitempty" bson:"rewards,omitempty"`
}

type CustomerLuckyWheelReward struct {
	ItemCode          string `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	VoucherCode       string `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	Ticket            string `json:"ticket,omitempty" bson:"ticket,omitempty"`
	TurnsRotation     int64  `json:"turnsRotation,omitempty" bson:"turns_rotation,omitempty"`
	Points            int64  `json:"points,omitempty" bson:"points,omitempty"`
	RewardDescription string `json:"rewardDescription,omitempty" bson:"reward_description,omitempty"`
	PieceCode         string `json:"pieceCode,omitempty" bson:"piece_code,omitempty"`
	ComboCode         string `json:"comboCode,omitempty" bson:"combo_code,omitempty"`
}

var CustomerLuckyWheelDB = &db.Instance{
	ColName:        "customer_lucky_wheel",
	TemplateObject: &CustomerLuckyWheel{},
}

// InitCustomerLuckyWheelModel is func init model
func InitCustomerLuckyWheelModel(s *mongo.Database) {
	CustomerLuckyWheelDB.ApplyDatabase(s)
}

type CustomerLuckyWheelLog struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CustomerID      int64              `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	AccountID       int64              `json:"accountId,omitempty" bson:"account_id,omitempty"`
	CustomerPhone   string             `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`
	LuckyWheelCode  string             `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	Quantity        int64              `json:"quantity,omitempty" bson:"quantity,omitempty"`
	Key             string             `json:"key,omitempty" bson:"key,omitempty"`
	SystemDisplay   string             `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
	Source          string             `json:"source,omitempty" bson:"-"`
	Data            interface{}        `json:"data,omitempty" bson:"data,omitempty"`
}

var CustomerLuckyWheelLogDB = &db.Instance{
	ColName:        "customer_lucky_wheel_log",
	TemplateObject: &CustomerLuckyWheelLog{},
}

// InitCustomerLuckyWheelLogModel is func init model
func InitCustomerLuckyWheelLogModel(s *mongo.Database) {
	CustomerLuckyWheelLogDB.ApplyDatabase(s)
}
