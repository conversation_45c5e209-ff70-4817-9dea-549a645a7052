package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

// LuckyWheelStore is struct to define table lucky_wheel_store
type LuckyWheelStore struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code           string  `json:"code,omitempty" bson:"code,omitempty"`
	LuckyWheelCode string  `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	Name           string  `json:"name,omitempty" bson:"name,omitempty"`
	Description    *string `json:"description,omitempty" bson:"description,omitempty"`
	BackgroundUrl  *string `json:"backgroundUrl,omitempty" bson:"background_url,omitempty"`
	Version        string  `json:"version,omitempty" bson:"version,omitempty"`

	StoreItems []*LuckyWheelStoreItem `json:"storeItems,omitempty" bson:"-"` // view only
}

// LuckyWheelStoreDB is var db
var LuckyWheelStoreDB = &db.Instance{
	ColName:        "lucky_wheel_store",
	TemplateObject: &LuckyWheelStore{},
}

// InitLuckyWheelStoreModel is func init model
func InitLuckyWheelStoreModel(s *mongo.Database) {
	LuckyWheelStoreDB.ApplyDatabase(s)
}

// index for collection
// db.getCollection('lucky_wheel_store').createIndex({lucky_wheel_code: 1}, {unique: true})

// --------------------------------------------------

// LuckyWheelStoreItem is struct to define table lucky_wheel_store_item
type LuckyWheelStoreItem struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	StoreCode       string     `json:"storeCode,omitempty" bson:"store_code,omitempty"`
	LuckyWheelCode  string     `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	StoreItemCode   string     `json:"storeItemCode,omitempty" bson:"store_item_code,omitempty"`
	Name            string     `json:"name,omitempty" bson:"name,omitempty"`
	DisplayUrl      string     `json:"displayUrl,omitempty" bson:"display_url,omitempty"`
	Point           int        `json:"point,omitempty" bson:"point,omitempty"`              // 0 is free
	MaxExchange     *int       `json:"maxExchange,omitempty" bson:"max_exchange,omitempty"` // null or 0 is unlimited
	StartTime       *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime         *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	IsActive        *bool      `json:"isActive,omitempty" bson:"is_active,omitempty"`
	DisplayPriority *int       `json:"displayPriority,omitempty" bson:"display_priority,omitempty"` // largest is first
	ActionName      *string    `json:"actionName,omitempty" bson:"action_name,omitempty"`
	ActionLink      *string    `json:"actionLink,omitempty" bson:"action_link,omitempty"`
	Rewards         *[]*struct {
		TypeReward            enum.LuckyWheelItemRewardType `json:"typeReward,omitempty" bson:"type_reward,omitempty"`
		RewardDescription     string                        `json:"rewardDescription,omitempty" bson:"reward_description,omitempty"`
		Points                int64                         `json:"points,omitempty" bson:"points,omitempty"`
		LkPoints              int64                         `json:"lkPoints,omitempty" bson:"lk_points,omitempty"`
		PromotionID           int64                         `json:"promotionID,omitempty" bson:"promotion_id,omitempty"`
		TicketPattern         string                        `json:"ticketPattern,omitempty" bson:"ticket_pattern,omitempty"`
		VoucherPattern        *string                       `json:"voucherPattern,omitempty" bson:"voucher_pattern,omitempty"`
		VoucherCode           string                        `json:"voucherCode,omitempty" bson:"-"`
		VoucherID             int64                         `json:"voucherId,omitempty" bson:"-"`
		TurnsRotation         int64                         `json:"turnsRotation,omitempty" bson:"turns_rotation,omitempty"`
		NumberOfDayUseVoucher *int64                        `json:"numberOfDayUseVoucher,omitempty" bson:"number_of_day_use_voucher,omitempty"`
	} `json:"rewards,omitempty" bson:"rewards,omitempty"`
	Version string `json:"version,omitempty" bson:"version,omitempty"`

	ExchangedCount int  `json:"exchangedCount,omitempty" bson:"-"` // set data to view only, count of exchanged
	CanExchange    bool `json:"canExchange,omitempty" bson:"-"`    // set data to view only, true if maxExchange is null or 0 or maxExchange > exchangedCount
}

// LuckyWheelStoreItemDB is var db
var LuckyWheelStoreItemDB = &db.Instance{
	ColName:        "lucky_wheel_store_item",
	TemplateObject: &LuckyWheelStoreItem{},
}

// InitLuckyWheelStoreItemModel is func init model
func InitLuckyWheelStoreItemModel(s *mongo.Database) {
	LuckyWheelStoreItemDB.ApplyDatabase(s)
}

// index for collection
// db.getCollection('lucky_wheel_store_item').createIndex({lucky_wheel_code: 1}, {unique: true})
// db.getCollection('lucky_wheel_store_item').createIndex({store_code: 1, lucky_wheel_code: 1}, {unique: true})

// --------------------------------------------------

// LuckyWheelStoreLog is struct to define table lucky_wheel_store_log
type LuckyWheelStoreLog struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	StoreCode      string `json:"storeCode,omitempty" bson:"store_code,omitempty"`
	LuckyWheelCode string `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	StoreItemCode  string `json:"storeItemCode,omitempty" bson:"store_item_code,omitempty"`
	StoreItemName  string `json:"storeItemName,omitempty" bson:"store_item_name,omitempty"`
	DisplayUrl     string `json:"displayUrl,omitempty" bson:"display_url,omitempty"`

	CustomerID    int64  `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	AccountID     int64  `json:"accountID,omitempty" bson:"account_id,omitempty"`
	CustomerPhone string `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`

	UsedPoint int    `json:"usedPoint,omitempty" bson:"used_point,omitempty"`
	Message   string `json:"message,omitempty" bson:"message,omitempty"`

	Rewards *[]*struct {
		TypeReward            enum.LuckyWheelItemRewardType `json:"typeReward,omitempty" bson:"type_reward,omitempty"`
		RewardDescription     string                        `json:"rewardDescription,omitempty" bson:"reward_description,omitempty"`
		Points                int64                         `json:"points,omitempty" bson:"points,omitempty"`
		LkPoints              int64                         `json:"lkPoints,omitempty" bson:"lk_points,omitempty"`
		PromotionID           int64                         `json:"promotionID,omitempty" bson:"promotion_id,omitempty"`
		TicketPattern         string                        `json:"ticketPattern,omitempty" bson:"ticket_pattern,omitempty"`
		VoucherPattern        *string                       `json:"voucherPattern,omitempty" bson:"voucher_pattern,omitempty"`
		VoucherCode           string                        `json:"voucherCode,omitempty" bson:"-"`
		VoucherID             int64                         `json:"voucherId,omitempty" bson:"-"`
		TurnsRotation         int64                         `json:"turnsRotation,omitempty" bson:"turns_rotation,omitempty"`
		NumberOfDayUseVoucher *int64                        `json:"numberOfDayUseVoucher,omitempty" bson:"number_of_day_use_voucher,omitempty"`
	} `json:"rewards,omitempty" bson:"rewards,omitempty"`
}

// LuckyWheelStoreLogDB is var db
var LuckyWheelStoreLogDB = &db.Instance{
	ColName:        "lucky_wheel_store_log",
	TemplateObject: &LuckyWheelStoreLog{},
}

// InitLuckyWheelStoreLogModel is func init model
func InitLuckyWheelStoreLogModel(s *mongo.Database) {
	LuckyWheelStoreLogDB.ApplyDatabase(s)
}

// index for collection
// db.getCollection('lucky_wheel_store_log').createIndex({store_code: 1, lucky_wheel_code: 1}, {unique: true})
// db.getCollection('lucky_wheel_store_log').createIndex({customer_id: 1, store_code: 1, lucky_wheel_code: 1}, {unique: true})
