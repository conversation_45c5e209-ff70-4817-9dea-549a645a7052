package action

import (
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	MIN_ORDER_VALUE_PLACEHOLDER = "[MIN_ORDER_VALUE]"
	DISCOUNT_PLACEHOLDER        = "[DISCOUNT]"
)

// GetVoucherByID get voucher by id
func GetVoucherByID(voucherID int64) *common.APIResponse {
	if voucherID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "VOUCHER_ID_REQUIRED",
		}
	}
	return model.VoucherDB.QueryOne(bson.M{"voucher_id": voucherID})
}

// GetPromotionByCode func
func GetPromotionByCode(voucherCode string) *common.APIResponse {
	voucherCode = strings.TrimSpace(voucherCode)
	if voucherCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "VOUCHER_CODE_REQUIRED",
		}
	}

	voucherResult := model.VoucherDB.QueryOne(bson.M{
		"code": voucherCode,
		"status": bson.M{
			"$nin": []string{"DELETED"},
		},
	})
	if voucherResult.Status != common.APIStatus.Ok {
		return voucherResult
	}

	voucher := voucherResult.Data.([]*model.Voucher)[0]
	if voucher.PromotionID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin khuyến mãi.",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}

	promotionResult := model.PromotionDB.QueryOne(bson.M{
		"promotion_id": voucher.PromotionID,
		"status": bson.M{
			"$nin": []string{"DELETED", "HIDE"},
		},
	})

	if promotionResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin khuyến mãi.",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}

	promotionInfo := promotionResult.Data.([]*model.Promotion)[0]
	if promotionInfo.PromotionID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy thông tin khuyến mãi.",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}

	voucher.Promotion = promotionInfo
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Successfully",
		Data:    []*model.Voucher{voucher},
	}

}

// GetPromotionByCustomerID func
func GetPromotionByCustomerID(customerId int64) *common.APIResponse {
	if customerId == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "VOUCHER_ID_REQUIRED",
		}
	}
	return model.VoucherDB.Query(bson.M{"applied_customers": bson.M{"$in": []int64{customerId}}}, 0, 1000, nil)
}

// GetVouchers get vouchers
func GetVouchers(query *model.Voucher, offset int64, limit int64, getTotal bool, sortField string, sortType int, tabType string) *common.APIResponse {
	if len(tabType) > 0 {
		if tabType == "NOT_USED" {
			if query.CustomerID != nil {
				voucherCodes := make([]string, 0)
				if query.CustomerID != nil {
					offset2 := int64(0)
					limit2 := int64(1000)
					for {
						userPromotionResp := model.UserPromotionDB.Query(&bson.M{
							"customer_id": *query.CustomerID,
							"$or": []bson.M{
								{"amount": nil},
								{"amount": 0},
							},
							"status": bson.M{"$nin": []string{string(enum.CodeStatus.DELETED), string(enum.CodeStatus.INACTIVE), string(enum.CodeStatus.USED)}},
						}, offset2*limit2, limit2, nil)
						if userPromotionResp.Status != common.APIStatus.Ok {
							break
						}

						for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
							voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
						}
						offset2++
					}

				}

				if len(voucherCodes) == 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy mã khuyến mãi.",
						ErrorCode: "VOUCHER_NOT_FOUND",
					}
				}

				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"code": bson.M{
						"$in": voucherCodes,
					},
				})
			} else {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"is_used": false,
				})
			}
		} else if tabType == "USED" {
			if query.CustomerID != nil {
				voucherCodes := make([]string, 0)
				if query.CustomerID != nil {
					offset2 := int64(0)
					limit2 := int64(1000)
					for {
						userPromotionResp := model.UserPromotionDB.Query(&bson.M{
							"customer_id": *query.CustomerID,
							"amount": bson.M{
								"$gt": 0,
							},
						}, offset2*limit2, limit2, nil)
						if userPromotionResp.Status != common.APIStatus.Ok {
							break
						}

						for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
							voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
						}
						offset2++
					}
				}

				if len(voucherCodes) == 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy mã khuyến mãi.",
						ErrorCode: "VOUCHER_NOT_FOUND",
					}
				}

				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"code": bson.M{
						"$in": voucherCodes,
					},
				})

			} else {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"is_used": true,
				})
			}
		}
	} else {
		if query.CustomerID != nil {
			voucherCodes := make([]string, 0)
			if query.CustomerID != nil {
				offset2 := int64(0)
				limit2 := int64(1000)
				for {
					userPromotionResp := model.UserPromotionDB.Query(&bson.M{
						"customer_id": *query.CustomerID,
						"status":      bson.M{"$nin": []string{string(enum.CodeStatus.DELETED), string(enum.CodeStatus.INACTIVE)}},
					}, offset2*limit2, limit2, nil)
					if userPromotionResp.Status != common.APIStatus.Ok {
						break
					}

					for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
						voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
					}
					offset2++
				}

			}

			if len(voucherCodes) == 0 {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không tìm thấy mã khuyến mãi.",
					ErrorCode: "VOUCHER_NOT_FOUND",
				}
			}

			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"code": bson.M{
					"$in": voucherCodes,
				},
			})
		}
	}

	if query.ProductCode != "" {
		voucherResp := model.VoucherCacheReadDB.Query(bson.M{
			"ref_product": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", query.ProductCode), Options: ""},
		}, 0, 1000, nil)

		if voucherResp.Status != common.APIStatus.Ok {
			return voucherResp
		}

		voucherCodes := make([]string, 0)
		voucherRespData := voucherResp.Data.([]*model.Voucher)
		for _, voucherItem := range voucherRespData {
			voucherCodes = append(voucherCodes, voucherItem.Code)
		}

		if len(voucherCodes) > 0 {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"code": &bson.M{
					"$in": voucherCodes,
				},
			})
		}

	}

	result := model.VoucherDB.Query(
		query,
		offset,
		limit,
		&primitive.M{sortField: sortType})
	if getTotal {
		countResult := model.VoucherDB.Count(query)
		result.Total = countResult.Total
	}
	return result
}

/*
// getActiveVoucherOldVersion get active voucher
func getActiveVoucherOldVersion(account *model.Account) *common.APIResponse {
	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(account.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Tài khoản không xác định",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}

	conditions := bson.M{
		"status":         enum.PromotionStatus.ACTIVE,
		"promotion_type": enum.PromotionType.VOUCHERCODE,
	}

	conditions["$or"] = []bson.M{
		bson.M{
			"scopes.customer_level_codes": customerInfo.Level,
		},
		bson.M{
			"scopes.customer_level_codes": "ALL",
		},
		bson.M{
			"scopes.customer_level_codes": bson.M{
				"$exists": false,
			},
		},
	}

	queryResult := model.PromotionDB.Query(
		conditions,
		0,
		1000,
		nil,
	)
	if queryResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không có chương trình khuyến mãi nào.",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}
	promotionList := queryResult.Data.([]*model.Promotion)
	voucherList := []*model.Voucher{}

	for _, promotion := range promotionList {
		getVoucherResult := model.VoucherDB.Query(bson.M{
			"promotion_id": promotion.PromotionID,
			"type":         enum.VoucherType.PUBLIC,
			"status":       enum.VoucherStatus.ACTIVE,
			"end_time": bson.M{
				"$gt": time.Now(),
			},
			"public_time": bson.M{
				"$lt": time.Now(),
			},
			"$or": bson.A{
				bson.M{
					"applied_customers": bson.M{"$size": 0},
				},
				bson.M{
					"applied_customers": nil,
				},
				bson.M{
					"applied_customers": customerInfo.CustomerID,
				},
			},
		}, 0, 0, nil)

		if getVoucherResult.Status == common.APIStatus.Ok {
			vouchers := getVoucherResult.Data.([]*model.Voucher)
			for _, voucher := range vouchers {
				voucher.Promotion = promotion
				voucherList = append(voucherList, voucher)
			}
		}
	}

	if len(voucherList) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không có mã khuyến mãi nào.",
			ErrorCode: "VOUCHER_NOT_FOUND",
		}
	}

	resp := getDetailVouchers(voucherList, customerInfo.CustomerID)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Danh sách mã khuyến mãi.",
		Data:    resp,
	}
}
*/
//
//// GetActiveVoucher get active voucher
//func GetActiveVoucher(cart *model.Cart, account *model.Account) *common.APIResponse {
//	if cart == nil {
//		return getActiveVoucherOldVersion(account)
//	}
//	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(account.AccountID)
//	if err != nil {
//		return &common.APIResponse{
//			Status:    common.APIStatus.Forbidden,
//			Message:   "Tài khoản không xác định",
//			ErrorCode: "CUSTOMER_NOT_FOUND",
//		}
//	}
//
//	conditions := bson.M{
//		"status":         enum.PromotionStatus.ACTIVE,
//		"promotion_type": enum.PromotionType.VOUCHERCODE,
//	}
//
//	conditions["$or"] = []bson.M{
//		bson.M{
//			"scopes.customer_level_codes": customerInfo.Level,
//		},
//		bson.M{
//			"scopes.customer_level_codes": "ALL",
//		},
//		bson.M{
//			"scopes.customer_level_codes": bson.M{
//				"$exists": false,
//			},
//		},
//	}
//
//	queryResult := model.PromotionDB.Query(
//		conditions,
//		0,
//		1000,
//		nil,
//	)
//	if queryResult.Status != common.APIStatus.Ok {
//		return &common.APIResponse{
//			Status:    common.APIStatus.NotFound,
//			Message:   "Không có chương trình khuyến mãi nào.",
//			ErrorCode: "PROMOTION_NOT_FOUND",
//		}
//	}
//	promotionList := queryResult.Data.([]*model.Promotion)
//	voucherList := []*model.Voucher{}
//
//	promotionIDs := make([]int64, 0)
//	mapUserPromotion := make(map[string]*model.UserPromotion)
//	mapPromotion := make(map[int64]*model.Promotion)
//
//	regionCodes := make([]string, 0)
//	mapRegionCodes := make(map[string][]string)
//
//	for _, promotion := range promotionList {
//		mapPromotion[promotion.PromotionID] = promotion
//		promotionIDs = append(promotionIDs, promotion.PromotionID)
//		for _, scope := range promotion.Scopes {
//			regionCodes = append(regionCodes, scope.AreaCodes...)
//		}
//	}
//
//	for _, region := range regionCodes {
//		mapRegionCodes[region] = []string{region}
//	}
//
//	regionResp := client.LocationClient.GetRegionListByRegionCodes()
//	if regionResp.Status == common.APIStatus.Ok {
//		for _, region := range regionResp.Data {
//			if region.Scope == "SALE_REGION" && len(mapRegionCodes[region.Code]) > 0 {
//				mapRegionCodes[region.Code] = append(mapRegionCodes[region.Code], region.ProvinceCodes...)
//			}
//		}
//	}
//
//	cart.ScopeRegionCodes = mapRegionCodes
//	userPromotionsResult := model.UserPromotionDB.Query(bson.M{
//		"customer_id": cart.CustomerID,
//		"promotion_id": bson.M{
//			"$in": promotionIDs,
//		},
//	}, 0, 0, nil)
//
//	if userPromotionsResult.Status == common.APIStatus.Ok {
//		userPromotions := userPromotionsResult.Data.([]*model.UserPromotion)
//		for _, userPromo := range userPromotions {
//			mapUserPromotion[userPromo.VoucherCode] = userPromo
//		}
//	}
//
//	getVouchersResult := model.VoucherDB.Query(bson.M{
//		"promotion_id": bson.M{
//			"$in": promotionIDs,
//		},
//		"type":   enum.VoucherType.PUBLIC,
//		"status": enum.VoucherStatus.ACTIVE,
//		"end_time": bson.M{
//			"$gt": time.Now(),
//		},
//		"public_time": bson.M{
//			"$lt": time.Now(),
//		},
//		"$or": bson.A{
//			bson.M{
//				"applied_customers": bson.M{"$size": 0},
//			},
//			bson.M{
//				"applied_customers": nil,
//			},
//			bson.M{
//				"applied_customers": customerInfo.CustomerID,
//			},
//		},
//	}, 0, 0, nil)
//
//	if getVouchersResult.Status != common.APIStatus.Ok {
//		return &common.APIResponse{
//			Status:    common.APIStatus.NotFound,
//			Message:   "Không có chương trình khuyến mãi nào.",
//			ErrorCode: "VOUCHER_NOT_FOUND",
//		}
//	}
//
//	// Scan N voucher
//	vouchers := getVouchersResult.Data.([]*model.Voucher)
//	for idx, voucher := range vouchers {
//		if mapUserPromotion[voucher.Code] != nil && voucher.MaxUsagePerCustomer > 0 && voucher.MaxUsagePerCustomer <= mapUserPromotion[voucher.Code].Amount {
//			continue
//		}
//		voucher.Promotion = mapPromotion[voucher.PromotionID]
//		isMatch, canView, _, _, _ := getMatchingCondition(*voucher, *cart)
//		if !canView {
//			continue
//		}
//		vouchers[idx].CanUse = &isMatch
//		voucherList = append(voucherList, voucher)
//	}
//
//	if len(voucherList) == 0 {
//		return &common.APIResponse{
//			Status:    common.APIStatus.NotFound,
//			Message:   "Không có mã khuyến mãi nào.",
//			ErrorCode: "VOUCHER_NOT_FOUND",
//		}
//	}
//
//	resp := getDetailVouchers(voucherList, customerInfo.CustomerID)
//
//	return &common.APIResponse{
//		Status:  common.APIStatus.Ok,
//		Message: "Danh sách mã khuyến mãi.",
//		Data:    resp,
//	}
//}
//
//// SearchActiveVoucher search active voucher
//func SearchActiveVoucher(voucherCode string, account *model.Account) *common.APIResponse {
//	if !client.Services.Customer.IsOnline() {
//		return &common.APIResponse{
//			Status:    common.APIStatus.Error,
//			Message:   "Đăng ký tài khoản đang bảo trì, vui lòng thử lại sau",
//			ErrorCode: "CUSTOMER_UNVAILABLE",
//		}
//	}
//
//	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(account.AccountID)
//	if err != nil {
//		return &common.APIResponse{
//			Status:    common.APIStatus.Forbidden,
//			Message:   "Tài khoản không xác định",
//			ErrorCode: "CUSTOMER_NOT_FOUND",
//		}
//	}
//
//	getVoucherResult := model.VoucherDB.QueryOne(bson.M{
//		"code":   voucherCode,
//		"type":   enum.VoucherType.PUBLIC,
//		"status": enum.VoucherStatus.ACTIVE,
//		"end_time": bson.M{
//			"$gt": time.Now(),
//		},
//		"public_time": bson.M{
//			"$lt": time.Now(),
//		},
//	})
//
//	if getVoucherResult.Status != common.APIStatus.Ok {
//		return &common.APIResponse{
//			Status:    common.APIStatus.NotFound,
//			Message:   "Không tìm thấy mã khuyến mãi nào.",
//			ErrorCode: "VOUCHER_NOT_FOUND",
//		}
//	}
//
//	voucher := getVoucherResult.Data.([]*model.Voucher)[0]
//
//	conditions := bson.M{
//		"promotion_id":   voucher.PromotionID,
//		"status":         enum.PromotionStatus.ACTIVE,
//		"promotion_type": enum.PromotionType.VOUCHERCODE,
//	}
//
//	conditions["$or"] = []bson.M{
//		bson.M{
//			"scopes.customer_level_codes": customerInfo.Level,
//		},
//		bson.M{
//			"scopes.customer_level_codes": "ALL",
//		},
//		bson.M{
//			"scopes.customer_level_codes": bson.M{
//				"$exists": false,
//			},
//		},
//	}
//
//	queryResult := model.PromotionDB.QueryOne(conditions)
//	if queryResult.Status != common.APIStatus.Ok {
//		return &common.APIResponse{
//			Status:    common.APIStatus.NotFound,
//			Message:   "Không tìm thấy mã khuyến mãi nào.",
//			ErrorCode: "VOUCHER_NOT_FOUND",
//		}
//	}
//	promotion := queryResult.Data.([]*model.Promotion)[0]
//	voucher.Promotion = promotion
//	return &common.APIResponse{
//		Status:  common.APIStatus.Ok,
//		Message: "Danh sách mã khuyến mãi.",
//		Data:    []model.Voucher{*voucher},
//	}
//}

// CreateVoucher create a new voucher
func CreateVoucher(voucher *model.Voucher, account *model.Account) *common.APIResponse {
	// Validate code
	if voucher.Code == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "CODE_REQUIRED",
		}
	}

	// Validate status
	if voucher.Status != nil && !isVoucherStatusValid(*voucher.Status) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái mã khuyến mãi không hợp lệ.",
			ErrorCode: "STATUS_INVALID",
		}
	}

	// Validate startTime
	if voucher.StartTime == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thời gian bắt đầu không được để trống.",
			ErrorCode: "START_TIME_REQUIRE",
		}
	}

	// Validate endTime
	if voucher.EndTime == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thời gian kết thúc không được để trống.",
			ErrorCode: "END_TIME_REQUIRE",
		}
	}

	// Validate publicTime
	if voucher.PublicTime == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thời gian hiển thị không được để trống.",
			ErrorCode: "PUBLIC_TIME_REQUIRE",
		}
	}

	// Validate startTime
	if voucher.StartTime.Unix() > voucher.EndTime.Unix() {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc.",
			ErrorCode: "START_TIME_INVALID",
		}
	}

	// Validate publicTime
	if voucher.PublicTime.Unix() > voucher.StartTime.Unix() {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Thời gian hiển thị không được sau ngày bắt đầu khuyến mãi.",
			ErrorCode: "PUBLIC_TIME_INVALID",
		}
	}

	if voucher.IsReuseOnOrderCancel != nil && *voucher.IsReuseOnOrderCancel && (voucher.MaxUsagePerCustomer == nil || *voucher.MaxUsagePerCustomer == 0) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số lần sử dụng tối đa trên mỗi khách hàng không được để trống hoặc bằng 0 khi chọn điều kiện hoàn lại mã đơn.",
			ErrorCode: "MAX_USAGE_PER_CUSTOMER_REQUIRE_WHEN_REUSE_ON_ORDER_CANCEL",
		}
	}

	//if (voucher.AndConditions == nil || len(voucher.AndConditions) == 0) && (voucher.OrConditions == nil || len(voucher.OrConditions) == 0) {
	//	return &common.APIResponse{
	//		Status:    common.APIStatus.Invalid,
	//		Message:   "Cần ít nhất một điều kiện cho mã khuyến mãi.",
	//		ErrorCode: "CONDITION_REQUIRE",
	//	}
	//}

	getVoucherResult := model.VoucherDB.QueryOne(
		bson.M{
			"code": voucher.Code,
		},
	)

	// Validate Code
	if getVoucherResult.Status == common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi đã tồn tại.",
			ErrorCode: "CODE_INVALID",
		}
	}

	var promotion *model.Promotion

	getPromotionResult := model.PromotionDB.QueryOne(
		bson.M{
			"promotion_id": voucher.PromotionID,
		},
	)
	if getPromotionResult.Status == common.APIStatus.Ok {
		promotion = getPromotionResult.Data.([]*model.Promotion)[0]

	}

	if promotion == nil && voucher.VoucherGroupCode != nil {
		if *voucher.VoucherGroupCode == "HOANTIEN" {
			promotion = getPromotionByGroupCode(*voucher.VoucherGroupCode)
			if promotion != nil {
				voucher.PromotionID = promotion.PromotionID
			}
		}
	}

	if voucher.UsageType == nil {
		voucher.UsageType = &enum.UsageType.ALONE
	}

	if voucher.Status == nil {
		voucher.Status = &enum.VoucherStatus.ACTIVE
	}

	if voucher.DisplayName == "" && promotion != nil {
		voucher.PromotionName = promotion.PromotionName
		voucher.DisplayName = promotion.Description
	}

	if voucher.CustomerApplyType == "" {
		if voucher.AppliedCustomers != nil && len(*voucher.AppliedCustomers) > 0 {
			voucher.CustomerApplyType = enum.CustomerApplyType.MANY
		} else {
			voucher.CustomerApplyType = enum.CustomerApplyType.ALL
		}
	}
	if voucher.SettingType == nil {
		voucher.SettingType = &enum.SettingType.DEFAULT
	}
	if voucher.SettingType != nil && *voucher.SettingType == enum.SettingType.SEGMENT && voucher.SegmentationCode != "" {
		voucher.CustomerApplyType = enum.CustomerApplyType.MANY
	}

	voucher.VoucherID = model.GenId("VOUCHER_ID")
	temp := time.Now()
	voucher.CreatedBy = account.AccountID
	voucher.CreatedByUserName = &account.Username
	voucher.CreatedTime = &temp
	voucher.VersionNo = uuid.New().String()
	voucher.IsUsed = utils.ParseBoolToPointer(false)

	// set default value to voucher
	if promotion != nil {
		if len(voucher.OrConditions) == 0 || voucher.OrConditions == nil {
			voucher.OrConditions = promotion.OrConditions
		}

		if len(voucher.AndConditions) == 0 || voucher.AndConditions == nil {
			voucher.AndConditions = promotion.AndConditions
		}

		if len(voucher.Scopes) == 0 {
			voucher.Scopes = promotion.Scopes
		}

		if len(voucher.Scopes) > 0 {
			voucherPlatform := findVoucherPlatform(voucher.Scopes)
			promotionPlatform := findVoucherPlatform(promotion.Scopes)
			if voucherPlatform == nil && promotionPlatform != nil {
				voucher.Scopes = append(voucher.Scopes, *promotionPlatform)
			}
		}

		// Add default scopes if not present
		hasCustomerLevel := false
		hasCustomerScope := false

		if voucher.Scopes != nil {
			for _, scope := range voucher.Scopes {
				if scope.Type != nil {
					if *scope.Type == enum.ScopeType.CUSTOMER_LEVEL {
						hasCustomerLevel = true
					}
					if *scope.Type == enum.ScopeType.CUSTOMER_SCOPE {
						hasCustomerScope = true
					}
				}
			}
		}

		// Add default CUSTOMER_LEVEL scope if not present
		if !hasCustomerLevel {
			if voucher.Scopes == nil {
				voucher.Scopes = []model.Scope{}
			}
			voucher.Scopes = append(voucher.Scopes, model.Scope{
				Type:         &enum.ScopeType.CUSTOMER_LEVEL,
				QuantityType: &enum.QuantityType.ALL,
			})
		}

		// Add default CUSTOMER_SCOPE scope if not present
		if !hasCustomerScope {
			if voucher.Scopes == nil {
				voucher.Scopes = []model.Scope{}
			}
			voucher.Scopes = append(voucher.Scopes, model.Scope{
				Type:         &enum.ScopeType.CUSTOMER_SCOPE,
				QuantityType: &enum.QuantityType.ALL,
			})
		}

		if len(voucher.Rewards) == 0 && promotion.Rewards != nil && len(*promotion.Rewards) > 0 {
			voucher.Rewards = *promotion.Rewards
		}

		if (voucher.ConditionDescription == nil || len(*voucher.ConditionDescription) == 0) && promotion.ConditionDescription != nil {
			voucher.ConditionDescription = promotion.ConditionDescription
		}

		if voucher.PromotionOrganizer == nil && promotion.PromotionOrganizer != nil {
			voucher.PromotionOrganizer = promotion.PromotionOrganizer
		}

		if voucher.ChargeFee == nil && promotion.ChargeFee != nil {
			voucher.ChargeFee = promotion.ChargeFee
		}

		if voucher.VoucherImage == nil && promotion.VoucherImage != nil {
			voucher.VoucherImage = promotion.VoucherImage
		}

		if voucher.Tag == nil && promotion.Tag != nil {
			voucher.Tag = promotion.Tag
		}

		if voucher.ShortName == "" && promotion.ShortName != "" {
			voucher.ShortName = promotion.ShortName
		}

		if voucher.ApplyType == "" && promotion.ApplyType != "" {
			voucher.ApplyType = promotion.ApplyType
			voucher.MaxAutoApplyCount = promotion.MaxAutoApplyCount
			voucher.Priority = promotion.Priority
		}
		if voucher.MaxUsage == nil && promotion.MaxUsage != nil {
			voucher.MaxUsage = promotion.MaxUsage
		}
		if voucher.MaxUsagePerCustomer == nil && promotion.MaxUsagePerCustomer != nil {
			voucher.MaxUsagePerCustomer = promotion.MaxUsagePerCustomer
		}
		if voucher.VoucherType == nil && promotion.VoucherType != nil {
			voucher.VoucherType = promotion.VoucherType
		}

		if voucher.SellerCode == nil && promotion.SellerCode != nil {
			voucher.SellerCode = promotion.SellerCode
		}

		if voucher.SellerCodes == nil && promotion.SellerCodes != nil && len(*promotion.SellerCodes) > 0 {
			voucher.SellerCodes = promotion.SellerCodes
		}

		if voucher.SellerName == nil && promotion.SellerName != nil {
			voucher.SellerName = promotion.SellerName
		}

		if voucher.ApplyDiscount == nil && promotion.ApplyDiscount != nil && len(*promotion.ApplyDiscount.NotInSkus) > 0 {
			voucher.ApplyDiscount = promotion.ApplyDiscount
		}

		if voucher.SellerCode == nil && promotion.SellerCode != nil {
			voucher.SellerCode = promotion.SellerCode
		}

		if voucher.SellerCodes == nil && promotion.SellerCodes != nil && len(*promotion.SellerCodes) > 0 {
			voucher.SellerCodes = promotion.SellerCodes
		}

		if voucher.LinkToPage == nil && promotion.LinkToPage != nil {
			voucher.LinkToPage = promotion.LinkToPage
		}

		if voucher.LinkToStore == nil && promotion.LinkToStore != nil {
			voucher.LinkToStore = promotion.LinkToStore
		}

		if voucher.SystemDisplay == "" && promotion.SystemDisplay != "" {
			voucher.SystemDisplay = promotion.SystemDisplay
		}

		if voucher.StoreCode == nil && promotion.StoreCode != nil {
			voucher.StoreCode = promotion.StoreCode
		}

		if voucher.StoreName == nil && promotion.StoreName != nil {
			voucher.StoreName = promotion.StoreName
		}

		if voucher.VoucherGroupCode == nil && promotion.VoucherGroupCode != nil {
			voucher.VoucherGroupCode = promotion.VoucherGroupCode
		}

		if promotion.IsReuseOnOrderCancel != nil && *promotion.IsReuseOnOrderCancel {
			voucher.IsReuseOnOrderCancel = utils.ParseBoolToPointer(true)
		}
		if promotion.VoucherReuseDuration != nil {
			voucher.VoucherReuseDuration = promotion.VoucherReuseDuration
		}
		if voucher.IsReuseOnOrderCancel == nil {
			voucher.IsReuseOnOrderCancel = utils.ParseBoolToPointer(false)
		}

		if voucher.SkipCheckVoucherGroup == nil && promotion.SkipCheckVoucherGroup != nil {
			voucher.SkipCheckVoucherGroup = promotion.SkipCheckVoucherGroup
		}
	}

	// Add default scopes if not present (for vouchers without promotion)
	if promotion == nil {
		hasCustomerLevel := false
		hasCustomerScope := false

		if voucher.Scopes != nil {
			for _, scope := range voucher.Scopes {
				if scope.Type != nil {
					if *scope.Type == enum.ScopeType.CUSTOMER_LEVEL {
						hasCustomerLevel = true
					}
					if *scope.Type == enum.ScopeType.CUSTOMER_SCOPE {
						hasCustomerScope = true
					}
				}
			}
		}

		// Add default CUSTOMER_LEVEL scope if not present
		if !hasCustomerLevel {
			if voucher.Scopes == nil {
				voucher.Scopes = []model.Scope{}
			}
			voucher.Scopes = append(voucher.Scopes, model.Scope{
				Type:         &enum.ScopeType.CUSTOMER_LEVEL,
				QuantityType: &enum.QuantityType.ALL,
			})
		}

		// Add default CUSTOMER_SCOPE scope if not present
		if !hasCustomerScope {
			if voucher.Scopes == nil {
				voucher.Scopes = []model.Scope{}
			}
			voucher.Scopes = append(voucher.Scopes, model.Scope{
				Type:         &enum.ScopeType.CUSTOMER_SCOPE,
				QuantityType: &enum.QuantityType.ALL,
			})
		}
	}

	if voucher.SystemDisplay == "" {
		voucher.SystemDisplay = "BUYMED"
	}

	if voucher.DisplayName == "" {
		voucher.DisplayName = voucher.Code
	}

	if voucher.PromotionOrganizer != nil && *voucher.PromotionOrganizer == enum.PromotionOrganizer.SELLER_CENTER {
		voucher.ApplyType = "AUTO"
	}

	// TODO: force isSpecific = true for internal seller and seller center
	if voucher.PromotionOrganizer != nil && *voucher.PromotionOrganizer == enum.PromotionOrganizer.SELLER_CENTER {
		voucher.IsSpecific = utils.ParseBoolToPointer(true)
	}

	voucher = formatVoucherDisplayName(voucher)
	normCodeStr := strings.Replace(utils.NormalizeString(voucher.Code), " ", "-", -1)
	normDisplayNameStr := strings.Replace(utils.NormalizeString(voucher.DisplayName), " ", "-", -1)
	normShortNameStr := strings.Replace(utils.NormalizeString(voucher.ShortName), " ", "-", -1)
	voucher.HashTag = fmt.Sprintf("%d-%s-%s-%s", voucher.PromotionID, normCodeStr, normDisplayNameStr, normShortNameStr)

	createResult := model.VoucherDB.Create(voucher)

	if createResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Tạo mã khuyến mãi không thành công!",
			ErrorCode: "SERVER_ERROR",
		}
	}

	if voucher.AppliedCustomers != nil {
		if len(*voucher.AppliedCustomers) <= 20 {
			CreateUserVoucher(account, &model.UserPromotion{
				VoucherCode: voucher.Code,
				Status:      &enum.CodeStatus.ACTIVE,
				CustomerIDs: *voucher.AppliedCustomers,
			})
		} else {
			for _, customerID := range *voucher.AppliedCustomers {
				errPushJob := model.UserForVoucherJob.Push(&model.UserVoucherRequest{
					CustomerID:  customerID,
					VoucherCode: voucher.Code,
					Status:      &enum.CodeStatus.ACTIVE,
					AccountID:   account.AccountID,
					TypeAction:  "CREATE",
				}, &job.JobItemMetadata{
					Topic: "default",
					Keys:  []string{"USER_VOUCHER_CREATE", voucher.Code, fmt.Sprintf("%d", customerID)},
				})

				if errPushJob != nil {
					fmt.Println("Error push job create user voucher, ", errPushJob.Error())
				}
			}
		}
	}
	WarmUpVoucherByCode(voucher.Code, voucher, "")

	// send to personalization
	if voucher.SettingType != nil && *voucher.SettingType == enum.SettingType.SEGMENT && voucher.SegmentationCode != "" {
		req := model.SegmentationVoucher{
			VoucherCode:      voucher.Code,
			SegmentationCode: voucher.SegmentationCode,
			Status:           "ACTIVE",
		}

		client.Services.PersonalizationRule.CreateSegmentationVoucher(&req)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo mã khuyến mãi thành công",
		Data:    createResult.Data.([]*model.Voucher),
	}
}

func CreateVoucherByPromotionID(account *model.Account, customerID, promotionID int64, voucherCode string, voucherID int64, nDayUseVoucher *int64) *common.APIResponse {
	qPromotion := model.PromotionDB.QueryOne(model.Promotion{
		PromotionID: promotionID,
	})
	if qPromotion.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không tìm thấy chương trình khuyến mãi",
			ErrorCode: "PROMOTION_NOT_FOUND",
		}
	}

	promotion := qPromotion.Data.([]*model.Promotion)[0]
	voucher := model.Voucher{
		DisplayName:           promotion.Description,
		VoucherID:             0,
		Code:                  "",
		PromotionID:           promotion.PromotionID,
		PromotionName:         promotion.PromotionName,
		ApplyType:             promotion.ApplyType,
		StartTime:             promotion.StartTime,
		EndTime:               promotion.EndTime,
		PublicTime:            promotion.PublicTime,
		MaxUsage:              utils.ParseInt64ToPointer(1),
		MaxUsagePerCustomer:   utils.ParseInt64ToPointer(1),
		MaxAutoApplyCount:     promotion.MaxAutoApplyCount,
		Priority:              promotion.Priority,
		VoucherType:           &enum.VoucherType.PUBLIC,
		VoucherGroupCode:      promotion.VoucherGroupCode,
		Status:                &enum.VoucherStatus.ACTIVE,
		Scopes:                promotion.Scopes,
		OrConditions:          promotion.OrConditions,
		AndConditions:         promotion.AndConditions,
		ConditionDescription:  promotion.ConditionDescription,
		CustomerApplyType:     "MANY",
		SystemNote:            "Create by promotion",
		SystemDisplay:         "BUYMED",
		PromotionOrganizer:    promotion.PromotionOrganizer,
		ChargeFee:             promotion.ChargeFee,
		ShortName:             promotion.ShortName,
		VoucherImage:          promotion.VoucherImage,
		Tag:                   promotion.Tag,
		SellerCode:            promotion.SellerCode,
		SellerCodes:           promotion.SellerCodes,
		SellerName:            promotion.SellerName,
		ApplyDiscount:         promotion.ApplyDiscount,
		LinkToPage:            promotion.LinkToPage,
		LinkToStore:           promotion.LinkToStore,
		StoreCode:             promotion.StoreCode,
		StoreName:             promotion.StoreName,
		IsUsed:                utils.ParseBoolToPointer(false),
		SkipCheckVoucherGroup: promotion.SkipCheckVoucherGroup,
	}

	if promotion.SystemDisplay != "" {
		voucher.SystemDisplay = promotion.SystemDisplay
	}

	if promotion.MaxUsage != nil {
		voucher.MaxUsage = promotion.MaxUsage
	}
	if promotion.MaxUsagePerCustomer != nil {
		voucher.MaxUsagePerCustomer = promotion.MaxUsagePerCustomer
	}
	if promotion.VoucherType != nil {
		voucher.VoucherType = promotion.VoucherType
	}
	if promotion.IsReuseOnOrderCancel != nil && *promotion.IsReuseOnOrderCancel {
		voucher.IsReuseOnOrderCancel = utils.ParseBoolToPointer(true)
	}
	if promotion.VoucherReuseDuration != nil {
		voucher.VoucherReuseDuration = promotion.VoucherReuseDuration
	}
	if nDayUseVoucher != nil && *nDayUseVoucher > 0 {
		// reset end time & start time
		now := time.Now()
		// if now is before start time, do not reset start time
		if now.After(*voucher.StartTime) {
			if voucher.StartTime == nil || now.After(*voucher.StartTime) {
				if voucher.StartTime == nil || now.After(*voucher.StartTime) {
					voucher.StartTime = &now
				}
			}
		}
		newEndTime := voucher.StartTime.AddDate(0, 0, int(*nDayUseVoucher))
		voucher.EndTime = &newEndTime
	}
	normCodeStr := strings.Replace(utils.NormalizeString(voucher.Code), " ", "-", -1)
	normDisplayNameStr := strings.Replace(utils.NormalizeString(voucher.DisplayName), " ", "-", -1)
	normShortNameStr := strings.Replace(utils.NormalizeString(voucher.ShortName), " ", "-", -1)
	voucher.HashTag = fmt.Sprintf("%d-%s-%s-%s", voucher.PromotionID, normCodeStr, normDisplayNameStr, normShortNameStr)

	if voucherCode != "" && voucherID != 0 {
		voucher.VoucherID, voucher.Code = voucherID, voucherCode
	} else {
		voucher.VoucherID, voucher.Code = model.GenVoucherID()
	}
	if promotion.Rewards != nil {
		voucher.Rewards = *promotion.Rewards
	}
	voucher.HashTag = fmt.Sprintf("%s-%d-%s", utils.NormalizeString(voucher.Code), promotion.PromotionID, utils.NormalizeString(promotion.Description))
	res := model.VoucherDB.Create(&voucher)
	if res.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Tạo mã khuyến mãi không thành công!",
			ErrorCode: "SERVER_ERROR",
		}
	}
	resUserVoucher := model.UserPromotionDB.Create(&model.UserPromotion{
		CustomerID:  customerID,
		PromotionID: voucher.PromotionID,
		VoucherCode: voucher.Code,
		Status:      &enum.CodeStatus.ACTIVE,
	})
	if resUserVoucher.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Tạo mã khuyến mãi không thành công!",
			ErrorCode: "SERVER_ERROR",
		}
	}
	go func() {
		WarmUpVoucherByCode(voucher.Code, res.Data.([]*model.Voucher)[0], "")
		WarmUpUserPromotion(voucher.Code, customerID, nil)
	}()
	return res
}

func formatVoucherDisplayName(v *model.Voucher) *model.Voucher {
	if v.DisplayName == "" {
		return v
	}

	_, ok := v.AndConditions[enum.ConditionType.ORDER_VALUE]

	if ok && len(v.AndConditions[enum.ConditionType.ORDER_VALUE].OrConditions) > 0 {
		totalMinPrice := v.AndConditions[enum.ConditionType.ORDER_VALUE].OrConditions[0].OrderConditionField.MinTotalPrice
		if totalMinPrice != nil {
			v.DisplayName = strings.Replace(
				v.DisplayName,
				MIN_ORDER_VALUE_PLACEHOLDER,
				utils.FormatVNDCurrency(strconv.Itoa(int(*totalMinPrice))),
				-1,
			)
		}
	}

	if len(v.Rewards) > 0 && v.Rewards[0].AbsoluteDiscount > 0 {
		v.DisplayName = strings.Replace(
			v.DisplayName,
			DISCOUNT_PLACEHOLDER,
			utils.FormatVNDCurrency(strconv.Itoa(int(v.Rewards[0].AbsoluteDiscount))),
			-1,
		)
	}

	return v
}

func findVoucherPlatform(scopes []model.Scope) *model.Scope {
	for _, scope := range scopes {
		if scope.Type != nil && *scope.Type == enum.ScopeType.DISPLAY_PLATFORM {
			return &scope
		}
	}
	return nil
}

// RefundVoucher is func to refund voucher
func RefundVoucher(req *model.PromotionRefundRequest) *common.APIResponse {
	if req.ApplyVoucherCount == nil {
		req.ApplyVoucherCount = make(map[string]int)
	}
	voucherCodes := req.VoucherCodes
	accountId := req.AccountID
	if len(voucherCodes) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "VOUCHER_ID_REQUIRED",
		}
	}

	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(accountId)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Tài khoản không xác định",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}
	queryVoucher := model.Voucher{
		ComplexQuery: []*bson.M{
			{
				"code": bson.M{"$in": voucherCodes},
			},
		},
	}
	qVoucher := model.VoucherDB.Query(queryVoucher, 0, 0, nil)
	if qVoucher.Status != common.APIStatus.Ok {
		return qVoucher
	}
	vouchers := qVoucher.Data.([]*model.Voucher)
	for _, voucher := range vouchers {
		qUsed := model.UserPromotionDB.QueryOne(model.UserPromotion{CustomerID: customerInfo.CustomerID, VoucherCode: voucher.Code})
		if qUsed.Status != common.APIStatus.Ok {
			return qUsed
		}
		useValue := qUsed.Data.([]*model.UserPromotion)[0]
		newOrderIds := make([]int64, 0)
		newStatus := enum.CodeStatus.USED
		newGiftCount := make(map[string]int)
		if useValue.GiftCount != nil {
			for key, value := range useValue.GiftCount {
				newGiftCount[key] = value
			}
		}
		//if voucher.CustomerApplyType == enum.CustomerApplyType.ALL && useValue.Amount != nil && *useValue.Amount == 1 {
		//	qResult := model.UserPromotionDB.Delete(model.UserPromotion{CustomerID: customerInfo.CustomerID, VoucherCode: voucher.Code})
		//	if qResult.Status != common.APIStatus.Ok {
		//		return qResult
		//	}
		//	model.UserPromotionCacheDB.Delete(model.UserPromotion{CustomerID: customerInfo.CustomerID, VoucherCode: voucher.Code})
		//} else {
		if useValue.GiftCount != nil {
			gifts := make([]model.Gift, 0)
			if voucher.Rewards != nil && len(voucher.Rewards) > 0 {
				reward := voucher.Rewards[0]
				if reward.Type != nil && *reward.Type == enum.RewardType.GIFT {
					gifts = reward.Gifts
				}
				for _, gift := range gifts {
					// decrease gift count
					newGiftCount[gift.Sku] = newGiftCount[gift.Sku] - int(gift.Quantity)*req.ApplyVoucherCount[voucher.Code]
				}
			}
		}
		if useValue.OrderIDs != nil {
			for _, id := range *useValue.OrderIDs {
				if id != req.OrderID {
					newOrderIds = append(newOrderIds, id)
				}
			}
		}

		if useValue.Amount != nil && *useValue.Amount == 1 {
			newStatus = enum.CodeStatus.ACTIVE
		}
		qResult := model.UserPromotionDB.UpdateOneWithOption(bson.M{
			"customer_id":  customerInfo.CustomerID,
			"voucher_code": voucher.Code,
		}, bson.M{
			"$inc": bson.M{"amount": -1, "total_reward_count": -req.ApplyVoucherCount[voucher.Code]},
			"$set": bson.M{"order_ids": newOrderIds, "status": newStatus, "gift_count": newGiftCount},
		})
		if qResult.Status != common.APIStatus.Ok {
			return qResult
		}
		//}
		// update voucher status
		model.VoucherDB.IncreOne(&model.Voucher{Code: voucher.Code},
			"usage_total", -1,
		)
		go func(voucher *model.Voucher) {
			if voucher.Status != nil && *voucher.Status == enum.VoucherStatus.HIDE &&
				voucher.MaxUsage != nil && *voucher.MaxUsage > 0 && voucher.UsageTotal != nil && (*voucher.UsageTotal-1) < *voucher.MaxUsage {
				updater := model.Voucher{NeedCheck: utils.ParseBoolToPointer(true)}
				if time.Now().Unix() <= voucher.EndTime.Unix() {
					updater.Status = &enum.VoucherStatus.ACTIVE
				}
				model.VoucherDB.UpdateOne(model.Voucher{Code: voucher.Code}, updater)
			}
			if voucher.UsageTotal != nil && *voucher.UsageTotal-1 == 0 {
				model.VoucherDB.UpdateOne(model.Voucher{Code: voucher.Code}, model.Voucher{IsUsed: utils.ParseBoolToPointer(false)})
			}
			WarmUpVoucherByCode(voucher.Code, nil, "")
			WarmUpUserPromotion(voucher.Code, customerInfo.CustomerID, nil)
			model.VoucherHistoryDB.Delete(bson.M{"customer_id": customerInfo.CustomerID, "voucher.code": voucher.Code, "order_id": req.OrderID})
			//createVoucherHistory(&model.VoucherHistory{
			//	CustomerID: customerInfo.CustomerID,
			//	Usage:      1,
			//	OrderID:    req.OrderID,
			//	Type:       enum.VoucherHistoryType.REFUND,
			//}, voucher.PromotionID, customerInfo.CustomerID, voucher.Code)
		}(voucher)

	}
	return qVoucher
}

// UpdateVoucher update a voucher
func UpdateVoucher(voucher *model.UpdateVoucherRequest, accountID int64) *common.APIResponse {
	if voucher.Code == "" && voucher.VoucherID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "VOUCHER_ID_REQUIRED",
		}
	}

	if voucher.IsReuseOnOrderCancel != nil && *voucher.IsReuseOnOrderCancel && (voucher.MaxUsagePerCustomer == nil || *voucher.MaxUsagePerCustomer == 0) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số lần sử dụng tối đa trên mỗi khách hàng không được để trống hoặc bằng 0 khi chọn điều kiện hoàn lại mã đơn.",
			ErrorCode: "MAX_USAGE_PER_CUSTOMER_REQUIRE_WHEN_REUSE_ON_ORDER_CANCEL",
		}
	}

	getVoucherResult := model.VoucherDB.QueryOne(
		model.Voucher{
			Code:      voucher.Code,
			VoucherID: voucher.VoucherID,
		},
	)

	// Validate Code
	if getVoucherResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không tồn tại.",
			ErrorCode: "CODE_INVALID",
		}
	}

	existVoucher := getVoucherResult.Data.([]*model.Voucher)[0]
	if existVoucher.SettingType == nil {
		existVoucher.SettingType = &enum.SettingType.DEFAULT
	}

	voucher.VersionNo = uuid.New().String()
	if voucher.UsageType == nil || *voucher.UsageType == "" {
		voucher.UsageType = &enum.UsageType.ALONE
	}
	if voucher.DisplayName != "" {
		existVoucher.DisplayName = voucher.DisplayName
	}
	if voucher.ShortName != "" {
		existVoucher.ShortName = voucher.ShortName
	}
	normCodeStr := strings.Replace(utils.NormalizeString(existVoucher.Code), " ", "-", -1)
	normPromotionNameStr := strings.Replace(utils.NormalizeString(existVoucher.DisplayName), " ", "-", -1)
	normShortNameStr := strings.Replace(utils.NormalizeString(existVoucher.ShortName), " ", "-", -1)
	voucher.HashTag = fmt.Sprintf("%d-%s-%s-%s", existVoucher.PromotionID, normCodeStr, normPromotionNameStr, normShortNameStr)

	afterOption := options.After

	// Use existing conditions if not provided in the update request
	if voucher.OrConditions == nil {
		voucher.OrConditions = existVoucher.OrConditions
	}
	if voucher.AndConditions == nil {
		voucher.AndConditions = existVoucher.AndConditions
	}
	if voucher.IsReuseOnOrderCancel == nil {
		voucher.IsReuseOnOrderCancel = existVoucher.IsReuseOnOrderCancel
	}
	if voucher.SettingType == nil {
		voucher.SettingType = &enum.SettingType.DEFAULT
	} else {
		if *voucher.SettingType == enum.SettingType.SEGMENT && voucher.SegmentationCode != "" {
			voucher.CustomerApplyType = enum.CustomerApplyType.MANY
		}
	}

	// Handle scopes - use existing scopes if not provided, then add defaults if missing
	if voucher.Scopes == nil {
		voucher.Scopes = existVoucher.Scopes
	}

	// Add default scopes if not present
	hasCustomerLevel := false
	hasCustomerScope := false

	if voucher.Scopes != nil {
		for _, scope := range voucher.Scopes {
			if scope.Type != nil {
				if *scope.Type == enum.ScopeType.CUSTOMER_LEVEL {
					hasCustomerLevel = true
				}
				if *scope.Type == enum.ScopeType.CUSTOMER_SCOPE {
					hasCustomerScope = true
				}
			}
		}
	}

	// Add default CUSTOMER_LEVEL scope if not present
	if !hasCustomerLevel {
		if voucher.Scopes == nil {
			voucher.Scopes = []model.Scope{}
		}
		voucher.Scopes = append(voucher.Scopes, model.Scope{
			Type:         &enum.ScopeType.CUSTOMER_LEVEL,
			QuantityType: &enum.QuantityType.ALL,
		})
	}

	// Add default CUSTOMER_SCOPE scope if not present
	if !hasCustomerScope {
		if voucher.Scopes == nil {
			voucher.Scopes = []model.Scope{}
		}
		voucher.Scopes = append(voucher.Scopes, model.Scope{
			Type:         &enum.ScopeType.CUSTOMER_SCOPE,
			QuantityType: &enum.QuantityType.ALL,
		})
	}

	updateResult := model.VoucherDB.UpdateOne(
		model.Voucher{
			Code:      voucher.Code,
			VoucherID: voucher.VoucherID,
		},
		voucher,
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		},
	)

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Cập nhật mã khuyến mãi không thành công!",
			ErrorCode: "SERVER_ERROR",
		}
	}
	go func() {
		WarmUpVoucherByCode(updateResult.Data.([]*model.Voucher)[0].Code, updateResult.Data.([]*model.Voucher)[0], "")
		WarmUpUserPromotion(updateResult.Data.([]*model.Voucher)[0].Code, 0, nil)
	}()

	if voucher.AppliedCustomers != nil {
		if len(*voucher.AppliedCustomers) <= 20 {
			CreateUserVoucher(&model.Account{
				AccountID: accountID,
			}, &model.UserPromotion{
				VoucherCode: voucher.Code,
				Status:      &enum.CodeStatus.ACTIVE,
				CustomerIDs: *voucher.AppliedCustomers,
			})
		} else {
			for _, customerID := range *voucher.AppliedCustomers {
				errPushJob := model.UserForVoucherJob.Push(&model.UserVoucherRequest{
					CustomerID:  customerID,
					VoucherCode: voucher.Code,
					Status:      &enum.CodeStatus.ACTIVE,
					AccountID:   accountID,
					TypeAction:  "CREATE",
				}, &job.JobItemMetadata{
					Topic: "default",
					Keys:  []string{"USER_VOUCHER_CREATE", voucher.Code, fmt.Sprintf("%d", customerID)},
				})

				if errPushJob != nil {
					fmt.Println("Error push job create user voucher, ", errPushJob.Error())
				}
			}
		}

		WarmUpVoucherByCode(voucher.Code, &model.Voucher{
			Code:             voucher.Code,
			DisplayName:      voucher.DisplayName,
			ShortName:        voucher.ShortName,
			Scopes:           voucher.Scopes,
			Status:           voucher.Status,
			AppliedCustomers: voucher.AppliedCustomers,
		}, "")
	}

	voucherResp := updateResult.Data.([]*model.Voucher)[0]
	req := model.SegmentationVoucher{
		VoucherCode:      voucherResp.Code,
		SegmentationCode: voucherResp.SegmentationCode,
	}
	if voucher.SettingType != nil && *voucher.SettingType == enum.SettingType.SEGMENT {
		req.Status = "ACTIVE"
		if existVoucher.SegmentationCode == "" && voucher.SegmentationCode != "" {
			client.Services.PersonalizationRule.CreateSegmentationVoucher(&req)
		} else {
			client.Services.PersonalizationRule.UpdateStatusSegmentationVoucher(&req)
		}
	} else {
		req.Status = "INACTIVE"
		client.Services.PersonalizationRule.UpdateStatusSegmentationVoucher(&req)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật mã khuyến mãi thành công",
		Data:    updateResult.Data.([]*model.Voucher),
	}
}

// GetVouchersByCodes is func to get list vouchers by list codes
func GetVouchersByCodes(listCodes []string) *common.APIResponse {
	return model.VoucherDB.Query(bson.M{
		"code": bson.M{
			"$in": listCodes,
		},
		"status": bson.M{
			"$nin": []string{"DELETED"},
		},
	}, 0, int64(len(listCodes)), nil)
}

// UpdateVoucherStatus update a voucher status
func UpdateVoucherStatus(input *model.Voucher, AccountID int64) *common.APIResponse {
	if input.Code == "" && input.VoucherID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không được để trống.",
			ErrorCode: "VOUCHER_ID_REQUIRED",
		}
	}

	if input.Status == nil || !isVoucherStatusValid(*input.Status) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái không hợp lệ.",
			ErrorCode: "STATUS_INVALID",
		}
	}

	// Get Voucher
	voucherResult := model.VoucherDB.QueryOne(
		model.Voucher{
			Code:      input.Code,
			VoucherID: input.VoucherID,
		},
	)

	if voucherResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không tồn tại.",
			ErrorCode: "INVALID_VOUCHER_ID_INPUT",
		}
	}

	existedVoucher := voucherResult.Data.([]*model.Voucher)[0]

	// Check status hiên tại của VOUCHER có được cập nhật không ?
	canUpdate := false
	switch *existedVoucher.Status {
	case enum.VoucherStatus.HIDE:
		canUpdate = true
		break
	case enum.VoucherStatus.WAITING:
		canUpdate = true
		break
	case enum.VoucherStatus.ACTIVE:
		canUpdate = true
		break
	}

	if !canUpdate {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi đã hết hạn. Không cho phép cập nhật trạng thái. Xin cảm ơn.",
			ErrorCode: "INVALID_VOUCHER_STATUS",
		}
	}

	now := time.Now()
	existedVoucher.UpdatedBy = AccountID
	existedVoucher.LastUpdatedTime = &now
	existedVoucher.VersionNo = uuid.New().String()
	existedVoucher.Status = input.Status

	afterOption := options.After
	updateResult := model.VoucherDB.UpdateOne(
		model.Voucher{
			Code:      input.Code,
			VoucherID: input.VoucherID,
		},
		existedVoucher,
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		},
	)

	if updateResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Cập nhật trạng thái mã khuyến mãi thất bại.",
		}
	}
	go func() { WarmUpVoucherByCode(existedVoucher.Code, nil, "") }()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật trạng thái mã khuyến mãi thành công.",
		Data:    updateResult.Data.([]*model.Voucher),
	}
}

func isVoucherTypeValid(voucherType enum.VoucherTypeValue) bool {
	switch voucherType {
	case enum.VoucherType.PRIVATE:
		return true
	case enum.VoucherType.PUBLIC:
		return true
	default:
		return false
	}
}

func isVoucherStatusValid(voucherStatus enum.VoucherStatusValue) bool {
	switch voucherStatus {
	case enum.VoucherStatus.HIDE:
		return true
	case enum.VoucherStatus.ACTIVE:
		return true
	case enum.VoucherStatus.WAITING:
		return true
	case enum.VoucherStatus.DELETED:
		return true
	case enum.VoucherStatus.EXPIRED:
		return true
	default:
		return false
	}
}

func GetCustomerVouchers(accountId, offset, limit int64, getTotal bool, systemDisplay string) *common.APIResponse {
	customerInfo, err := client.Services.Customer.GetCustomerByAccountID(accountId)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Tài khoản không xác định",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}

	voucherCodes := make([]string, 0)
	mapUserPromotion := make(map[string]*model.UserPromotion, 0)
	offset2 := int64(0)
	limit2 := int64(1000)
	for {
		userPromotionResp := model.UserPromotionDB.Query(&bson.M{
			"customer_id": customerInfo.CustomerID,
			"status":      bson.M{"$nin": []string{string(enum.CodeStatus.DELETED), string(enum.CodeStatus.INACTIVE)}},
		}, offset2*limit2, limit2, nil)
		if userPromotionResp.Status != common.APIStatus.Ok {
			break
		}

		for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
			voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
			mapUserPromotion[userPromotion.VoucherCode] = userPromotion
		}
		offset2++
	}

	if len(voucherCodes) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Không có mã khuyến mãi.",
			ErrorCode: "VOUCHER_NOT_FOUND",
		}
	}

	if systemDisplay == "" {
		systemDisplay = "BUYMED"
	} else if systemDisplay == "ALL" {
		systemDisplay = ""
	}

	query := model.Voucher{
		SystemDisplay:     systemDisplay,
		Status:            &enum.VoucherStatus.ACTIVE,
		CustomerApplyType: enum.CustomerApplyType.MANY,
	}

	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"$and": []*bson.M{
			{
				"code": bson.M{
					"$in": voucherCodes,
				},
				"apply_type": bson.M{
					"$nin": []string{string(enum.ApplyType.AUTO)},
				},
			},
		},
	})

	qResult := model.VoucherDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if qResult.Status != common.APIStatus.Ok {
		return qResult
	}
	voucherList := qResult.Data.([]*model.Voucher)

	resp := make([]*model.ViewVoucherResponse, 0)

	for _, voucher := range voucherList {
		maxUsagePerCustomer := *voucher.MaxUsagePerCustomer
		availableQuantity := *voucher.MaxUsage
		customerUsageTotal := mapUserPromotion[voucher.Code].Amount
		isUnlimited := false

		if (voucher.MaxUsage == nil || (voucher.MaxUsage != nil && *voucher.MaxUsage == 0)) && (voucher.MaxUsagePerCustomer == nil || (voucher.MaxUsagePerCustomer != nil && *voucher.MaxUsagePerCustomer == 0)) {
			isUnlimited = true
		} else {
			if voucher.UsageTotal != nil && voucher.MaxUsage != nil && *voucher.MaxUsage > 0 {
				availableQuantity = availableQuantity - *voucher.UsageTotal
				if customerUsageTotal != nil && *customerUsageTotal > 0 {
					availableQuantity = availableQuantity + *customerUsageTotal
				}
			}

			if maxUsagePerCustomer > 0 && (availableQuantity > maxUsagePerCustomer || availableQuantity == 0) {
				availableQuantity = maxUsagePerCustomer
			}
		}

		if !isUnlimited && customerUsageTotal != nil && availableQuantity <= *customerUsageTotal {
			continue
		}

		viewVoucher := setViewData(voucher, nil)
		resp = append(resp, &model.ViewVoucherResponse{
			Voucher:            viewVoucher,
			UserPromotion:      mapUserPromotion[voucher.Code],
			AvailableQuantity:  &availableQuantity,
			CustomerUsageTotal: customerUsageTotal,
			IsUnlimited:        isUnlimited,
		})
	}
	if getTotal {
		total := model.VoucherDB.Count(&query).Total
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Data:    resp,
			Total:   total,
			Message: "Query voucher list of customer successfully",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    resp,
		Message: "Query voucher list of customer successfully",
	}
}

func updateUsageVoucher(usedVoucher *model.UserPromotion, customerId int64, voucherCode string) *common.APIResponse {
	// update usage in user promotion

	qResult := model.UserPromotionDB.UpdateOneWithOption(bson.M{
		"customer_id":  customerId,
		"voucher_code": voucherCode,
	}, bson.M{
		"$inc": bson.M{"amount": -1},
	})

	if qResult.Status == common.APIStatus.Ok {
		// update usage total in voucher code
		qResult := model.VoucherDB.QueryOne(&model.Voucher{Code: voucherCode})
		if qResult.Status == common.APIStatus.Ok {
			voucher := qResult.Data.([]*model.Voucher)[0]

			// Hoàn trả voucher
			increResp := model.VoucherDB.IncreOne(&model.Voucher{Code: voucher.Code},
				"usage_total", -1,
			)

			if increResp.Status != common.APIStatus.Ok {
				return increResp
			}

			// Thêm code xử lý-> cập nhật status thành ACTIVE
			if voucher.Status != nil && *voucher.Status == enum.VoucherStatus.HIDE {
				if voucher.MaxUsage != nil && *voucher.MaxUsage > 0 && voucher.UsageTotal != nil && (*voucher.UsageTotal-1) < *voucher.MaxUsage {

					var status *enum.VoucherStatusValue = nil
					// nếu còn hạn
					if time.Now().Unix() <= voucher.EndTime.Unix() {
						status = &enum.VoucherStatus.ACTIVE
					}

					upResp := model.VoucherDB.UpdateOne(model.Voucher{
						ID:        voucher.ID,
						VersionNo: voucher.VersionNo,
					}, model.Voucher{
						Status: status,
					},
					)

					if upResp.Status != common.APIStatus.Ok {
						// Cập nhật voucher cần check (needCheck = true -> có worker đi check data)
						t := true
						return model.VoucherDB.UpdateOne(&model.Voucher{Code: voucherCode}, &model.Voucher{NeedCheck: &t})
					}
				}
			}

			return increResp

		}
		return qResult
	}
	return qResult
}

func getDetailVouchers(voucherList []*model.Voucher, customerId int64) []*model.DetailVoucherResponse {
	voucherCodes := make([]string, 0)
	for _, voucher := range voucherList {
		voucherCodes = append(voucherCodes, voucher.Code)
	}
	qUserPromotionResult := model.UserPromotionDB.Query(bson.M{
		"voucher_code": bson.M{
			"$in": voucherCodes,
		},
		"customer_id": customerId,
	}, 0, int64(len(voucherCodes)), nil)

	mapUserPromotion := make(map[string]*model.UserPromotion, 0)
	if qUserPromotionResult.Status == common.APIStatus.Ok {
		for _, userPromo := range qUserPromotionResult.Data.([]*model.UserPromotion) {
			mapUserPromotion[userPromo.VoucherCode] = userPromo
		}
	}

	resp := make([]*model.DetailVoucherResponse, 0)

	for _, voucher := range voucherList {
		resp = append(resp, &model.DetailVoucherResponse{
			Voucher:       voucher,
			UserPromotion: mapUserPromotion[voucher.Code],
		})
	}
	return resp
}

// CheckVoucher func
func CheckVoucher() {

	vouchersNeedActive := model.VoucherDB.Query(
		bson.M{
			"need_check": true,
		},
		0,
		100,
		nil,
	)

	f := false

	if vouchersNeedActive.Status == common.APIStatus.Ok {
		vouchers := vouchersNeedActive.Data.([]*model.Voucher)
		if len(vouchers) > 0 {

			for _, voucher := range vouchers {

				// Nếu voucher đang bị ẩn
				if voucher.Status != nil && *voucher.Status == enum.VoucherStatus.HIDE {
					if voucher.MaxUsage != nil && *voucher.MaxUsage > 0 && voucher.UsageTotal != nil && *voucher.UsageTotal < *voucher.MaxUsage {

						var status *enum.VoucherStatusValue = nil
						// nếu còn hạn
						if time.Now().Unix() <= voucher.EndTime.Unix() {
							status = &enum.VoucherStatus.ACTIVE
						}

						model.VoucherDB.UpdateOne(model.Voucher{
							ID:        voucher.ID,
							VersionNo: voucher.VersionNo,
						}, model.Voucher{
							NeedCheck: &f,
							Status:    status,
						},
						)
					}
				} else {
					// Cập nhật need check = false
					model.VoucherDB.UpdateOne(model.Voucher{
						ID:        voucher.ID,
						VersionNo: voucher.VersionNo,
					}, model.Voucher{
						NeedCheck: &f,
					},
					)

				}
				WarmUpVoucherByCode(voucher.Code, nil, "")
			}

		}
	}
}

/*
GetListVoucher is func to get list voucher
@author: tuanv.tran
*/
func GetListVoucher(query *model.Voucher, offset, limit int64, getTotal bool, sortField string, sortType int) *common.APIResponse {
	result := model.VoucherDB.Query(query, offset, limit, &primitive.M{sortField: sortType})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.VoucherDB.Count(query).Total
	}
	return result
}

/*
MigrateHashTagVoucher is func to update hash tag
*/
func MigrateHashTagVoucher() *common.APIResponse {
	voucherRes := model.VoucherDB.Query(model.Voucher{}, 0, 0, nil)
	if voucherRes.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Không tìm thấy mã khuyến mãi",
		}
	}

	vouchers := voucherRes.Data.([]*model.Voucher)
	for _, voucher := range vouchers {
		normCodeStr := strings.Replace(utils.NormalizeString(voucher.Code), " ", "-", -1)
		normPromotionNameStr := strings.Replace(utils.NormalizeString(voucher.PromotionName), " ", "-", -1)
		hashTag := fmt.Sprintf("%d-%s-%s", voucher.PromotionID, normCodeStr, normPromotionNameStr)

		updateResult := model.VoucherDB.UpdateOne(&model.Voucher{
			Code: voucher.Code,
		}, &model.Voucher{
			HashTag: hashTag,
		})

		if updateResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Cập nhật mã khuyến mãi thất bại.",
			}
		}

		voucher.HashTag = hashTag
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật mã khuyến mãi thành công.",
		Data:    vouchers,
	}
}

// DeleteAppliedCustomers delete data appliedCustomers in voucher
func DeleteAppliedCustomers(input *model.DeleteAppliedCustomersRequest, AccountID int64) *common.APIResponse {
	voucherResult := model.VoucherDB.QueryOne(&model.Voucher{
		VoucherID: input.VoucherID,
	})

	if voucherResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Mã khuyến mãi không tồn tại.",
			ErrorCode: "INVALID_VOUCHER_ID_INPUT",
		}
	}

	existedVoucher := voucherResult.Data.([]*model.Voucher)[0]

	appliedCustomers := []int64{}
	existedVoucher.UpdatedBy = AccountID
	existedVoucher.VersionNo = uuid.New().String()
	existedVoucher.AppliedCustomers = &appliedCustomers

	afterOption := options.After

	return model.VoucherDB.UpdateOne(
		&model.Voucher{
			VoucherID: input.VoucherID,
		},
		existedVoucher,
		&options.FindOneAndUpdateOptions{
			ReturnDocument: &afterOption,
		},
	)
}

// GetVoucherUsageHistoryList get list voucher usage history
func GetVoucherUsageHistoryList(query *model.Voucher, offset, limit int64, getTotal bool, tabType string) *common.APIResponse {
	if len(tabType) > 0 {
		if tabType == "NOT_USED" {
			if query.CustomerID != nil {
				voucherCodes := make([]string, 0)
				if query.CustomerID != nil {
					offset2 := int64(0)
					limit2 := int64(1000)
					for {
						userPromotionResp := model.UserPromotionDB.Query(&bson.M{
							"customer_id": *query.CustomerID,
							"$or": []bson.M{
								{"amount": nil},
								{"amount": 0},
							},
							"status": bson.M{"$nin": []string{string(enum.CodeStatus.DELETED), string(enum.CodeStatus.INACTIVE), string(enum.CodeStatus.USED)}},
						}, offset2*limit2, limit2, nil)
						if userPromotionResp.Status != common.APIStatus.Ok {
							break
						}

						for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
							voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
						}
						offset2++
					}

				}

				if len(voucherCodes) == 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy mã khuyến mãi.",
						ErrorCode: "VOUCHER_NOT_FOUND",
					}
				}

				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"code": bson.M{
						"$in": voucherCodes,
					},
				})
			} else {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"$or": []bson.M{
						{"usage_total": nil},
						{"usage_total": 0},
					},
				})
			}
		} else if tabType == "USED" {
			if query.CustomerID != nil {
				voucherCodes := make([]string, 0)
				if query.CustomerID != nil {
					offset2 := int64(0)
					limit2 := int64(1000)
					for {
						userPromotionResp := model.UserPromotionDB.Query(&bson.M{
							"customer_id": *query.CustomerID,
							"amount": bson.M{
								"$gt": 0,
							},
						}, offset2*limit2, limit2, nil)
						if userPromotionResp.Status != common.APIStatus.Ok {
							break
						}

						for _, userPromotion := range userPromotionResp.Data.([]*model.UserPromotion) {
							voucherCodes = append(voucherCodes, userPromotion.VoucherCode)
						}
						offset2++
					}
				}

				if len(voucherCodes) == 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Không tìm thấy mã khuyến mãi.",
						ErrorCode: "VOUCHER_NOT_FOUND",
					}
				}

				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"code": bson.M{
						"$in": voucherCodes,
					},
				})
			} else {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"usage_total": &bson.M{
						"$exists": true,
						"$gt":     0,
					},
				})
			}
		}
	}

	result := model.VoucherDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.VoucherDB.Count(query).Total
	}
	return result
}

// GetVoucherBySku is func to get voucher by sku
func GetVoucherBySku(acc *model.Account, customerId int64, skus []string, getVoucherInfo bool, offset, limit int, getTotal bool, systemDisplay string) *common.APIResponse {
	type respData struct {
		Sku         string                      `json:"sku"`
		ProductCode string                      `json:"productCode"`
		Vouchers    []*model.VoucherViewWebOnly `json:"vouchers,omitempty"`
		HasGift     bool                        `json:"hasGift"`
	}
	resp := make([]*respData, 0)
	productCodes := make([]string, 0)
	//sellerCodes := make([]string, 0)
	mapSku := make(map[string]string, 0)
	mapProduct := make(map[string]string, 0)
	mapSkuGift := make(map[string]bool, 0)

	var customer *model.Customer
	var err error
	if customerId != 0 {
		customer, err = client.Services.Customer.GetCustomerByCustomerID(customerId)
	} else {
		customer, err = client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	}

	// customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID) // get customer info
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "GET_CUSTOMER_ERROR",
		}
	}
	for _, sku := range skus {
		mapSku[sku] = sku
		tmp := strings.Split(sku, ".")
		if len(tmp) > 1 {
			productCodes = append(productCodes, tmp[1])
			mapProduct[tmp[1]] = sku
		}
	}

	queryBson := bson.M{
		"exist_ref_product": true,
		"system_display":    "BUYMED",
	}

	if len(skus) > 0 {
		if len(skus) == 1 {
			queryBson["ref_product"] = skus[0]
		} else {
			queryBson["ref_product"] = bson.M{"$in": skus}
		}
	}

	if systemDisplay != "" {
		queryBson["system_display"] = systemDisplay
	}

	now := time.Now()
	queryBson["status"] = "ACTIVE"
	queryBson["filter"] = bson.M{"$all": []string{customer.ProvinceCode, customer.Level, customer.Scope}}
	queryBson["public_time"] = bson.M{"$lte": now}

	qVoucher := model.VoucherCacheReadDB.Query(queryBson, 0, 0, &primitive.M{"priority": -1})
	mapVoucher := make(map[string][]*model.VoucherViewWebOnly)
	voucherCodes := make([]string, 0)
	vouchers := make([]*model.Voucher, 0)
	if qVoucher.Status == common.APIStatus.Ok {
		for _, voucher := range qVoucher.Data.([]*model.Voucher) {
			voucherCodes = append(voucherCodes, voucher.Code)
			vouchers = append(vouchers, voucher)
		}
	}
	mapUseVoucher := make(map[string]bool)
	mapNotUseVoucher := make(map[string]bool)
	qUseVoucher := model.UserPromotionCacheDB.Query(model.UserPromotion{CustomerID: customer.CustomerID, ComplexQuery: []*bson.M{
		{
			"voucher_code": bson.M{"$in": voucherCodes},
		},
	}}, 0, 0, nil)
	if qUseVoucher.Status == common.APIStatus.Ok {
		for _, use := range qUseVoucher.Data.([]*model.UserPromotion) {
			if use.Status != nil && *use.Status != enum.CodeStatus.DELETED {
				mapUseVoucher[use.VoucherCode] = true
			}
			if use.Status != nil && *use.Status == enum.CodeStatus.INACTIVE {
				mapNotUseVoucher[use.VoucherCode] = true
			}
		}
	}
	for _, voucher := range vouchers {
		if voucher.CustomerApplyType == enum.CustomerApplyType.MANY && !mapUseVoucher[voucher.Code] {
			continue
		}
		if voucher.CustomerApplyType == enum.CustomerApplyType.ALL && mapNotUseVoucher[voucher.Code] {
			continue
		}
		mapCheckExist := make(map[string]bool)
		for _, key := range voucher.RefProduct {
			if len(skus) > 0 {
				value := mapProduct[key]
				if value == "" {
					value = mapSku[key]
				}
				if value != "" {
					mapVoucher[value] = append(mapVoucher[value], setViewData(voucher, nil))
					if len(voucher.Rewards) > 0 && len(voucher.Rewards[0].Gifts) > 0 {
						mapSkuGift[value] = true
					}
				}
			} else {
				if data, ok := mapCheckExist[key]; ok && data {
					continue
				}
				d := respData{}
				strArr := strings.Split(key, ".")
				if len(strArr) > 1 {
					d.Sku = key
				} else {
					d.ProductCode = key
				}

				if len(voucher.Rewards) > 0 && len(voucher.Rewards[0].Gifts) > 0 {
					mapCheckExist[key] = true
					d.HasGift = true
				}
				resp = append(resp, &d)
			}

		}
	}

	if len(skus) > 0 {
		resp = make([]*respData, 0)
		for k, v := range mapVoucher {
			d := respData{
				Sku:     k,
				HasGift: mapSkuGift[k],
			}
			if getVoucherInfo {
				d.Vouchers = v
			}
			resp = append(resp, &d)
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   resp,
	}
}

func getPromotionByGroupCode(groupCode string) *model.Promotion {
	now := time.Now()
	query := model.Promotion{
		VoucherGroupCode: utils.ParseStringToPointer(groupCode),
		Status:           &enum.PromotionStatus.ACTIVE,
		ComplexQuery: []*bson.M{
			{
				"start_time": bson.M{"$lte": now},
			},
			{
				"end_time": bson.M{"$gte": now},
			},
		},
	}
	q := model.PromotionDB.QueryOne(query)
	if q.Status == common.APIStatus.Ok && len(q.Data.([]*model.Promotion)) > 0 {
		return q.Data.([]*model.Promotion)[0]
	}
	return nil
}

func SelfColectVoucher(acc *model.Account, voucherCode string, collectSource *model.CollectSource) *common.APIResponse {
	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "CUSTOMER_NOT_FOUND",
			Message:   "Không tìm thấy thông tin khách hàng",
		}
	}

	if voucherCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "VOUCHER_CODE_NOT_FOUND",
			Message:   "Không tìm thấy mã voucher",
		}
	}

	qVoucher := model.VoucherDB.QueryOne(model.Voucher{
		Code: voucherCode,
	})

	if qVoucher.Status != common.APIStatus.Ok {
		return qVoucher
	}
	voucher := qVoucher.Data.([]*model.Voucher)[0]

	qUserPromotion := model.UserPromotionDB.QueryOne(bson.M{
		"customer_id":  customer.CustomerID,
		"voucher_code": voucherCode,
	})

	now := time.Now()
	collectSource.TimeCollect = now

	if qUserPromotion.Status == common.APIStatus.Ok {
		updateResult := model.UserPromotionDB.UpdateOne(model.UserPromotion{VoucherCode: voucherCode, CustomerID: customer.CustomerID}, &model.UserPromotion{
			IsCollected:   utils.ParseBoolToPointer(true),
			CollectSource: collectSource,
		})
		if updateResult.Status == common.APIStatus.Ok {
			WarmUpUserPromotion(updateResult.Data.([]*model.UserPromotion)[0].VoucherCode, updateResult.Data.([]*model.UserPromotion)[0].CustomerID, nil)
		}

		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Thu thập thành công",
			Data:    updateResult.Data.([]*model.UserPromotion),
		}
	}

	createResult := model.UserPromotionDB.Create(&model.UserPromotion{
		CreatedBy:     acc.AccountID,
		CustomerID:    customer.CustomerID,
		PromotionID:   voucher.PromotionID,
		VoucherCode:   voucherCode,
		IsCollected:   utils.ParseBoolToPointer(true),
		Status:        &enum.CodeStatus.ACTIVE,
		CollectSource: collectSource,
	})

	if createResult.Status == common.APIStatus.Ok {
		WarmUpUserPromotion(createResult.Data.([]*model.UserPromotion)[0].VoucherCode, createResult.Data.([]*model.UserPromotion)[0].CustomerID, nil)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Thu thập thành công",
		Data:    createResult.Data.([]*model.UserPromotion),
	}
}

func UpdateVoucherGroup(v *model.Voucher) *common.APIResponse {
	result := model.VoucherDB.UpdateOne(&model.Voucher{
		VoucherID: v.VoucherID,
		Code:      v.Code,
	}, v)

	if result.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Cập nhật mã khuyến mãi không thành công!",
			ErrorCode: "SERVER_ERROR",
		}
	}

	go func() {
		WarmUpVoucherByCode(result.Data.([]*model.Voucher)[0].Code, result.Data.([]*model.Voucher)[0], "")
		WarmUpUserPromotion(result.Data.([]*model.Voucher)[0].Code, 0, nil)
	}()
	return result
}

func CreateMultipleVoucher(acc *model.Account, input *model.CreateMMultiVoucherRequest) *common.APIResponse {
	importResultCode := model.GenCodeWithTime()

	importResultResp := model.ImportResultDB.Create(&model.ImportResult{
		Code:            importResultCode,
		AccountFullname: acc.Fullname,
		Username:        acc.Username,
		AccountID:       acc.AccountID,
		Total:           len(input.Data),
		ModelName:       "VOUCHER_CREATE",
		Status:          "IN_PROGRESS",
	})

	if importResultResp.Status == common.APIStatus.Ok {
		go func() {
			listDetail := make([]*model.ImportResultDetail, 0)
			importJobCode := model.GenCodeWithTime()
			for index, item := range input.Data {
				importResultDetailCode := model.GenCodeWithTime(index + 1)
				request, _ := json.Marshal(item)
				importResultDetail := &model.ImportResultDetail{
					ImportResultCode: importResultCode,
					ImportJobCode:    importJobCode,
					Code:             importResultDetailCode,
					Request:          string(request),
					Status:           "PENDING",
					IndexImport:      index + 1,
				}
				listDetail = append(listDetail, importResultDetail)

				if len(listDetail) == 1 {
					model.ImportResultDetailDB.CreateMany(listDetail)
					errPushJob := model.ImportVoucherJob.Push(importItem{
						Code:          importResultCode,
						Username:      acc.Username,
						AccountID:     acc.AccountID,
						ImportJobCode: importJobCode,
					}, &job.JobItemMetadata{
						Topic: "default",
						Keys:  []string{"VOUCHER_CREATE", importResultCode},
					})

					if errPushJob != nil {
						fmt.Println("Error push job create voucher, ", errPushJob.Error())
					}
					listDetail = make([]*model.ImportResultDetail, 0)
					importJobCode = model.GenCodeWithTime()
				}
			}

			if len(listDetail) > 0 {
				model.ImportResultDetailDB.CreateMany(listDetail)
				errPushJob := model.ImportVoucherJob.Push(importItem{
					Code:          importResultCode,
					Username:      acc.Username,
					AccountID:     acc.AccountID,
					ImportJobCode: importJobCode,
				}, &job.JobItemMetadata{
					Topic: "default",
					Keys:  []string{"VOUCHER_CREATE", importResultCode},
				})

				if errPushJob != nil {
					fmt.Println("Error push job create voucher, ", errPushJob.Error())
				}
			}
		}()
	}

	return importResultResp
}

func ImportVoucherConsumer(item *job.JobItem) (errRes error) {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var importItem model.ImportItem
	err = bson.Unmarshal(data, &importItem)
	if err != nil {
		return err
	}
	func() {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("panic ImportVoucherConsumer: %s", string(debug.Stack()))
				fmt.Printf("[%d - %s - %s] %s\n", importItem.AccountID, importItem.ImportJobCode, importItem.Code, err.Error())
			}
		}()

		errRes = excuseImportVoucher(importItem)
	}()

	return errRes
}

func excuseImportVoucher(importItem model.ImportItem) error {
	qContent := model.ImportResultDetailDB.Query(model.ImportResultDetail{ImportJobCode: importItem.ImportJobCode}, 0, 0, nil)
	if qContent.Status != common.APIStatus.Ok {
		return fmt.Errorf("%s_%s", qContent.ErrorCode, qContent.Message)
	}
	details := qContent.Data.([]*model.ImportResultDetail)
	failCount := 0
	for _, detail := range details {
		startTime := time.Now()
		var voucher model.Voucher
		var resp *common.APIResponse
		err := json.Unmarshal([]byte(detail.Request), &voucher)
		if err != nil {
			resp = &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Can not parse input data, %s", err.Error()),
				ErrorCode: "PAYLOAD_INVALID",
			}
		}
		resp = CreateVoucher(&voucher, &model.Account{AccountID: importItem.AccountID})
		if updateImportResultDetailWithTime(importItem.Code, detail.Code, resp, &startTime); resp.Status != common.APIStatus.Ok {
			failCount = failCount + 1
		}
	}
	updateImportResult(importItem.Code, len(details)-failCount, failCount)
	if res := completedImport(importItem); res.Status == common.APIStatus.Ok {
		_ = client.Services.Notification.CreateNotification(&model.Notification{
			Username:     importItem.Username,
			UserID:       importItem.AccountID,
			ReceiverType: utils.ParseStringToPointer("EMPLOYEE"),
			Topic:        "ANNOUNCEMENT",
			Title:        fmt.Sprintln("Cập nhật trạng thái import voucher thành công"),
			Link:         fmt.Sprintf("/marketing/history-import-voucher?code=%s", importItem.Code),
		})
	}

	return nil
}

// Update multi voucher
func UpdateMultipleVoucher(acc *model.Account, input *model.UpdateMultiVoucherRequest) *common.APIResponse {
	importResultCode := model.GenCodeWithTime()

	importResultResp := model.ImportResultDB.Create(&model.ImportResult{
		Code:            importResultCode,
		AccountFullname: acc.Fullname,
		Username:        acc.Username,
		AccountID:       acc.AccountID,
		Total:           len(input.Data),
		ModelName:       "VOUCHER_UPDATE",
		Status:          "IN_PROGRESS",
	})

	if importResultResp.Status == common.APIStatus.Ok {
		go func() {
			listDetail := make([]*model.ImportResultDetail, 0)
			importJobCode := model.GenCodeWithTime()
			for index, item := range input.Data {
				importResultDetailCode := model.GenCodeWithTime(index + 1)
				request, _ := json.Marshal(item)
				importResultDetail := &model.ImportResultDetail{
					ImportResultCode: importResultCode,
					ImportJobCode:    importJobCode,
					Code:             importResultDetailCode,
					Request:          string(request),
					Status:           "PENDING",
					IndexImport:      index + 1,
				}
				listDetail = append(listDetail, importResultDetail)

				if len(listDetail) == 1 {
					model.ImportResultDetailDB.CreateMany(listDetail)
					errPushJob := model.ImportUpdateVoucherJob.Push(importItem{
						Code:          importResultCode,
						Username:      acc.Username,
						AccountID:     acc.AccountID,
						ImportJobCode: importJobCode,
					}, &job.JobItemMetadata{
						Topic: "default",
						Keys:  []string{"VOUCHER_UPDATE", importResultCode},
					})

					if errPushJob != nil {
						fmt.Println("Error push job update voucher, ", errPushJob.Error())
					}
					listDetail = make([]*model.ImportResultDetail, 0)
					importJobCode = model.GenCodeWithTime()
				}
			}

			if len(listDetail) > 0 {
				model.ImportResultDetailDB.CreateMany(listDetail)
				errPushJob := model.ImportUpdateVoucherJob.Push(importItem{
					Code:          importResultCode,
					Username:      acc.Username,
					AccountID:     acc.AccountID,
					ImportJobCode: importJobCode,
				}, &job.JobItemMetadata{
					Topic: "default",
					Keys:  []string{"VOUCHER_UPDATE", importResultCode},
				})

				if errPushJob != nil {
					fmt.Println("Error push job update voucher, ", errPushJob.Error())
				}
			}
		}()
	}

	return importResultResp
}

func ImportUpdateVoucherConsumer(item *job.JobItem) (errRes error) {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var importItem model.ImportItem
	err = bson.Unmarshal(data, &importItem)
	if err != nil {
		return err
	}
	func() {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("panic ImportUpdateVoucherConsumer: %s", string(debug.Stack()))
				fmt.Printf("[%d - %s - %s] %s\n", importItem.AccountID, importItem.ImportJobCode, importItem.Code, err.Error())
			}
		}()

		errRes = excuseImportUpdateVoucher(importItem)
	}()

	return errRes
}

func excuseImportUpdateVoucher(importItem model.ImportItem) error {
	qContent := model.ImportResultDetailDB.Query(model.ImportResultDetail{ImportJobCode: importItem.ImportJobCode}, 0, 0, nil)
	if qContent.Status != common.APIStatus.Ok {
		return fmt.Errorf("%s_%s", qContent.ErrorCode, qContent.Message)
	}
	details := qContent.Data.([]*model.ImportResultDetail)
	failCount := 0
	for _, detail := range details {
		startTime := time.Now()
		var voucher model.UpdateVoucherRequest
		var resp *common.APIResponse
		err := json.Unmarshal([]byte(detail.Request), &voucher)
		if err != nil {
			resp = &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Can not parse input data, %s", err.Error()),
				ErrorCode: "PAYLOAD_INVALID",
			}
		}

		// Handle logic import update scope of voucher
		// Check and get scope of voucher
		if len(voucher.Scopes) > 0 {
			voucherResp := model.VoucherDB.QueryOne(model.Voucher{Code: voucher.Code})

			if voucherResp.Status == common.APIStatus.Ok {
				dataResp := &model.Voucher{}

				if dataSlice, ok := voucherResp.Data.([]*model.Voucher); ok && len(dataSlice) > 0 {
					dataResp = dataSlice[0]
				}

				voucherScopeMap := make(map[string]model.Scope)

				// Create map scope of voucher
				for _, scope := range voucher.Scopes {
					if scope.Type != nil {
						scopeKey := string(enum.ScopeTypeValue(*scope.Type))
						voucherScopeMap[scopeKey] = scope
					}
				}

				// Check old scope and new scope to update scope of voucher
				for i, dataScope := range dataResp.Scopes {
					if dataScope.Type == nil {
						continue
					}

					scopeKey := string(enum.ScopeTypeValue(*dataScope.Type))
					if updatedScope, ok := voucherScopeMap[scopeKey]; ok {
						dataResp.Scopes[i] = updatedScope
					}
				}

				// Update scope for voucher
				voucher.Scopes = dataResp.Scopes
			}
		}

		resp = UpdateVoucher(&voucher, importItem.AccountID)
		if updateImportResultDetailWithTime(importItem.Code, detail.Code, resp, &startTime); resp.Status != common.APIStatus.Ok {
			failCount = failCount + 1
		}
	}
	updateImportResult(importItem.Code, len(details)-failCount, failCount)
	if res := completedImport(importItem); res.Status == common.APIStatus.Ok {
		_ = client.Services.Notification.CreateNotification(&model.Notification{
			Username:     importItem.Username,
			UserID:       importItem.AccountID,
			ReceiverType: utils.ParseStringToPointer("EMPLOYEE"),
			Topic:        "ANNOUNCEMENT",
			Title:        fmt.Sprintln("Cập nhật trạng thái import cập nhật voucher thành công"),
			Link:         fmt.Sprintf("/marketing/history-import-update-voucher?code=%s", importItem.Code),
		})
	}

	return nil
}

func getProductInfoByCondition(orConditions, andConditions map[enum.ConditionTypeValue]model.PromotionType, applyDiscount *model.ApplyDiscountOptions, sellerCodes *[]string, storeCode *string) ([]string, []string, []string, []string, []string, []string) {
	var productCodes, tagCodes, skuCodes, sellers, skusNotIn, tagCodesIn []string
	mapUnitSKU := make(map[string]struct{})
	mapUnitSKUNotIn := make(map[string]struct{})
	mapUnitSeller := make(map[string]struct{})
	mapUnitProductCode := make(map[string]struct{})
	mapUnitTag := make(map[string]struct{})
	mapUnitTagIn := make(map[string]struct{})

	extractCodes := func(conditions []model.PromotionCondition) {
		for _, condition := range conditions {
			if condition.ProductConditionField.ProductCode != "" {
				if _, exists := mapUnitProductCode[condition.ProductConditionField.ProductCode]; !exists {
					mapUnitProductCode[condition.ProductConditionField.ProductCode] = struct{}{}
					productCodes = append(productCodes, condition.ProductConditionField.ProductCode)
				}
			}
			if condition.ProductConditionField.SellerCode != nil {
				productConditionSku := fmt.Sprintf("%s.%s", *condition.ProductConditionField.SellerCode, condition.ProductConditionField.ProductCode)
				if _, exists := mapUnitSKU[productConditionSku]; !exists {
					mapUnitSKU[productConditionSku] = struct{}{}
					skuCodes = append(skuCodes, productConditionSku)
				}

				if _, exists := mapUnitSeller[*condition.ProductConditionField.SellerCode]; !exists {
					mapUnitSeller[*condition.ProductConditionField.SellerCode] = struct{}{}
					sellers = append(sellers, *condition.ProductConditionField.SellerCode)
				}
			}
			if condition.ProductTagConditionField.TagCode != "" {
				if _, exists := mapUnitTagIn[condition.ProductTagConditionField.TagCode]; !exists {
					mapUnitTagIn[condition.ProductTagConditionField.TagCode] = struct{}{}
					tagCodesIn = append(tagCodesIn, condition.ProductTagConditionField.TagCode)
				}
			}
		}
	}

	for _, promotionType := range orConditions {
		extractCodes(promotionType.OrConditions)
		extractCodes(promotionType.AndConditions)
	}

	for _, promotionType := range andConditions {
		extractCodes(promotionType.OrConditions)
		extractCodes(promotionType.AndConditions)
	}

	if applyDiscount != nil && applyDiscount.Skus != nil && len(*applyDiscount.Skus) > 0 {
		for _, sku := range *applyDiscount.Skus {
			if _, exists := mapUnitSKU[sku]; !exists {
				mapUnitSKU[sku] = struct{}{}
				skuCodes = append(skuCodes, sku)
			}
		}
	}

	if applyDiscount != nil && applyDiscount.NotInSkus != nil && len(*applyDiscount.NotInSkus) > 0 {
		for _, sku := range *applyDiscount.NotInSkus {
			if _, exists := mapUnitSKU[sku]; !exists {
				mapUnitSKUNotIn[sku] = struct{}{}
				skusNotIn = append(skusNotIn, sku)
			}
		}
	}

	if sellerCodes != nil && len(*sellerCodes) > 0 {
		for _, sellerCode := range *sellerCodes {
			if _, exists := mapUnitSeller[sellerCode]; !exists {
				mapUnitSeller[sellerCode] = struct{}{}
				sellers = append(sellers, sellerCode)
			}
		}
	}

	//TODO: If don't have any condition and/or, we will use store's tag code
	if len(tagCodesIn) == 0 {
		if storeCode != nil && *storeCode != "" && mapVendorStoreByTag != nil {
			muMapVendorStoreByTag.RLock()
			allProductTag, ok := mapVendorStoreByTag[*storeCode]
			muMapVendorStoreByTag.RUnlock()
			if ok {
				if _, exists := mapUnitTagIn[allProductTag]; !exists {
					mapUnitTag[allProductTag] = struct{}{}
					tagCodes = append(tagCodes, allProductTag)
				}
			}
		}
	}

	return productCodes, tagCodes, skuCodes, sellers, skusNotIn, tagCodesIn
}

func setDataNameToCondition(orConditions, andConditions map[enum.ConditionTypeValue]model.PromotionType, products map[string]*model.Product, tags map[string]*model.Tag) {
	setNames := func(conditions []model.PromotionCondition) []model.PromotionCondition {
		for i, condition := range conditions {
			if productDetail, ok := products[condition.ProductConditionField.ProductCode]; ok {
				conditions[i].ProductConditionField.ProductName = productDetail.Name
			}
			if tagDetail, ok := tags[condition.ProductTagConditionField.TagCode]; ok {
				conditions[i].ProductTagConditionField.TagName = tagDetail.Name
			}
		}
		return conditions
	}
	for conditionType, promotionType := range orConditions {
		promotionType.OrConditions = setNames(promotionType.OrConditions)
		promotionType.AndConditions = setNames(promotionType.AndConditions)
		orConditions[conditionType] = promotionType
	}

	for conditionType, promotionType := range andConditions {
		promotionType.OrConditions = setNames(promotionType.OrConditions)
		promotionType.AndConditions = setNames(promotionType.AndConditions)
		andConditions[conditionType] = promotionType
	}
}

func UpdateSpecificVoucher(input *model.UpdateSpecificVoucher) *common.APIResponse {
	result := model.VoucherDB.UpdateOne(&model.Voucher{
		VoucherID: input.VoucherID,
		Code:      input.Code,
	}, model.UpdateSpecificVoucher{
		IsSpecific: input.IsSpecific,
	})

	if result.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Lỗi! Không thể cập nhật trạng thái tính chiết khấu vào giá hiển thị",
			ErrorCode: "VOUCHER_UPDATE_ERROR",
		}
	}

	go func() {
		WarmUpVoucherByCode(result.Data.([]*model.Voucher)[0].Code, result.Data.([]*model.Voucher)[0], "")
	}()
	return result
}

// ReuseOnOrderCancel is func to update reissueable on order cancel
func ReuseOnOrderCancel(input *model.ReuseOnOrderCancelRequest, acc *model.Account) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "data is invalid",
			ErrorCode: "INVALID_INPUT",
		}
	}
	if input.RedeemCodes == nil || len(*input.RedeemCodes) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không có mã khuyến mãi nào được chọn",
			ErrorCode: "INVALID_REDDEM_CODE",
		}
	}

	// get voucher consist of redeem codes & voucherReIssuableOnCancelOrder = true
	voucherCodes := *input.RedeemCodes
	voucherQuery := model.Voucher{
		ComplexQuery: []*bson.M{
			{
				"code": bson.M{
					"$in": voucherCodes,
				},
			},
		},
	}
	voucherResult := model.VoucherDB.Query(voucherQuery, 0, 0, nil)
	if voucherResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy mã khuyến mãi",
			ErrorCode: "VOUCHER_NOT_FOUND",
		}
	}

	voucherValid := make(map[string]*model.Voucher)
	vouchers := voucherResult.Data.([]*model.Voucher)
	now := time.Now()
	for _, voucher := range vouchers {
		if voucher.StartTime == nil || voucher.EndTime == nil {
			continue
		}

		// validation
		if voucher.IsReuseOnOrderCancel == nil || !*voucher.IsReuseOnOrderCancel {
			continue
		}
		if voucher.VoucherReuseDuration == nil || *voucher.VoucherReuseDuration == 0 {
			continue
		} else {
			if voucher.EndTime.Before(now) {
				continue
			}

			durationDay := voucher.EndTime.Sub(now).Hours() / 24
			if durationDay < float64(*voucher.VoucherReuseDuration) {
				voucher.StartTime = &now
				voucher.PublicTime = voucher.StartTime
				newEndTime := voucher.StartTime.AddDate(0, 0, *voucher.VoucherReuseDuration)
				voucher.EndTime = &newEndTime
			}
		}

		voucher.ID = primitive.NewObjectID()
		newPattern := fmt.Sprintf("%s_%s_%v", "RF", voucher.Code, input.OrderID)
		voucher.OldVoucherCode = voucher.Code
		voucher.Code = newPattern
		voucher.VoucherID = model.GenId("VOUCHER_ID")
		voucher.UsageTotal = utils.ParseInt64ToPointer(0)
		voucher.MaxUsagePerCustomer = utils.ParseInt64ToPointer(1)
		voucher.CustomerApplyType = enum.CustomerApplyType.MANY
		voucher.Status = &enum.VoucherStatus.ACTIVE
		voucherValid[voucher.Code] = voucher
	}
	if len(voucherValid) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Ok,
			ErrorCode: "VOUCHER_TO_REFUND_NOT_FOUND",
		}
	}

	for _, voucher := range voucherValid {
		voucher = formatVoucherDisplayName(voucher)
		normCodeStr := strings.Replace(utils.NormalizeString(voucher.Code), " ", "-", -1)
		normDisplayNameStr := strings.Replace(utils.NormalizeString(voucher.DisplayName), " ", "-", -1)
		normShortNameStr := strings.Replace(utils.NormalizeString(voucher.ShortName), " ", "-", -1)
		voucher.HashTag = fmt.Sprintf("%d-%s-%s-%s", voucher.PromotionID, normCodeStr, normDisplayNameStr, normShortNameStr)

		createResult := model.VoucherDB.Create(voucher)
		if createResult.Status == common.APIStatus.Ok {
			result := createResult.Data.([]*model.Voucher)[0]
			CreateUserVoucher(acc, &model.UserPromotion{
				CustomerIDs: []int64{input.CustomerID},
				VoucherCode: result.Code,
				Status:      &enum.CodeStatus.ACTIVE,
			})

			WarmUpVoucherByCode(result.Code, result, "")
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}

func getListUserPromotionActive(voucherCode string) ([]int64, []int64, []int64) {
	activeCustomerIDs := make([]int64, 0)
	inactiveCustomerIDs := make([]int64, 0)
	customerIDs := make([]int64, 0)
	_id_offset := primitive.NilObjectID
	limit := int64(100)
	for {
		// query user voucher
		query := model.UserPromotion{
			VoucherCode: voucherCode,
			ComplexQuery: []*bson.M{
				{"_id": bson.M{"$gt": _id_offset}},
			},
		}

		userVoucherResp := model.UserPromotionDB.Query(query, 0, limit, &bson.M{"_id": 1})
		if userVoucherResp.Status != common.APIStatus.Ok {
			break
		}

		userVoucher := userVoucherResp.Data.([]*model.UserPromotion)
		if len(userVoucher) == 0 {
			break
		}

		for _, uv := range userVoucher {
			_id_offset = uv.ID
			customerIDs = append(customerIDs, uv.CustomerID)
			if uv.Status != nil && *uv.Status == enum.CodeStatus.INACTIVE {
				inactiveCustomerIDs = append(inactiveCustomerIDs, uv.CustomerID)
			} else {
				activeCustomerIDs = append(activeCustomerIDs, uv.CustomerID)
			}
		}
	}

	return customerIDs, activeCustomerIDs, inactiveCustomerIDs
}
