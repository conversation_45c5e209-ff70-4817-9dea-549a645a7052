package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// FriendGift is model define gift for friend
type FriendGift struct {
	PromotionIDs       *[]int64 `json:"promotionIds" bson:"promotion_ids,omitempty"`
	FriendPromotionIDs *[]int64 `json:"friendPromotionIds" bson:"friend_promotion_ids,omitempty"`
	OrderQuantity      int64    `json:"orderQuantity" bson:"order_quantity,omitempty" validate:"required"`
}

// GiftSetting is model define gift setting
type GiftSetting struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       string             `json:"createdBy,omitempty" bson:"created_by,omitempty"`

	NewbieGift       *[]int64            `json:"newbieGift,omitempty" bson:"newbie_gift,omitempty"`
	FriendGift       *FriendGift         `json:"friendGift,omitempty" bson:"friend_gift,omitempty"`
	NewbieGiftDes    *string             `json:"newbieGiftDes,omitempty" bson:"newbie_gift_des,omitempty"`
	FriendGiftDes    *string             `json:"friendGiftDes,omitempty" bson:"friend_gift_des,omitempty"`
	NewbieGiftSubDes *string             `json:"newbieGiftSubDes,omitempty" bson:"newbie_gift_sub_des,omitempty"`
	FriendGiftSubDes *string             `json:"friendGiftSubDes,omitempty" bson:"friend_gift_sub_des,omitempty"`
	PartnerGift      map[string]*[]int64 `json:"partnerGift,omitempty" bson:"partner_gift,omitempty"` // KIOTVIET

}

// GiftSettingDB is an instance db
var GiftSettingDB = &db.Instance{
	ColName:        "gift_setting",
	TemplateObject: &GiftSetting{},
}

// InitGiftSettingModel is func init model
func InitGiftSettingModel(s *mongo.Database) {
	GiftSettingDB.ApplyDatabase(s)
}
