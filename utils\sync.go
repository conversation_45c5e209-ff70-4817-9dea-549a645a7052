package utils

import (
	"sync"
)

type Semaphore struct {
	pool   chan struct{}
	wg     sync.WaitGroup
	isWait bool
}

func (sema *Semaphore) Acquire() {
	sema.pool <- struct{}{}
}

func (sema *Semaphore) Release() {
	<-sema.pool
	if sema.isWait {
		sema.wg.Done()
	}
}
func (sema *Semaphore) Wait() {
	sema.wg.Wait()
}
func NewSemaphore(numWorker int, waitOpt ...int) *Semaphore {
	sema := &Semaphore{
		pool: make(chan struct{}, numWorker),
	}

	if len(waitOpt) > 0 {
		sema.wg.Add(waitOpt[0])
		sema.isWait = true
	}

	return sema
}
