package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type GamificationCustomer struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code           string `json:"code,omitempty" bson:"code,omitempty"`
	CustomerID     int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	GamificationID int64  `json:"gamificationId,omitempty" bson:"gamification_id,omitempty"`
	LuckyWheelCode string `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`

	JoinType string     `json:"joinType,omitempty" bson:"join_type,omitempty"`
	JoinTime *time.Time `json:"joinTime,omitempty" bson:"join_time,omitempty"`

	Status string `json:"status,omitempty" bson:"status,omitempty"`

	ComplexQuery   []*bson.M `json:"-" bson:"$and,omitempty"`
	CustomerStatus string    `json:"customerStatus,omitempty" bson:"-"` // Blacklist, Normal
}

var GamificationCustomerDB = &db.Instance{
	ColName:        "gamification_customer",
	TemplateObject: &GamificationCustomer{},
}

// InitGamificationCustomerModel is func init model
func InitGamificationCustomerModel(s *mongo.Database) {
	GamificationCustomerDB.ApplyDatabase(s)
}
