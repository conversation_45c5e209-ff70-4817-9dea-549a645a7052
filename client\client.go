package client

import (
	"fmt"
	"os"
	"runtime"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/client/circa"

	"gitlab.com/thuocsi.vn/marketplace/promotion/client/customer"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client/order"
	"gitlab.com/thuocsi.vn/marketplace/promotion/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

// Default timeout and retry constants
const DEFAULT_TIMEOUT = 25 * time.Second
const DEFAULT_RETRY_TIME = 1
const DEFAULT_WAIT_TIME = 50 * time.Millisecond

// Promo ..
type gatewayClient struct {
	Customer     *customer.Client
	Order        *order.Client
	Circa        *circa.Client
	Product      *productClient
	Notification *notificationClient
	Seller       *sellerClient
	DataHarvest  *dataHarvestClient
	Config       *configManagerClient

	PersonalizationRule *personalizationRuleClient
}

// Services ...
// ErrUnvaliableService ...
var (
	Services             *gatewayClient
	ErrUnvaliableService = fmt.Errorf("%s", "Service customer unavailable")
)

func init() {
	Services = &gatewayClient{
		Customer: customer.NewServiceClient(conf.Config.APIHost, conf.Config.APIKey, conf.Config.LogDBConf.DatabaseName),
		// Product:  NewProductClient(conf.Config.APIHost, conf.Config.APIKey, conf.Config.LogDBName),
	}
}

func InitClientWithLog(s *mongo.Database) {
	Services.Order = order.NewServiceClient(conf.Config.APIHost, conf.Config.APIKey, s)
	Services.Product = NewProductClient(conf.Config.APIHost, conf.Config.APIKey, conf.Config.LogDBConf.DatabaseName, s)
	Services.Notification = NewNotificationServiceClient(conf.Config.APIHost, conf.Config.APIKey, s)
	Services.Circa = circa.NewServiceClient(conf.Config.CircaAPIHost, conf.Config.APIKey, s)
	Services.Seller = NewSellerClient(conf.Config.APIHost, conf.Config.APIKey, s)
	Services.DataHarvest = NewDataHarvestClient(conf.Config.APIHost, conf.Config.APIKey, "customer-report-client", s)
	Services.Config = NewConfigManagerClient(s)
	Services.PersonalizationRule = NewPersonalizationRuleClient(conf.Config.BuymedAPIHost, conf.Config.BuymedAPIKey, s)
	NewLocationServiceClient(s)
}

// HealthCheck is func health check all services
func (cli *gatewayClient) HealthCheck() error {
	hostname, _ := os.Hostname()
	fmt.Printf("hostname = %s, runtime go routine = %d\n", hostname, runtime.NumGoroutine())
	return nil
}
