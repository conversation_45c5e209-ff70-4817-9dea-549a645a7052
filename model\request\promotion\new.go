package promotion

import (
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"time"
)

type New struct {
	AccountID     int64                    `json:"accountId,omitempty" bson:"account_id,omitempty"`
	CampaignID    int64                    `json:"campaignId,omitempty" bson:"campaign_id,omitempty"`
	PromotionName string                   `json:"promotionName,omitempty" bson:"promotion_name,omitempty"`
	PromotionType *enum.PromotionTypeValue `json:"promotionType,omitempty" bson:"promotion_type,omitempty"`

	Banner           string     `json:"banner,omitempty" bson:"banner,omitempty"`
	Description      string     `json:"description,omitempty" bson:"description,omitempty"`
	TotalCode        int64      `json:"totalCode,omitempty" bson:"total_code,omitempty"`
	ApplyPerUser     int64      `json:"applyPerUser,omitempty" bson:"apply_per_user,omitempty"`
	StartTime        *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime          *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	StartCollectTime *time.Time `json:"startCollectTime,omitempty" bson:"start_collect_time,omitempty"`

	Rules []interface{} `json:"rules,omitempty" bson:"rules,omitempty"`
}
