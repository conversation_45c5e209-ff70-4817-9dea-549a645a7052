language: go
go:
  - 1.15.2
  - tip
matrix:
  allow_failures:
    - go: tip

notifications:
  email:
    recipients: <EMAIL>
    on_success: change
    on_failure: always

before_install:
  - go install github.com/mattn/goveralls
  - mkdir -p $GOPATH/src/gopkg.in
  - ln -s $GOPATH/src/github.com/$TRAVIS_REPO_SLUG $GOPATH/src/gopkg.in/validator.v9

# Only clone the most recent commit.
git:
  depth: 1

script:
  - go test -v -race -covermode=atomic -coverprofile=coverage.coverprofile ./...

after_success: |
  [ $TRAVIS_GO_VERSION = 1.15.2 ] &&
  goveralls -coverprofile=coverage.coverprofile -service travis-ci -repotoken $COVERALLS_TOKEN
