package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
)

func MigrateGamification(req sdk.APIRequest, resp sdk.APIResponder) error {

	var (
		offset = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit  = sdk.ParseInt64(req.GetParam("limit"), 20)
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	return resp.Respond(action.MigrateGamification(offset, limit))
}

func RemoveVoucherCacheByCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	code := req.GetParam("code")
	return resp.Respond(action.RemoveVoucherCacheByCode(code))
}
