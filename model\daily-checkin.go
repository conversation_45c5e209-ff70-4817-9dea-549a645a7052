package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CheckinConfig struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code        string     `json:"code,omitempty" bson:"code,omitempty"`
	Name        string     `json:"name,omitempty" bson:"name,omitempty"`
	Description *string    `json:"description,omitempty" bson:"description,omitempty"`
	IsActive    *bool      `json:"isActive,omitempty" bson:"is_active,omitempty"`
	StartTime   *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime     *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Priority    *int64     `json:"priority,omitempty" bson:"priority,omitempty"`
	CheckinType string     `json:"checkinType,omitempty" bson:"checkin_type,omitempty"`
	PeriodTime  int        `json:"periodTime,omitempty" bson:"period_time,omitempty"` // minutes

	BackgroundWeb    *string `json:"backgroundWeb,omitempty" bson:"background_web,omitempty"`
	BackgroundMobile *string `json:"backgroundMobile,omitempty" bson:"background_mobile,omitempty"`
	GameUrl          *string `json:"gameUrl,omitempty" bson:"game_url,omitempty"`
	TitleWeb         *string `json:"titleWeb,omitempty" bson:"title_web,omitempty"`
	TitleMobile      *string `json:"titleMobile,omitempty" bson:"title_mobile,omitempty"`

	Scope        *CheckinScope `json:"scope,omitempty" bson:"scope,omitempty"`
	ComplexQuery []*bson.M     `json:"-" bson:"$and,omitempty"`

	VersionUpdate string `json:"versionUpdate,omitempty" bson:"version_update,omitempty"`

	Items           []*CheckinItem `json:"items,omitempty" bson:"-"`           // for response only
	NextCheckinTime *time.Time     `json:"nextCheckinTime,omitempty" bson:"-"` // for response only
	LastCheckinTime *time.Time     `json:"lastCheckinTime,omitempty" bson:"-"` // for response only
}

type CheckinScope struct {
	ProvinceCodes     []string `json:"provinceCodes,omitempty" bson:"province_codes,omitempty"`
	RegionCodes       []string `json:"regionCodes,omitempty" bson:"region_codes,omitempty"`
	CustomerLevels    []string `json:"levels,omitempty" bson:"levels,omitempty"`
	CustomerScopes    []string `json:"scopes,omitempty" bson:"scopes,omitempty"`
	CustomerApplyType string   `json:"customerApplyType,omitempty" bson:"customer_apply_type,omitempty"` // ALL || MANY
}

type CheckinItem struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CheckinCode     string                `json:"checkinCode,omitempty" bson:"checkin_code,omitempty"`
	ItemCode        string                `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	ItemName        string                `json:"itemName,omitempty" bson:"item_name,omitempty"`
	Rewards         *[]*CheckinItemReward `json:"rewards,omitempty" bson:"rewards,omitempty"`
	ActionName      *string               `json:"actionName,omitempty" bson:"action_name,omitempty"`
	ActionLink      *string               `json:"actionLink,omitempty" bson:"action_link,omitempty"`
	Index           int                   `json:"index,omitempty" bson:"index,omitempty"`
	IsActive        *bool                 `json:"isActive,omitempty" bson:"is_active,omitempty"`
	CheckedQuantity int64                 `json:"checkedQuantity,omitempty" bson:"checked_quantity,omitempty"`

	TypeCheckinItem        string  `json:"typeCheckinItem,omitempty" bson:"type_checkin_item,omitempty"`
	ImageUrl               string  `json:"itemUrl,omitempty" bson:"item_url,omitempty"`
	DescriptionOtherReward *string `json:"descriptionOtherReward,omitempty" bson:"description_other_reward,omitempty"`

	CheckinStatus string `json:"checkinStatus,omitempty" bson:"-"` // for response only
	CanCheckin    bool   `json:"canCheckin,omitempty" bson:"-"`    // for response only
}

type CheckinItemReward struct {
	TypeReward            enum.CheckinItemRewardType `json:"typeReward,omitempty" bson:"type_reward,omitempty"`
	RewardDescription     string                     `json:"rewardDescription,omitempty" bson:"reward_description,omitempty"`
	Points                int64                      `json:"points,omitempty" bson:"points,omitempty"`
	PromotionID           int64                      `json:"promotionID,omitempty" bson:"promotion_id,omitempty"`
	TicketPattern         string                     `json:"ticketPattern,omitempty" bson:"ticket_pattern,omitempty"`
	VoucherPattern        *string                    `json:"voucherPattern,omitempty" bson:"voucher_pattern,omitempty"`
	TurnsRotation         int64                      `json:"turnsRotation,omitempty" bson:"turns_rotation,omitempty"`
	LuckyWheelCodes       []string                   `json:"luckyWheelCodes,omitempty" bson:"lucky_wheel_codes,omitempty"`
	VoucherID             int64                      `json:"voucherID,omitempty" bson:"-"`
	VoucherCode           string                     `json:"voucherCode,omitempty" bson:"-"`
	NumberOfDayUseVoucher *int64                     `json:"numberOfDayUseVoucher,omitempty" bson:"number_of_day_use_voucher,omitempty"`
}

// CheckinConfigDB ...
var CheckinConfigDB = &db.Instance{
	ColName:        "checkin_config",
	TemplateObject: &CheckinConfig{},
}

// InitCheckinConfigModel is func init model checkin
func InitCheckinConfigModel(s *mongo.Database) {
	CheckinConfigDB.ApplyDatabase(s)
}

// CheckinItemDB ...
var CheckinItemDB = &db.Instance{
	ColName:        "checkin_item",
	TemplateObject: &CheckinItem{},
}

// InitCheckinItemModel is func init model checkin_item
func InitCheckinItemModel(s *mongo.Database) {
	CheckinItemDB.ApplyDatabase(s)
}
