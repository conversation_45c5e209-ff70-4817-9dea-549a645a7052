package cache

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Flag ...
type Flag struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	IsProcessingSkuSaleInfo *bool `bson:"is_processing_sku_sale_info,omitempty"`
	SyncCampaign            *bool `bson:"sync_campaign,omitempty"`
	SyncVoucher             *bool `bson:"sync_voucher,omitempty"`
	SyncUserPromotion       *bool `bson:"sync_user_promotion,omitempty"`
}

// FlagDB ...
var FlagDB = &db.Instance{
	ColName:        "flag_v2",
	TemplateObject: &Flag{},
}

// InitFlagModel is func init model flag
func InitFlagModel(s *mongo.Database) {
	FlagDB.ApplyDatabase(s)

	bl := false
	_ = FlagDB.Upsert(&Flag{}, &Flag{
		IsProcessingSkuSaleInfo: &bl,
	})
}
