package api

import (
	"encoding/json"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

func CreateShareLog(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ShareLog
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.<PERSON>rror(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.<PERSON><PERSON>r(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	uaInfo := getUAInfo(req.GetHeader("User-Agent"))
	input.UaMetadata = &model.UaMetadata{}
	input.UaMetadata.OSName = uaInfo.OSName
	input.UaMetadata.OSVersion = uaInfo.OSVersion
	input.UaMetadata.ClientName = uaInfo.ClientName
	input.UaMetadata.ClientVersion = uaInfo.ClientVersion
	input.UaMetadata.Platform = uaInfo.Platform

	return resp.Respond(action.CreateShareLog(&input))
}

func ShareLogGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	query := model.ShareLog{}

	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: "INVALID",
			})
		}
	}

	return resp.Respond(action.GetListShareLog(&query, offset, limit, getTotal))
}
