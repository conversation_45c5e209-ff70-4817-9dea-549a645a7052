package enum

type PromotionStatusValue string
type PromotionStatusEnt struct {
	WAITING PromotionStatusValue
	ACTIVE  PromotionStatusValue
	FULL    PromotionStatusValue
	HIDE    PromotionStatusValue
	EXPIRED PromotionStatusValue
	DELETED PromotionStatusValue
}

var PromotionStatus = &PromotionStatusEnt{
	"WAITING",
	"ACTIVE",
	"FULL",
	"HIDE",
	"EXPIRED",
	"DELETED",
}

type PromotionTypeValue string
type PromotionTypeEnt struct {
	COMBO       PromotionTypeValue
	VOUCHERCODE PromotionTypeValue
	FREESHIP    PromotionTypeValue
}

var PromotionType = &PromotionTypeEnt{
	"COMBO",
	"VOUCHERCODE",
	"FREESHIP",
}

type ApplyTypeValue string
type applyType struct {
	AUTO   ApplyTypeValue
	MANUAL ApplyTypeValue
}

var ApplyType = &applyType{
	AUTO:   "AUTO",
	MANUAL: "MANUAL",
}

type PromotionOrganizerValue string
type PromotionOrganizerEnt struct {
	MARKETING       PromotionOrganizerValue
	INTERNAL_SELLER PromotionOrganizerValue
	SELLER_CENTER   PromotionOrganizerValue
}

var PromotionOrganizer = &PromotionOrganizerEnt{
	"MARKETING",
	"INTERNAL_SELLER",
	"SELLER_CENTER",
}

type ChargeFeeValue string
type ChargeFeeValueEnt struct {
	MARKETING       ChargeFeeValue
	INTERNAL_SELLER ChargeFeeValue
	SELLER_CENTER   ChargeFeeValue
}

var ChargeFee = &ChargeFeeValueEnt{
	"MARKETING",
	"INTERNAL_SELLER",
	"SELLER_CENTER",
}

type ScopeTypeValue string
type ScopeTypeEnt struct {
	PRODUCT          ScopeTypeValue
	CATEGORY         ScopeTypeValue
	PRODUCER         ScopeTypeValue
	SELLER           ScopeTypeValue
	INGREDIENT       ScopeTypeValue
	AREA             ScopeTypeValue
	CUSTOMER_LEVEL   ScopeTypeValue
	CUSTOMER_SCOPE   ScopeTypeValue
	PRODUCT_TAG      ScopeTypeValue
	DISPLAY_PLATFORM ScopeTypeValue

	MIN_CLIENT_VERSION ScopeTypeValue
}

var ScopeType = &ScopeTypeEnt{
	"PRODUCT",
	"CATEGORY",
	"PRODUCER",
	"SELLER",
	"INGREDIENT",
	"AREA",
	"CUSTOMER_LEVEL",
	"CUSTOMER_SCOPE",
	"PRODUCT_TAG",
	"DISPLAY_PLATFORM",
	"MIN_CLIENT_VERSION",
}

type CodeStatusValue string
type CodeStatusEnt struct {
	ACTIVE    CodeStatusValue
	INACTIVE  CodeStatusValue
	COLLECTED CodeStatusValue // no use
	USED      CodeStatusValue
	ABSORBED  CodeStatusValue // no use
	DELETED   CodeStatusValue
}

var CodeStatus = &CodeStatusEnt{
	"ACTIVE",
	"INACTIVE",
	"COLLECTED",
	"USED",
	"ABSORBED",
	"DELETED",
}

type UseTypeValue string
type useType struct {
	ADDED  UseTypeValue
	PUBLIC UseTypeValue
}

var UseType = &useType{
	"ADDED",
	"PUBLIC",
}

type CodeTypeValue string
type CodeTypeEnt struct {
	MANY CodeTypeValue
	ONE  CodeTypeValue
}

var CodeType = &CodeTypeEnt{
	"MANY",
	"ONE",
}

type CampaignStatusValue string
type CampaignStatusEnt struct {
	EXPIRED    CampaignStatusValue
	DELETED    CampaignStatusValue
	NEW        CampaignStatusValue
	UPCOMING   CampaignStatusValue
	PROCESSING CampaignStatusValue
}

var CampaignStatus = &CampaignStatusEnt{
	"EXPIRED",
	"DELETED",
	"NEW",
	"UPCOMING",
	"PROCESSING",
}

type CartStateValue string
type cartStateValue struct {
	DRAFT     CartStateValue
	CHECKOUT  CartStateValue
	PAID      CartStateValue
	CANCELLED CartStateValue
	DELETED   CartStateValue
}

var CartState = &cartStateValue{
	"draft",
	"checkout",
	"paid",
	"cancelled",
	"deleted",
}

type QuantityTypeValue string
type QuantityTypeEnt struct {
	ALL  QuantityTypeValue
	MANY QuantityTypeValue
}

var QuantityType = &QuantityTypeEnt{
	"ALL",
	"MANY",
}

type UsageTypeValue string
type UsageTypeEnt struct {
	ALONE UsageTypeValue
	MANY  UsageTypeValue
}

var UsageType = &UsageTypeEnt{
	"ALONE",
	"MANY",
}

type CustomerApplyTypeValue string
type customerApplyType struct {
	ALL  CustomerApplyTypeValue
	MANY CustomerApplyTypeValue
}

var CustomerApplyType = &customerApplyType{
	"ALL",
	"MANY",
}

type ConditionTypeValue string
type ConditionTypeEnt struct {
	PRODUCT           ConditionTypeValue
	ORDER_VALUE       ConditionTypeValue
	PRODUCT_TAG       ConditionTypeValue
	CATEGORY          ConditionTypeValue
	INGREDIENT        ConditionTypeValue
	PRODUCER          ConditionTypeValue
	NO_RULE           ConditionTypeValue
	CUSTOMER_HISTORY  ConditionTypeValue
	PAYMENT_METHOD    ConditionTypeValue
	PRODUCT_BLACKLIST ConditionTypeValue
}

var ConditionType = &ConditionTypeEnt{
	"PRODUCT",
	"ORDER_VALUE",
	"PRODUCT_TAG",
	"CATEGORY",
	"INGREDIENT",
	"PRODUCER",
	"NO_RULE",
	"CUSTOMER_HISTORY",
	"PAYMENT_METHOD",
	"PRODUCT_BLACKLIST",
}

type RewardTypeValue string
type RewardTypeEnt struct {
	PERCENTAGE            RewardTypeValue
	PERCENTAGE_ON_PRODUCT RewardTypeValue
	ABSOLUTE              RewardTypeValue
	ABSOLUTE_ON_PRODUCT   RewardTypeValue
	GIFT                  RewardTypeValue
	POINT                 RewardTypeValue
}

var RewardType = &RewardTypeEnt{
	"PERCENTAGE",
	"PERCENTAGE_ON_PRODUCT",
	"ABSOLUTE",
	"ABSOLUTE_ON_PRODUCT",
	"GIFT",
	"POINT",
}

type VoucherTypeValue string
type VoucherTypeEnt struct {
	PUBLIC  VoucherTypeValue
	PRIVATE VoucherTypeValue
}

var VoucherType = &VoucherTypeEnt{
	"PUBLIC",
	"PRIVATE",
}

type VoucherStatusValue string
type VoucherStatusEnt struct {
	HIDE    VoucherStatusValue
	WAITING VoucherStatusValue
	ACTIVE  VoucherStatusValue
	EXPIRED VoucherStatusValue
	DELETED VoucherStatusValue
}

var VoucherStatus = &VoucherStatusEnt{
	"HIDE",
	"WAITING",
	"ACTIVE",
	"EXPIRED",
	"DELETED",
}

type VoucherHistoryTypeValue string
type voucherHistoryType struct {
	USE    VoucherHistoryTypeValue
	REFUND VoucherHistoryTypeValue
}

var VoucherHistoryType = &voucherHistoryType{
	"USE",
	"REFUND",
}

type SummaryOrderInfoTypeValue string
type SummaryOrderInfoTypEnt struct {
	LAST_ORDER  SummaryOrderInfoTypeValue
	TOTAL_ORDER SummaryOrderInfoTypeValue
}

var SummaryOrderInfoTyp = &SummaryOrderInfoTypEnt{
	"LAST_ORDER",
	"TOTAL_ORDER",
}

type CampaignValueType string
type campainType struct {
	FLASH_SALE        CampaignValueType
	NORMAL            CampaignValueType
	MKT_SUPPORT_PRICE CampaignValueType
}

var CampaignType = &campainType{
	"FLASH_SALE",
	"NORMAL",
	"MKT_SUPPORT_PRICE",
}

type CampaignSaleValueType string
type campainSaleType struct {
	ABSOLUTE   CampaignSaleValueType
	PERCENTAGE CampaignSaleValueType
	PRICE      CampaignSaleValueType
	UNLIMIT    CampaignSaleValueType
}

var CampaignSaleType = &campainSaleType{
	"ABSOLUTE",
	"PERCENTAGE",
	"PRICE",
	"UNLIMIT",
}

type CampaignProductStatusType string
type campaignProductStatus struct {
	NORMAL    CampaignProductStatusType
	CANCELLED CampaignProductStatusType
}

var CampaignProductStatus = &campaignProductStatus{
	NORMAL:    "NORMAL",
	CANCELLED: "CANCELLED",
}

type operator struct {
	AND string
	OR  string
}

var Operator = &operator{
	AND: "AND",
	OR:  "OR",
}

type PlatformType string

type platform struct {
	MOBILE_WEB PlatformType
	MOBILE_APP PlatformType
	DESKTOP    PlatformType
}

var Platform = &platform{
	MOBILE_WEB: "mobile-web",
	MOBILE_APP: "mobile-app",
	DESKTOP:    "web-desktop",
}

type NotificationTagEnum string
type notificationTag struct {
	ACCOUNT   NotificationTagEnum
	ORDER     NotificationTagEnum
	PRICE     NotificationTagEnum
	PRODUCT   NotificationTagEnum
	TICKET    NotificationTagEnum
	PROMOTION NotificationTagEnum
	IMPORTANT NotificationTagEnum
	MARKETING NotificationTagEnum
}

var NotificationTag = &notificationTag{
	ACCOUNT:   "ACCOUNT",
	ORDER:     "ORDER",
	PRICE:     "PRICE",
	PRODUCT:   "PRODUCT",
	TICKET:    "TICKET",
	PROMOTION: "PROMOTION",
	IMPORTANT: "IMPORTANT",
	MARKETING: "MARKETING",
}

type SettingTypeValue string

type settingType struct {
	DEFAULT SettingTypeValue
	SEGMENT SettingTypeValue
}

var SettingType = &settingType{
	DEFAULT: "DEFAULT",
	SEGMENT: "SEGMENT",
}
