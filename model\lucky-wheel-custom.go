package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type LuckyWheelCustom struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	IsActive       *bool   `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Description    *string `json:"description,omitempty" bson:"description,omitempty"`
	Name           *string `json:"name,omitempty" bson:"name,omitempty"`
	Code           string  `json:"code,omitempty" bson:"code,omitempty"`
	LuckyWheelCode string  `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	Priority       *int64  `json:"priority,omitempty" bson:"priority,omitempty"`

	StartTime *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`

	ProvinceCodes  []string `json:"provinceCodes,omitempty" bson:"province_codes,omitempty"`
	RegionCodes    []string `json:"regionCodes,omitempty" bson:"region_codes,omitempty"`
	CustomerLevels []string `json:"levels,omitempty" bson:"levels,omitempty"`
	CustomerScopes []string `json:"scopes,omitempty" bson:"scopes,omitempty"`

	//Images
	BackgroundWeb    *string `json:"backgroundWeb,omitempty" bson:"background_web,omitempty"`
	BackgroundMobile *string `json:"backgroundMobile,omitempty" bson:"background_mobile,omitempty"`
	BannerWeb        *string `json:"bannerWeb,omitempty" bson:"banner_web,omitempty"`
	BannerMobile     *string `json:"bannerMobile,omitempty" bson:"banner_mobile,omitempty"`          //Full image for only one lucky wheel
	BannerMobileHalf *string `json:"bannerMobileHalf,omitempty" bson:"banner_mobile_half,omitempty"` //Size image for more than 2 lucky wheels
	ImageTitleWeb    *string `json:"imageTitleWeb,omitempty" bson:"image_title_web,omitempty"`
	ImageTitleMobile *string `json:"imageTitleMobile,omitempty" bson:"image_title_mobile,omitempty"`
	MainImage        *string `json:"mainImage,omitempty" bson:"main_image,omitempty"`
	MainImageMobile  *string `json:"mainImageMobile,omitempty" bson:"main_image_mobile,omitempty"`

	MainGif       *string `json:"mainGif,omitempty" bson:"main_gif,omitempty"`
	MainGifMobile *string `json:"mainGifMobile,omitempty" bson:"main_gif_mobile,omitempty"`
	RewardGif     *string `json:"rewardGif,omitempty" bson:"reward_gif,omitempty"`

	//query
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

var LuckyWheelCustomDB = &db.Instance{
	ColName:        "lucky_wheel_custom",
	TemplateObject: &LuckyWheelCustom{},
}

// InitLuckyWheelCustomModel is func init model
func InitLuckyWheelCustomModel(s *mongo.Database) {
	LuckyWheelCustomDB.ApplyDatabase(s)
}

// db.getCollection("lucky_wheel_custom").ensureIndex({"code": 1},{background:true, unique: true})
// db.getCollection("lucky_wheel_custom").ensureIndex({"lucky_wheel_code": 1, "is_active": 1, "start_time": 1, "end_time": 1, "province_codes": 1},{background:true})
