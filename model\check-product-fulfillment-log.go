package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type CheckProductFulfillmentLog struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code            string                      `json:"code,omitempty" bson:"code,omitempty"`
	CampaignID      int64                       `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	CampaignCode    string                      `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	CampaignFulfill *float64                    `json:"campaignFulfill,omitempty" bson:"campaign_fulfill,omitempty"` //ti le fulfill toi thieu tai thoi diem kiem tra
	StartTime       *time.Time                  `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime         *time.Time                  `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Status          enum.CampaignLogStatusValue `json:"status,omitempty" bson:"status,omitempty"`

	Type enum.CheckProductFulfillmentType `json:"type,omitempty" bson:"type,omitempty"`

	Total                           int `json:"total,omitempty" bson:"total,omitempty"`
	NumberOfActiveCampaignProduct   int `json:"numberOfActiveCampaignProduct,omitempty" bson:"number_of_active_campaign_product,omitempty"`     //so san pham dat fulfillment toi thieu trong campaign
	NumberOfInactiveCampaignProduct int `json:"NumberOfInactiveCampaignProduct,omitempty" bson:"number_of_inactive_campaign_product,omitempty"` //so san pham dang active bi tat vi khong du fulfillment

	FailReason string `json:"failReason,omitempty" bson:"fail_reason,omitempty"`
	AccountID  int64  `json:"accountID,omitempty" bson:"account_id,omitempty"` // action by

	// query
	ComplexQuery    []*bson.M  `json:"-" bson:"$and,omitempty"`
	CreatedTimeFrom *time.Time `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo   *time.Time `json:"createdTimeTo,omitempty" bson:"-"`
}

// CheckProductFulfillmentLogDB is instance db
var CheckProductFulfillmentLogDB = &db.Instance{
	ColName:        "check_product_fulfillment_log",
	TemplateObject: &CheckProductFulfillmentLog{},
}

// InitCheckProductFulfillmentLogModel is func init model level
func InitCheckProductFulfillmentLogModel(s *mongo.Database) {
	CheckProductFulfillmentLogDB.ApplyDatabase(s)
}
