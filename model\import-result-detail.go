package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ImportResultDetail struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code             string `json:"code,omitempty" bson:"code,omitempty"`
	Status           string `json:"status,omitempty" bson:"status,omitempty"`
	ImportResultCode string `json:"importResultCode,omitempty" bson:"import_result_code,omitempty"`
	ImportJobCode    string `json:"importJobCode,omitempty" bson:"import_job_code,omitempty"`
	Request          string `json:"request,omitempty" bson:"request,omitempty"`
	Response         string `json:"response,omitempty" bson:"response,omitempty"`

	ComplexQuery      []*bson.M  `json:"-" bson:"$and,omitempty"`
	CreatedTimeFrom   *time.Time `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo     *time.Time `json:"createdTimeTo,omitempty" bson:"-"`
	CompletedTimeFrom *time.Time `json:"completedTimeFrom,omitempty" bson:"-"`
	CompletedTimeTo   *time.Time `json:"completedTimeTo,omitempty" bson:"-"`

	IndexImport     int `json:"indexImport,omitempty" bson:"index_import,omitempty"`
	ProccessingTime int `json:"proccessingTime,omitempty" bson:"proccessing_time,omitempty"`
}

// ImportResultDetailDB ...
var ImportResultDetailDB = &db.Instance{
	ColName:        "import_result_detail",
	TemplateObject: &ImportResultDetail{},
}

// InitImportResultDetailModel is func init model import-result-detail
func InitImportResultDetailModel(s *mongo.Database) {
	ImportResultDetailDB.ApplyDatabase(s)

	//t := true
	//_ = ImportResultDetailDB.CreateIndex(bson.D{
	//	primitive.E{Key: "code", Value: 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
}
