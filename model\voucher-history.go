package model

import (
	"go.mongodb.org/mongo-driver/bson"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type VoucherHistory struct {
	ID          primitive.ObjectID           `json:"-" bson:"_id,omitempty" `
	CreatedTime *time.Time                   `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CustomerID  int64                        `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	OrderID     int64                        `json:"orderID,omitempty" bson:"order_id,omitempty"`
	Usage       int64                        `json:"usage,omitempty" bson:"usage,omitempty"`
	Type        enum.VoucherHistoryTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	Note        string                       `json:"note,omitempty" bson:"note,omitempty"`
	Voucher     *Voucher                     `json:"voucher,omitempty" bson:"voucher,omitempty"`
	Promotion   *Promotion                   `json:"promotion,omitempty" bson:"promotion,omitempty"`
	Order       *Order                       `json:"order,omitempty" bson:"-"`

	DateFrom     *time.Time `json:"timeFrom,omitempty" bson:"-"`
	DateTo       *time.Time `json:"timeTo,omitempty" bson:"-"`
	ComplexQuery []*bson.M  `json:"-" bson:"$and,omitempty"`
}

type VoucherHistoryViewWeb struct {
	ID          primitive.ObjectID           `json:"-" bson:"_id,omitempty" `
	CreatedTime *time.Time                   `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CustomerID  int64                        `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	OrderID     int64                        `json:"orderID,omitempty" bson:"order_id,omitempty"`
	Usage       int64                        `json:"usage,omitempty" bson:"usage,omitempty"`
	Type        enum.VoucherHistoryTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	Note        string                       `json:"note,omitempty" bson:"note,omitempty"`
	Voucher     *VoucherViewWebOnly          `json:"voucher,omitempty" bson:"voucher,omitempty"`
	Order       *Order                       `json:"order,omitempty" bson:"-"`
}

var VoucherHistoryDB = &db.Instance{
	ColName:        "voucher_history",
	TemplateObject: &VoucherHistory{},
}

// InitVoucherHistoryModel is func init model
func InitVoucherHistoryModel(s *mongo.Database) {
	VoucherHistoryDB.ApplyDatabase(s)

	// t := true

	// VoucherHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "voucher_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// VoucherHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "promotion_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// VoucherHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "customer_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
