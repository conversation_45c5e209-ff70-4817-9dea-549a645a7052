package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CustomerLuckyWheelCombo struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	AccountID     int64  `json:"accountId,omitempty" bson:"account_id,omitempty"`
	ComboCode     string `json:"comboCode,omitempty" bson:"combo_code,omitempty"`
	UsedQuantity  int64  `json:"usedQuantity,omitempty" bson:"used_quantity,omitempty"`
	VersionUpdate string `json:"versionUpdate,omitempty" bson:"version_update,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`

	Pieces  map[string]CustomerLuckyWheelComboPiece `json:"pieces,omitempty" bson:"pieces,omitempty"`
	Rewards []CustomerLuckyWheelComboReward         `json:"rewards,omitempty" bson:"rewards,omitempty"`
}

type CustomerLuckyWheelComboPiece struct {
	PieceCode    string `json:"pieceCode,omitempty" bson:"piece_code,omitempty"`
	Quantity     int64  `json:"quantity,omitempty" bson:"quantity,omitempty"`
	UsedQuantity int64  `json:"usedQuantity,omitempty" bson:"used_quantity,omitempty"`
}

type CustomerLuckyWheelComboReward struct {
	VoucherCode       string `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	Ticket            string `json:"ticket,omitempty" bson:"ticket,omitempty"`
	TurnsRotation     int64  `json:"turnsRotation,omitempty" bson:"turns_rotation,omitempty"`
	Points            int64  `json:"points,omitempty" bson:"points,omitempty"`
	RewardDescription string `json:"rewardDescription,omitempty" bson:"reward_description,omitempty"`
}

var CustomerLuckyWheelComboDB = &db.Instance{
	ColName:        "customer_lucky_wheel_combo",
	TemplateObject: &CustomerLuckyWheelCombo{},
}

// InitCustomerLuckyWheelComboModel is func init model
func InitCustomerLuckyWheelComboModel(s *mongo.Database) {
	CustomerLuckyWheelComboDB.ApplyDatabase(s)
	//t := true
	//CustomerLuckyWheelComboDB.CreateIndex(bson.D{
	//	primitive.E{Key: "account_id", Value: 1},
	//	primitive.E{Key: "combo_code", Value: 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
}

type LuckyWheelComboLog struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	AccountID      int64  `json:"accountId,omitempty" bson:"account_id,omitempty"`
	CustomerPhone  string `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`
	LuckyWheelCode string `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	ComboCode      string `json:"comboCode,omitempty" bson:"combo_code,omitempty"`
	Message        string `json:"message,omitempty" bson:"message,omitempty"`
	MessageHidden  string `json:"messageHidden,omitempty" bson:"message_hidden,omitempty"`
	ComboImageUrl  string `json:"comboImageUrl,omitempty" bson:"combo_image_url,omitempty"`
	IsHasGift      bool   `json:"isHasGift,omitempty" bson:"is_has_gift,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

var LuckyWheelComboLogDB = &db.Instance{
	ColName:        "lucky_wheel_combo_log",
	TemplateObject: &LuckyWheelComboLog{},
}

// InitLuckyWheelComboLogModel is func init model
func InitLuckyWheelComboLogModel(s *mongo.Database) {
	LuckyWheelComboLogDB.ApplyDatabase(s)
	//t := true
	//LuckyWheelComboLogDB.CreateIndex(bson.D{
	//	primitive.E{Key: "account_id", Value: 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
	//LuckyWheelComboLogDB.CreateIndex(bson.D{
	//	primitive.E{Key: "combo_code", Value: 1},
	//}, &options.IndexOptions{
	//	Background: &t,
	//})
}

// db.getCollection("lucky_wheel_combo_log").ensureIndex({"lucky_wheel_code": 1},{background:true, unique: true})
// db.getCollection("lucky_wheel_combo_log").ensureIndex({"combo_code": 1},{background:true, unique: true})
// db.getCollection("lucky_wheel_combo_log").ensureIndex({"account_id": 1},{background:true, unique: true})
