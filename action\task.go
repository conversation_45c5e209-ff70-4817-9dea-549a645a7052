package action

import (
	"fmt"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson"
)

func DelayNotifyConsumer(item *job.JobItem) (errRes error) {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}

	var notifyData model.Notification
	err = bson.Unmarshal(data, &notifyData)
	if err != nil {
		return err
	}

	errNoti := client.Services.Notification.CreateNotification(&notifyData)
	if errNoti != nil && errNoti.Status != common.APIStatus.Ok {
		return fmt.Errorf(errNoti.Message)
	}
	return nil
}
