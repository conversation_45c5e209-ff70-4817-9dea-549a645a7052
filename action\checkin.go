package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateCheckinConfig(req *model.CheckinConfig) *common.APIResponse {
	req.Code = model.GenCodeWithTime()
	req.VersionUpdate = model.GenCodeWithTime()
	return model.CheckinConfigDB.Create(req)
}

func CreateCheckinItem(req *model.CheckinItem) *common.APIResponse {
	return model.CheckinItemDB.Create(req)
}

func UpdateCheckinConfig(req *model.CheckinConfig) *common.APIResponse {
	if req.Code == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Code is required",
			ErrorCode: "CODE_REQUIRED",
		}
	}
	if qConfig := model.CheckinConfigDB.QueryOne(model.CheckinConfig{Code: req.Code}); qConfig.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Config not found",
			ErrorCode: "CONFIG_NOT_FOUND",
		}
	}
	curVersion := req.VersionUpdate
	req.VersionUpdate = model.GenCodeWithTime()
	return model.CheckinConfigDB.UpdateOne(model.CheckinConfig{Code: req.Code, VersionUpdate: curVersion}, req)
}

func UpdateCheckinItem(req *model.CheckinItem) *common.APIResponse {
	if req.ItemCode == "" || req.CheckinCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "ItemCode and CheckinCode is required",
			ErrorCode: "ITEM_CODE_AND_CHECKIN_CODE_REQUIRED",
		}
	}
	qItem := model.CheckinItemDB.QueryOne(model.CheckinItem{ItemCode: req.ItemCode, CheckinCode: req.CheckinCode})
	if qItem.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Item not found",
			ErrorCode: "ITEM_NOT_FOUND",
		}
	}
	//curItem := qItem.Data.([]*model.CheckinItem)[0]
	//qCheckinLog := model.CheckinLogDB.QueryOne(bson.M{"checkin_code": curItem.CheckinCode, "item_index" : bson.M{"$gte": curItem.Index}})
	//if qCheckinLog.Status == common.APIStatus.Ok {
	//	return &common.APIResponse{
	//		Status:    common.APIStatus.Invalid,
	//		Message:   "Không thể cập nhật item này do chương trình đã chạy",
	//		ErrorCode: "ITEM_LOCKED",
	//	}
	//}

	return model.CheckinItemDB.UpdateOne(model.CheckinItem{ItemCode: req.ItemCode, CheckinCode: req.CheckinCode}, req)
}

func GetCheckinConfig(code string) *common.APIResponse {
	return model.CheckinConfigDB.QueryOne(model.CheckinConfig{
		Code: code,
	})
}

func GetCheckinConfigList(req *model.CheckinConfig, offset, limit int64, getTotal bool) *common.APIResponse {
	res := model.CheckinConfigDB.Query(req, offset, limit, &primitive.M{"_id": -1})
	if res.Status != common.APIStatus.Ok {
		return res
	}
	if getTotal {
		res.Total = model.CheckinConfigDB.Count(req).Total
	}
	return res
}

func GetCheckinItemList(req *model.CheckinItem, offset, limit int64, getTotal bool) *common.APIResponse {
	res := model.CheckinItemDB.Query(req, offset, limit, &primitive.M{"_id": -1})
	if res.Status != common.APIStatus.Ok {
		return res
	}
	if getTotal {
		res.Total = model.CheckinItemDB.Count(req).Total
	}
	return res
}

func GetSelfCheckin(acc *model.Account) *common.APIResponse {
	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "CUSTOMER_NOT_FOUND",
			Message:   "Không tìm thấy thông tin khách hàng",
		}
	}
	qCheckin := model.CheckinConfigDB.Query(bson.M{
		"is_active":            true,
		"start_time":           bson.M{"$lte": time.Now()},
		"end_time":             bson.M{"$gte": time.Now()},
		"scope.province_codes": bson.M{"$in": []string{customer.ProvinceCode, "all"}},
	}, 0, 0, &primitive.M{"priority": -1})
	if qCheckin.Status != common.APIStatus.Ok {
		return qCheckin
	}
	checkinConfigs := qCheckin.Data.([]*model.CheckinConfig)
	mapCheckinConfig := make(map[string]*model.CheckinConfig)
	checkinConfigNeedCheckCustomer := make([]string, 0)
	mapCustomerCheckin := make(map[string]bool)
	for _, ck := range checkinConfigs {
		mapCheckinConfig[ck.Code] = ck
		if ck.Scope == nil || ck.Scope.CustomerApplyType == "" || ck.Scope.CustomerApplyType == "ALL" {
			continue
		}
		checkinConfigNeedCheckCustomer = append(checkinConfigNeedCheckCustomer, ck.Code)
	}
	if len(checkinConfigNeedCheckCustomer) > 0 {
		qCheckinCustomer := model.CheckinCustomerDB.Query(bson.M{
			"customer_id":  customer.CustomerID,
			"checkin_code": bson.M{"$in": checkinConfigNeedCheckCustomer},
			"status":       "ACTIVE",
		}, 0, 0, nil)
		if qCheckinCustomer.Status == common.APIStatus.Ok {
			for _, ck := range qCheckinCustomer.Data.([]*model.CheckinCustomer) {
				mapCustomerCheckin[ck.CheckinCode] = true
			}
		}
	}
	var checkinConfig *model.CheckinConfig
	for _, ck := range checkinConfigs {
		isMatchCustomer := false
		isMatchScope := false
		isMatchLevel := false
		if ck.Scope == nil || ck.Scope.CustomerApplyType == "" || ck.Scope.CustomerApplyType == "ALL" {
			isMatchCustomer = true
		} else {
			if _, ok := mapCustomerCheckin[ck.Code]; ok {
				isMatchCustomer = true
			}
		}
		for _, level := range ck.Scope.CustomerLevels {
			if level == "all" || level == customer.Level {
				isMatchLevel = true
			}
		}
		for _, scope := range ck.Scope.CustomerScopes {
			if scope == "all" || scope == customer.Scope {
				isMatchScope = true
			}
		}
		if isMatchCustomer && isMatchLevel && isMatchScope {
			checkinConfig = ck
			break
		}
	}
	if checkinConfig == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			ErrorCode: "CHECKIN_CONFIG_NOT_FOUND",
			Message:   "Không tìm thấy cấu hình checkin",
		}
	}
	qItem := model.CheckinItemDB.Query(model.CheckinItem{
		CheckinCode: checkinConfig.Code,
		IsActive:    utils.ParseBoolToPointer(true),
	}, 0, 0, &primitive.M{"index": 1})
	if qItem.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			ErrorCode: "CHECKIN_CONFIG_NOT_FOUND",
			Message:   "Không tìm thấy cấu hình checkin",
		}
	}
	checkinConfig.Items = qItem.Data.([]*model.CheckinItem)

	qCheckinLog := model.CheckinLogDB.Query(model.CheckinLog{
		CheckinCode: checkinConfig.Code,
		CustomerID:  customer.CustomerID,
	}, 0, 0, nil)
	if qCheckinLog.Status == common.APIStatus.Ok {
		mapCheckinLog := make(map[string]*model.CheckinLog)
		for _, log := range qCheckinLog.Data.([]*model.CheckinLog) {
			mapCheckinLog[log.ItemCode] = log
		}
		slotCheckin := 0
		for _, item := range checkinConfig.Items {
			if log, ok := mapCheckinLog[item.ItemCode]; ok {
				item.CheckinStatus = "CHECKED"
				item.CanCheckin = false
				checkinConfig.LastCheckinTime = log.CreatedTime
			} else {
				item.CheckinStatus = "UNCHECKED"
				if slotCheckin == 0 {
					slotCheckin = item.Index
					now := time.Now()
					if checkinConfig.PeriodTime/60 < 24 {
						nextCheckinTime := checkinConfig.LastCheckinTime.Add(time.Duration(checkinConfig.PeriodTime) * time.Minute)
						checkinConfig.NextCheckinTime = &nextCheckinTime
						if now.After(nextCheckinTime) {
							item.CanCheckin = true
						}
					} else {
						lastCheckinTime := checkinConfig.LastCheckinTime
						newDay := (checkinConfig.PeriodTime / 60 / 24) - 1
						if lastCheckinTime.Hour() >= 17 {
							newDay = checkinConfig.PeriodTime / 60 / 24
						}
						nextCheckinTime := time.Date(lastCheckinTime.Year(), lastCheckinTime.Month(), lastCheckinTime.Day()+newDay, 17, 0, 0, 0, now.Location())
						checkinConfig.NextCheckinTime = &nextCheckinTime
						if now.After(nextCheckinTime) {
							item.CanCheckin = true
						}
					}
				}
			}
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    []*model.CheckinConfig{checkinConfig},
		Message: "Lấy thông tin checkin thành công",
	}
}

// SelfCheckin
// 1. get customer from account
// 2. get checkin config & validate
// 3. get checkin item & validate
// 4. create checkin log
// 5. handle reward
// 6. return response
// /*
func SelfCheckin(acc *model.Account, code, itemCode string) *common.APIResponse {
	// get customer from account
	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "CUSTOMER_NOT_FOUND",
			Message:   "Không tìm thấy thông tin khách hàng",
		}
	}
	// get checkin config & validate
	qCheckin := model.CheckinConfigDB.QueryOne(bson.M{
		"code":                 code,
		"is_active":            true,
		"start_time":           bson.M{"$lte": time.Now()},
		"end_time":             bson.M{"$gte": time.Now()},
		"scope.province_codes": bson.M{"$in": []string{customer.ProvinceCode, "all"}},
	})
	if qCheckin.Status != common.APIStatus.Ok {
		return qCheckin
	}
	checkinConfig := qCheckin.Data.([]*model.CheckinConfig)[0]
	if checkinConfig.Scope != nil {
		if checkinConfig.Scope.CustomerApplyType != "" && checkinConfig.Scope.CustomerApplyType != "ALL" {
			qCheckinCustomer := model.CheckinCustomerDB.QueryOne(bson.M{
				"customer_id":  customer.CustomerID,
				"checkin_code": checkinConfig.Code,
				"status":       "ACTIVE",
			})
			if qCheckinCustomer.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    common.APIStatus.NotFound,
					ErrorCode: "CHECKIN_CONFIG_NOT_FOUND",
					Message:   "Không tìm thấy chương trình điểm danh",
				}
			}
		}
		isMatchLevel := false
		isMatchScope := false
		for _, level := range checkinConfig.Scope.CustomerLevels {
			if level == "all" || level == customer.Level {
				isMatchLevel = true
				break
			}
		}
		for _, scope := range checkinConfig.Scope.CustomerScopes {
			if scope == "all" || scope == customer.Scope {
				isMatchScope = true
				break
			}
		}
		if !(isMatchLevel && isMatchScope) {
			return &common.APIResponse{
				Status:    common.APIStatus.NotFound,
				ErrorCode: "CHECKIN_CONFIG_NOT_FOUND",
				Message:   "Không tìm thấy chương trình điểm danh",
			}
		}
	}
	// get checkin item & validate
	var checkinItem *model.CheckinItem
	var lastSlotCheckinIndex int // get last slot checkin - default 0
	qCheckinLog := model.CheckinLogDB.Query(bson.M{
		"checkin_code": checkinConfig.Code,
		"customer_id":  customer.CustomerID,
	}, 0, 0, &primitive.M{"item_index": -1})

	if qCheckinLog.Status == common.APIStatus.Ok {
		lastSlotCheckin := qCheckinLog.Data.([]*model.CheckinLog)[0]
		checkinConfig.LastCheckinTime = lastSlotCheckin.CreatedTime
		now := time.Now()
		isValidCheckinTime := true
		if checkinConfig.PeriodTime/60 < 24 {
			nextCheckinTime := checkinConfig.LastCheckinTime.Add(time.Duration(checkinConfig.PeriodTime) * time.Minute)
			checkinConfig.NextCheckinTime = &nextCheckinTime
			if !now.After(nextCheckinTime) {
				isValidCheckinTime = false
			}
		} else {
			lastCheckinTime := checkinConfig.LastCheckinTime
			newDay := (checkinConfig.PeriodTime / 60 / 24) - 1
			if lastCheckinTime.Hour() >= 17 {
				newDay = checkinConfig.PeriodTime / 60 / 24
			}
			nextCheckinTime := time.Date(lastCheckinTime.Year(), lastCheckinTime.Month(), lastCheckinTime.Day()+newDay, 17, 0, 0, 0, now.Location())
			if !now.After(nextCheckinTime) {
				isValidCheckinTime = false
			}
		}
		if !isValidCheckinTime {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Bạn đã điểm danh trong khoảng thời gian này",
				ErrorCode: "CHECKIN_TIME_INVALID",
			}
		}
		lastSlotCheckinIndex = lastSlotCheckin.ItemIndex
	}
	qCheckinItem := model.CheckinItemDB.Query(bson.M{
		"index":        bson.M{"$gt": lastSlotCheckinIndex},
		"checkin_code": checkinConfig.Code,
		"is_active":    true,
	}, 0, 0, &primitive.M{"index": 1})
	if qCheckinItem.Status == common.APIStatus.Ok {
		checkinItem = qCheckinItem.Data.([]*model.CheckinItem)[0]
	} else {
		return qCheckinItem
	}

	checkinLogRes := model.CheckinLogDB.Create(&model.CheckinLog{
		CheckinCode:     checkinConfig.Code,
		CustomerID:      customer.CustomerID,
		AccountID:       acc.AccountID,
		ItemCode:        checkinItem.ItemCode,
		ItemIndex:       checkinItem.Index,
		ImageUrl:        checkinItem.ImageUrl,
		TypeCheckinItem: checkinItem.TypeCheckinItem,
		ItemName:        checkinItem.ItemName,
	})
	if checkinLogRes.Status != common.APIStatus.Ok {
		return checkinLogRes
	}

	// todo handle reward
	rewardStrs := make([]string, 0)
	if checkinItem.Rewards != nil {
		for _, reward := range *checkinItem.Rewards {
			switch reward.TypeReward {
			case enum.CheckinItemReward.TICKET_PATTERN:
				ticket := strings.ReplaceAll(reward.TicketPattern, "#", model.GenShortCodeWithTime())
				reward.TicketPattern = ticket
				rewardStrs = append(rewardStrs, "1 phiếu dự thưởng "+ticket)
			case enum.CheckinItemReward.VOUCHER:
				patternVoucher := "CHECKIN_#"
				if reward.VoucherPattern != nil {
					patternVoucher = *reward.VoucherPattern
				}
				voucherID, voucherCode := model.GenVoucherID()
				voucherCode = strings.ReplaceAll(patternVoucher, "#", voucherCode)
				reward.VoucherCode = voucherCode
				reward.VoucherID = voucherID
				rewardStrs = append(rewardStrs, "1 "+checkinItem.ItemName+" - "+voucherCode)
			case enum.CheckinItemReward.POINTS:
				rewardStrs = append(rewardStrs, fmt.Sprintf("%d điểm tích lũy", reward.Points))
			case enum.CheckinItemReward.TURNS:
				rewardStrs = append(rewardStrs, fmt.Sprintf("%d lượt quay", reward.TurnsRotation))
			case enum.CheckinItemReward.OTHER:
				rewardStrs = append(rewardStrs, reward.RewardDescription)
			}
		}
	}

	model.CheckinItemDB.UpdateOneWithOption(bson.M{"item_code": checkinItem.ItemCode, "checkin_code": code}, bson.M{
		"$inc": bson.M{"checked_quantity": 1},
	})
	if checkinItem.Rewards != nil {
		for _, reward := range *checkinItem.Rewards {
			switch reward.TypeReward {
			case enum.CheckinItemReward.TICKET_PATTERN:
				// not implement
			case enum.CheckinItemReward.VOUCHER:
				voucherRes := CreateVoucherByPromotionID(nil, customer.CustomerID, reward.PromotionID, reward.VoucherCode, reward.VoucherID, reward.NumberOfDayUseVoucher)
				if voucherRes.Status == common.APIStatus.Ok {
					voucher := voucherRes.Data.([]*model.Voucher)[0]
					model.CheckinCustomerDB.UpdateOneWithOption(bson.M{"account_id": acc.AccountID, "checkin_code": code}, bson.M{
						"$push": bson.M{"reward": model.CustomerCheckinReward{
							ItemCode:    checkinItem.ItemCode,
							VoucherCode: voucher.Code,
						},
						}})
					_ = client.Services.Notification.CreateNotification(&model.Notification{
						Username:     customer.Username,
						UserID:       customer.AccountID,
						ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
						Topic:        "ANNOUNCEMENT",
						Title:        fmt.Sprintf("Bạn nhận được mã giảm giá %s từ chương trình %s", voucher.Code, checkinConfig.Name),
						Link:         fmt.Sprint("/promo-codes"),
						Tags:         []enum.NotificationTagEnum{enum.NotificationTag.PROMOTION, enum.NotificationTag.IMPORTANT},
					})
				}
			case enum.CheckinItemReward.POINTS:
				errIncrPoint := client.Services.Customer.UpdatePoint(acc.AccountID, reward.Points, "CHECKIN")
				if errIncrPoint == nil {
					model.CheckinCustomerDB.UpdateOneWithOption(bson.M{"account_id": acc.AccountID, "checkin_code": code}, bson.M{
						"$push": bson.M{"reward": model.CustomerCheckinReward{
							ItemCode: checkinItem.ItemCode,
							Points:   reward.Points,
						}},
					})
					_ = client.Services.Notification.CreateNotification(&model.Notification{
						Username:     customer.Username,
						UserID:       customer.AccountID,
						ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
						Topic:        "ANNOUNCEMENT",
						Title:        fmt.Sprintf("Bạn nhận được %d điểm tích lũy từ chương trình %s", reward.Points, checkinConfig.Name),
						Link:         fmt.Sprint("/users/loyalty_points"),
						Tags:         []enum.NotificationTagEnum{enum.NotificationTag.PROMOTION, enum.NotificationTag.IMPORTANT},
					})
				}
			case enum.CheckinItemReward.TURNS:
				mapLKName := make(map[string]string)
				if reward.LuckyWheelCodes != nil && len(reward.LuckyWheelCodes) > 0 {
					qLK := model.LuckyWheelDB.Query(bson.M{
						"code": bson.M{"$in": reward.LuckyWheelCodes},
					}, 0, 0, nil)
					if qLK.Status == common.APIStatus.Ok {
						for _, lk := range qLK.Data.([]*model.LuckyWheel) {
							mapLKName[lk.Code] = lk.Name
						}
					}
				}
				for _, lkCode := range reward.LuckyWheelCodes {
					qInc := model.CustomerLuckyWheelDB.UpdateOneWithOption(bson.M{"account_id": customer.AccountID, "lucky_wheel_code": lkCode}, bson.M{
						"$inc": bson.M{"quantity": reward.TurnsRotation}}, &options.FindOneAndUpdateOptions{Upsert: utils.ParseBoolToPointer(true)})
					if qInc.Status == common.APIStatus.Ok {
						_ = client.Services.Notification.CreateNotification(&model.Notification{
							Username:     customer.Username,
							UserID:       customer.AccountID,
							ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
							Topic:        "ANNOUNCEMENT",
							Title:        fmt.Sprintf("Bạn nhận được %d lượt từ chương trình %s để tham gia chương trình %s", reward.TurnsRotation, checkinConfig.Name, mapLKName[lkCode]),
							Link:         fmt.Sprint("/lucky-wheel?wheelCode=" + lkCode),
							Tags:         []enum.NotificationTagEnum{enum.NotificationTag.PROMOTION, enum.NotificationTag.IMPORTANT},
						})
					}
				}
				model.CheckinCustomerDB.UpdateOneWithOption(bson.M{"account_id": acc.AccountID, "checkin_code": code}, bson.M{
					"$push": bson.M{"reward": model.CustomerCheckinReward{
						ItemCode: checkinItem.ItemCode,
						Points:   reward.TurnsRotation,
					}},
				})
			case enum.CheckinItemReward.OTHER:
				model.CheckinCustomerDB.UpdateOneWithOption(bson.M{"account_id": acc.AccountID, "checkin_code": code}, bson.M{
					"$push": bson.M{"reward": model.CustomerCheckinReward{
						ItemCode:          checkinItem.ItemCode,
						RewardDescription: reward.RewardDescription,
					}},
				})
				_ = client.Services.Notification.CreateNotification(&model.Notification{
					Username:     customer.Username,
					UserID:       customer.AccountID,
					ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
					Topic:        "ANNOUNCEMENT",
					Title:        fmt.Sprintf("Bạn nhận được %s từ chương trình %s", reward.RewardDescription, checkinConfig.Name),
					Link:         fmt.Sprint("/"),
					Tags:         []enum.NotificationTagEnum{enum.NotificationTag.PROMOTION, enum.NotificationTag.IMPORTANT},
				})
			}
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []*model.CheckinItem{checkinItem},
		Message: func() string {
			if len(rewardStrs) > 0 {
				return "Quý khách đã nhận được " + "<strong>" + strings.Join(rewardStrs, ", ") + "</strong>"
			}
			return "Điểm danh thành công!"
		}(),
	}
}

func GetDailyCheckinLog(acc *model.Account, query *model.CheckinLog, offset, limit int64, getTotal bool) *common.APIResponse {
	res := model.CheckinLogDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if res.Status != common.APIStatus.Ok {
		return res
	}
	if getTotal {
		total := model.CheckinLogDB.Count(query).Total
		res.Total = total
	}
	return res
}
