package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetListShareLog(query *model.ShareLog, offset, limit int64, getTotal bool) *common.APIResponse {
	res := model.ShareLogDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if res.Status != common.APIStatus.Ok {
		return res
	}
	if getTotal {
		res.Total = model.ShareLogDB.Count(query).Total
	}
	return res
}

func CreateShareLog(shareLog *model.ShareLog) *common.APIResponse {
	shareLog.Code = model.GenCodeWithTime()
	return model.ShareLogDB.Create(shareLog)
}
