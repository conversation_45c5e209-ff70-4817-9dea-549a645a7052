package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type GamificationResultLog struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Status               string      `json:"status,omitempty" bson:"status,omitempty"`
	Action               string      `json:"action,omitempty" bson:"action,omitempty"`
	Key                  string      `json:"key,omitempty" bson:"key,omitempty"`
	Type                 string      `json:"type,omitempty" bson:"type,omitempty"`
	RequestData          interface{} `json:"requestData,omitempty" bson:"request_data,omitempty"`
	ScoreData            interface{} `json:"scoreData,omitempty" bson:"score_data,omitempty"`
	CustomerID           int64       `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	AccountID            int64       `json:"accountId,omitempty" bson:"account_id,omitempty"`
	CustomerPhone        string      `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`
	GamificationDetailID int64       `json:"gamificationDetailId,omitempty" bson:"gamification_detail_id,omitempty"`
}

var GamificationResultLogDB = &db.Instance{
	ColName:        "gamification_result_log",
	TemplateObject: &GamificationResultLog{},
}

func InitGamificationResultLogModel(s *mongo.Database) {
	GamificationResultLogDB.ApplyDatabase(s)
}
