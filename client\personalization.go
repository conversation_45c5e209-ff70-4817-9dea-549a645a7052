package client

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

const (
	pathPostSegmentationVoucher   = "/personalization-engine/personalization-rule/v1/segmentation-voucher"
	pathUpdateSegmentationVoucher = "/personalization-engine/personalization-rule/v1/segmentation-voucher/status"
)

type personalizationRuleClient struct {
	svc     *client.RestClient
	headers map[string]string
}

func NewPersonalizationRuleClient(apiHost, apiKey string, s *mongo.Database) *personalizationRuleClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}

	client := &personalizationRuleClient{
		svc: client.NewRESTClient(
			apiHost,
			"personalization_rule_client",
			DEFAULT_TIMEOUT,
			DEFAULT_RETRY_TIME,
			DEFAULT_WAIT_TIME,
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	client.svc.SetDBLog(s)
	return client
}

// CreateSegmentationVoucher ...
func (cli *personalizationRuleClient) CreateSegmentationVoucher(segmentVoucher *model.SegmentationVoucher) *common.APIResponse {
	if segmentVoucher == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "SegmentationVoucher is nil",
			ErrorCode: "SEGMENTATION_VOUCHER_IS_NIL",
		}
	}

	params := map[string]string{}

	_, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, segmentVoucher, pathPostSegmentationVoucher, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_SEGMENTATION_VOUCHER",
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}

// DeleteJobSegmentationVoucher ...
func (cli *personalizationRuleClient) UpdateStatusSegmentationVoucher(segmentVoucher *model.SegmentationVoucher) *common.APIResponse {
	if segmentVoucher == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "SegmentationVoucher is nil",
			ErrorCode: "SEGMENTATION_VOUCHER_IS_NIL",
		}
	}

	params := map[string]string{}

	_, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, segmentVoucher, pathUpdateSegmentationVoucher, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_SEGMENTATION_VOUCHER",
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}
