package action

import (
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
)

/*
  voucher conditon type: product, order, customer, payment, product_tag, product_blacklist
  condition type: AND, OR
  TODO: with Order condition, force condition type is AND
*/

type voucherConditionResult struct {
	cart          *model.Cart                                     // cart info
	orCondition   map[enum.ConditionTypeValue]model.PromotionType // or condition
	andConditions map[enum.ConditionTypeValue]model.PromotionType // and condition
	customer      *model.Customer                                 // customer info
	applyDiscount *model.ApplyDiscountOptions
	organization  enum.PromotionOrganizerValue // tổ chức tổ chức chương trình
	sellerCodes   []string                     // danh sách mã seller
	storeCode     *string                      // mã cửa hàng
	sellerName    string                       // tên seller

	customerExperience *model.CustomerExperience
}

type ConditionMessages []*model.ConditionMessage

type NearestAvailableCondition struct {
	current *int
	target  *int
}

var mapVoucherConditionType = map[enum.ConditionTypeValue]string{
	enum.ConditionType.CUSTOMER_HISTORY:  "Lịch sử khách hàng",
	enum.ConditionType.PRODUCT:           "Sản phẩm",
	enum.ConditionType.ORDER_VALUE:       "Đơn hàng",
	enum.ConditionType.PAYMENT_METHOD:    "Phương thức thanh toán",
	enum.ConditionType.PRODUCT_TAG:       "Loại sản phẩm",
	enum.ConditionType.PRODUCT_BLACKLIST: "Sản phẩm không hợp lệ",
}

func getMessageCustomerCondition(resultsCondition *voucherConditionResult, conditions []model.PromotionCondition, operator string) []*model.ConditionMessage {
	validateResult := make([]model.ConditionMessage, 0)

	for _, targetCondition := range conditions {
		targetMaxOrderCount := targetCondition.CustomerConditionField.MaxOrderCount
		targetMinOrderCount := targetCondition.CustomerConditionField.MinOrderCount
		targetIndexOrder := targetCondition.CustomerConditionField.IndexOrder
		targetMinDayNoOrder := targetCondition.CustomerConditionField.MinDayNoOrder
		// check is correct condition type
		if targetMaxOrderCount == nil && targetMinOrderCount == nil && targetIndexOrder == nil && targetMinDayNoOrder == nil {
			continue
		}

		isValid := true
		var errorMessage, message, progressMessage string
		var percentage *float64
		var current, target *int
		if targetMaxOrderCount != nil && *targetMaxOrderCount < resultsCondition.customer.OrderCount {
			isValid = false
			errorMessage = fmt.Sprintf("Bạn đã mua %d/%d đơn hàng, bạn còn thiếu %d đơn hàng để sử dụng mã", resultsCondition.customer.OrderCount, *targetMaxOrderCount, *targetMaxOrderCount-resultsCondition.customer.OrderCount)
			percentage = utils.ParseFloat64ToPointer(math.Floor((float64(resultsCondition.customer.OrderCount) / float64(*targetMaxOrderCount)) * 100))
			current = &resultsCondition.customer.OrderCount
			target = targetMaxOrderCount
			progressMessage = fmt.Sprintf("Đã mua %d/%d đơn hàng", resultsCondition.customer.OrderCount, *targetMaxOrderCount)
		} else if targetMaxOrderCount != nil {
			message = fmt.Sprintf("Bạn đã mua %d/%d lần, thỏa điều kiện sử dụng mã", resultsCondition.customer.OrderCount, *targetMaxOrderCount)
			current = targetMaxOrderCount
			target = targetMaxOrderCount
			progressMessage = fmt.Sprintf("Đã mua %d/%d đơn hàng", *targetMaxOrderCount, *targetMaxOrderCount)
		}

		if targetMinOrderCount != nil && *targetMinOrderCount > resultsCondition.customer.OrderCount {
			isValid = false
			errorMessage = "Mua " + fmt.Sprintf("%d", resultsCondition.customer.OrderCount) + "/" + fmt.Sprintf("%d", *targetMinOrderCount) + " đơn hàng nữa để sử dụng mã"
			percentage = utils.ParseFloat64ToPointer(math.Floor((float64(resultsCondition.customer.OrderCount) / float64(*targetMinOrderCount)) * 100))
			current = &resultsCondition.customer.OrderCount
			target = targetMinOrderCount
			progressMessage = fmt.Sprintf("Đã mua %d/%d đơn hàng", resultsCondition.customer.OrderCount, *targetMinOrderCount)
		} else if targetMinOrderCount != nil {
			message = "Đã thỏa " + fmt.Sprintf("%d", *targetMinOrderCount) + "/" + fmt.Sprintf("%d", *targetMinOrderCount) + " đơn hàng để sử dụng mã"
			current = targetMinOrderCount
			target = targetMinOrderCount
			progressMessage = fmt.Sprintf("Đã mua %d/%d đơn hàng", *targetMinOrderCount, *targetMinOrderCount)
		}

		if targetIndexOrder != nil && *targetIndexOrder != resultsCondition.customer.OrderCount+1 {
			isValid = false
			errorMessage = "Mua " + fmt.Sprintf("%d", resultsCondition.customer.OrderCount-1) + "/" + fmt.Sprintf("%d", *targetIndexOrder) + fmt.Sprintf("%d", *targetIndexOrder-resultsCondition.customer.OrderCount-1) + " đơn hàng để sử dụng mã"
			percentage = utils.ParseFloat64ToPointer(math.Floor((float64(resultsCondition.customer.OrderCount-1) / float64(*targetIndexOrder)) * 100))
			current = utils.ParseIntToPointer(resultsCondition.customer.OrderCount - 1)
			target = targetIndexOrder
			progressMessage = fmt.Sprintf("Đã mua %d/%d đơn hàng", resultsCondition.customer.OrderCount, *targetIndexOrder)
		} else if targetIndexOrder != nil {
			message = "Đã thỏa " + fmt.Sprintf("%d", *targetIndexOrder) + "/" + fmt.Sprintf("%d", *targetIndexOrder) + " đơn hàng để sử dụng mã"
			current = targetIndexOrder
			target = targetIndexOrder
			progressMessage = fmt.Sprintf("Đã mua %d/%d đơn hàng", *targetIndexOrder, *targetIndexOrder)
		}

		if targetMinDayNoOrder != nil {
			now := time.Now()
			lastOrderTime := now
			if resultsCondition.customer.LastOrderTime != nil {
				lastOrderTime = *resultsCondition.customer.LastOrderTime
			} else if resultsCondition.customer.ConfirmedTime != nil {
				lastOrderTime = *resultsCondition.customer.ConfirmedTime
			} else if resultsCondition.customer.CreatedTime != nil {
				lastOrderTime = *resultsCondition.customer.CreatedTime
			}
			lastOrderTime = lastOrderTime.Add(time.Duration(*targetMinDayNoOrder) * 24 * time.Hour)
			lastOrderTimeWithoutMinDayNoOrder := lastOrderTime.Add(-time.Duration(*targetMinDayNoOrder) * 24 * time.Hour)
			if now.Before(lastOrderTime) {
				isValid = false
				requireDateComeback := int(*targetMinDayNoOrder - int(now.Sub(lastOrderTimeWithoutMinDayNoOrder).Hours()/24))
				errorMessage = fmt.Sprintf("Bạn đã %d/%d ngày chưa đặt hàng - chưa đạt điều kiện. Hãy quay lại sau %d ngày để nhận ưu đãi", int(now.Sub(lastOrderTimeWithoutMinDayNoOrder).Hours()/24), *targetMinDayNoOrder, requireDateComeback)
				percentage = utils.ParseFloat64ToPointer(math.Floor((float64(int(now.Sub(lastOrderTimeWithoutMinDayNoOrder).Hours())/24) / float64(*targetMinDayNoOrder)) * 100))
				current = utils.ParseIntToPointer(int(now.Sub(lastOrderTimeWithoutMinDayNoOrder).Hours() / 24))
				target = targetMinDayNoOrder
				progressMessage = fmt.Sprintf("Đã %d/%d ngày chưa đặt hàng", int(now.Sub(lastOrderTimeWithoutMinDayNoOrder).Hours()/24), *targetMinDayNoOrder)
			} else {
				message = fmt.Sprintf("Bạn đã %d/%d ngày chưa đặt hàng - đã thỏa điều kiện để sử dụng mã", *targetMinDayNoOrder, *targetMinDayNoOrder)
				current = targetMinDayNoOrder
				target = targetMinDayNoOrder
				progressMessage = fmt.Sprintf("Đã %d/%d ngày chưa đặt hàng", *targetMinDayNoOrder, *targetMinDayNoOrder)
			}
		}

		validateResult = append(validateResult, model.ConditionMessage{
			IsValid:              &isValid,
			Message:              getReturnMessage(errorMessage, message),
			VoucherConditionType: enum.ConditionType.CUSTOMER_HISTORY,
			ConditionType:        operator,
			PercentageCondition:  getReturnPercent(percentage),
			Current:              current,
			Target:               target,
			ProgressMessage:      progressMessage,
		})
	}

	if len(validateResult) > 0 {
		result := make([]*model.ConditionMessage, len(validateResult))
		for i := range validateResult {
			result[i] = &validateResult[i]
		}
		return result
	}

	return nil
}

func getMessageProductCondition(resultsCondition *voucherConditionResult, conditions []model.PromotionCondition, operator string) []*model.ConditionMessage {
	validateResult := make([]model.ConditionMessage, 0)

	for _, targetCondition := range conditions {
		targetProductName := targetCondition.ProductConditionField.ProductName
		targetProductCode := targetCondition.ProductConditionField.ProductCode
		targetMinQuantity := targetCondition.ProductConditionField.MinQuantity
		targetMinTotalPrice := targetCondition.ProductConditionField.MinTotalPrice
		targetSellerCode := targetCondition.ProductConditionField.SellerCode
		// check is correct condition type
		if targetMinQuantity == nil && targetMinTotalPrice == nil && targetSellerCode == nil && targetProductCode == "" {
			continue
		}

		var errorCode enum.VoucherErrorCodeType
		var errorMessage, message, progressMessage string
		var percentage *float64
		var current, target *int
		isValid := true

		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", targetProductCode)
		if targetSellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *targetSellerCode)
		}

		if targetProductName == "" {
			targetProductName = "theo điều kiện"
		}

		item := resultsCondition.cart.ProductValue[keyValidate]
		// check quantity first, becuz it's allow 0 quantity
		if targetMinTotalPrice != nil && int(item.Total) < *targetMinTotalPrice {
			isValid = false
			errorMessage = fmt.Sprintf("Mua %s/%s vnđ sản phẩm %s", utils.FormatVNDCurrency(fmt.Sprint(item.Total)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), targetProductName)
			errorCode = enum.VoucherErrorCode.NOT_ENOUGH_ORDER_VALUE
			percentage = utils.ParseFloat64ToPointer(math.Floor((float64(int(item.Total)) / float64(*targetMinTotalPrice)) * 100))
			current = utils.ParseIntToPointer(int(item.Total))
			target = targetMinTotalPrice
			progressMessage = fmt.Sprintf("Đã mua %s/%s vnđ", utils.FormatVNDCurrency(fmt.Sprint(item.Total)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)))
		} else if targetMinTotalPrice != nil {
			message = fmt.Sprintf("Mua %s/%s vnđ sản phẩm %s", utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), targetProductName)
			current = targetMinTotalPrice
			target = targetMinTotalPrice
			progressMessage = fmt.Sprintf("Đã mua %s/%s vnđ", utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)))
		}

		if _, ok := resultsCondition.cart.ProductValue[keyValidate]; !ok {
			isValid = false
			errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			errorMessage = fmt.Sprintf("Mua sản phẩm %s", targetProductName)
			current = utils.ParseIntToPointer(int(item.Quantity))
			if targetMinQuantity != nil {
				errorMessage = fmt.Sprintf("Đã thêm vào giỏ %d/%d sản phẩm %s", int(item.Quantity), *targetMinQuantity, targetProductName)
				percentage = utils.ParseFloat64ToPointer(math.Floor((float64(int(item.Quantity)) / float64(*targetMinQuantity)) * 100))
			}
			target = utils.ParseIntToPointer(*targetMinQuantity)
			progressMessage = fmt.Sprintf("Đã thêm vào giỏ %d/%d sản phẩm", int(item.Quantity), *targetMinQuantity)
		} else if targetMinQuantity != nil {
			message = fmt.Sprintf("Đã thêm vào giỏ %d/%d sản phẩm %s", *targetMinQuantity, *targetMinQuantity, targetProductName)
			current = targetMinQuantity
			target = targetMinQuantity
			progressMessage = fmt.Sprintf("Đã thêm vào giỏ %d/%d sản phẩm", *targetMinQuantity, *targetMinQuantity)
		}

		if targetMinQuantity != nil && int(item.Quantity) < *targetMinQuantity {
			isValid = false
			errorMessage = fmt.Sprintf("Mua %d/%d sản phẩm %s", int(item.Quantity), *targetMinQuantity, targetProductName)
			errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
			percentage = utils.ParseFloat64ToPointer(math.Floor((float64(int(item.Quantity)) / float64(*targetMinQuantity)) * 100))
			current = utils.ParseIntToPointer(int(item.Quantity))
			target = targetMinQuantity
			progressMessage = fmt.Sprintf("Đã thêm vào giỏ %d/%d sản phẩm", int(item.Quantity), *targetMinQuantity)
		} else if targetMinQuantity != nil {
			message = fmt.Sprintf("Đã thêm vào giỏ %d/%d sản phẩm %s", *targetMinQuantity, *targetMinQuantity, targetProductName)
			current = targetMinQuantity
			target = targetMinQuantity
			progressMessage = fmt.Sprintf("Đã thêm vào giỏ %d/%d sản phẩm", *targetMinQuantity, *targetMinQuantity)
		}

		validateResult = append(validateResult, model.ConditionMessage{
			Message:              getReturnMessage(errorMessage, message),
			IsValid:              &isValid,
			ErrorCode:            errorCode,
			VoucherConditionType: enum.ConditionType.PRODUCT,
			ConditionType:        operator,
			FilterProduct:        formatConditionMessageByFilter(resultsCondition, formatConditionMessageByKey(targetCondition, enum.ConditionType.PRODUCT)),
			PercentageCondition:  getReturnPercent(percentage),
			Current:              current,
			Target:               target,
			ProgressMessage:      progressMessage,
		})
	}

	if len(validateResult) > 0 {
		result := make([]*model.ConditionMessage, len(validateResult))
		for i := range validateResult {
			result[i] = &validateResult[i]
		}
		return result
	}

	return nil
}

func getMessageOrderCondition(resultsCondition *voucherConditionResult, conditions []model.PromotionCondition, operator string) []*model.ConditionMessage {
	validateResult := make([]model.ConditionMessage, 0)

	for _, targetCondition := range conditions {
		targetMinTotalPrice := targetCondition.OrderConditionField.MinTotalPrice
		targetMinSkuQuantity := targetCondition.OrderConditionField.MinSkuQuantity
		targetNewSKU := targetCondition.OrderConditionField.NewSkuQuantity
		targetRepeatSKU := targetCondition.OrderConditionField.RepeatSkuQuantity
		if targetMinTotalPrice == nil && targetMinSkuQuantity == nil && targetNewSKU == nil && targetRepeatSKU == nil {
			continue
		}

		var messageMinTotalPrice, messageMinSKU, messageNewSKU, messageRepeatSKU, progressMessageMinTotalPrice, progressMessageMinSKU, progressNewSKU, progressRepeatSKU string
		var current, target *int
		cartPrice := resultsCondition.cart.Price
		totalItem := resultsCondition.cart.TotalItem
		skusStr := ""
		lastMonthSKUs := make([]string, 0)
		cartSKUs := make([]string, 0)
		newSKUs := 0
		repeatSKUs := 0

		if targetNewSKU != nil || targetRepeatSKU != nil {
			for sku := range resultsCondition.cart.SkuValue {
				cartSKUs = append(cartSKUs, sku)
			}
		}

		if resultsCondition.organization == enum.PromotionOrganizer.SELLER_CENTER || resultsCondition.organization == enum.PromotionOrganizer.INTERNAL_SELLER {
			cartPrice = 0
			totalItem = 0
			cartSKUs = make([]string, 0)
			if !isNilOrDefaultValue(resultsCondition.storeCode) {
				if d, ok := resultsCondition.cart.StoreValue[*resultsCondition.storeCode]; ok {
					cartPrice = d.Total
					totalItem = d.TotalItem
					cartSKUs = d.SKUs
				}
			} else {
				for _, seller := range resultsCondition.sellerCodes {
					if _, ok := resultsCondition.cart.SellerValue[seller]; ok {
						cartPrice += resultsCondition.cart.SellerValue[seller].Total
						totalItem += resultsCondition.cart.SellerValue[seller].TotalItem
						cartSKUs = append(cartSKUs, resultsCondition.cart.SellerValue[seller].SKUs...)
					}
				}
			}
		} else {
			if resultsCondition.applyDiscount != nil && resultsCondition.applyDiscount.NotInSkus != nil {
				for _, sku := range *resultsCondition.applyDiscount.NotInSkus {
					if _, ok := resultsCondition.cart.SkuValue[sku]; ok {
						cartPrice -= resultsCondition.cart.SkuValue[sku].Total
						if skuName := resultsCondition.applyDiscount.SkuName[sku]; skuName != "" {
							if skusStr == "" {
								skusStr += skuName
							} else {
								skusStr += ", " + skuName
							}
						}
					}
				}
			}
			if resultsCondition.applyDiscount != nil && resultsCondition.applyDiscount.Skus != nil && len(*resultsCondition.applyDiscount.Skus) > 0 {
				cartPrice = 0
				totalItem = 0
				for _, sku := range *resultsCondition.applyDiscount.Skus {
					if _, ok := resultsCondition.cart.SkuValue[sku]; ok {
						cartPrice += resultsCondition.cart.SkuValue[sku].Total
						totalItem += 1
						cartSKUs = append(cartSKUs, sku)
					}
				}
			}
		}

		if targetMinTotalPrice != nil {
			var errorCode enum.VoucherErrorCodeType
			percentage := utils.ParseFloat64ToPointer(100)
			isValid := true
			if *targetMinTotalPrice > int(cartPrice) {
				messageMinTotalPrice = fmt.Sprintf("Mua %s/%s vnđ để sử dụng mã", utils.FormatVNDCurrency((fmt.Sprint(cartPrice))), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)))
				if resultsCondition.sellerName != "" {
					messageMinTotalPrice = fmt.Sprintf("Mua %s/%s vnđ từ %s để sử dụng mã", utils.FormatVNDCurrency(fmt.Sprint(cartPrice)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), resultsCondition.sellerName)
				}
				if skusStr != "" {
					messageMinTotalPrice += ", không áp dụng sản phẩm " + skusStr
				}
				errorCode = enum.VoucherErrorCode.NOT_ENOUGH_ORDER_VALUE
				isValid = false
				percentage = utils.ParseFloat64ToPointer(math.Floor((float64(cartPrice) / float64(*targetMinTotalPrice)) * 100))
				current = utils.ParseIntToPointer(int(cartPrice))
				target = targetMinTotalPrice
				progressMessageMinTotalPrice = fmt.Sprintf("Đã thỏa %s/%s vnđ", utils.FormatVNDCurrency(fmt.Sprint(cartPrice)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)))
			} else {
				messageMinTotalPrice = fmt.Sprintf("Đã thoả %s/%s vnđ để sử dụng mã", utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)))
				current = targetMinTotalPrice
				target = targetMinTotalPrice
				progressMessageMinTotalPrice = fmt.Sprintf("Đã thỏa %s/%s vnđ", utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)))
			}

			validateResult = append(validateResult, model.ConditionMessage{
				Message:              messageMinTotalPrice,
				IsValid:              &isValid,
				ErrorCode:            errorCode,
				VoucherConditionType: enum.ConditionType.ORDER_VALUE,
				ConditionType:        enum.Operator.AND,
				PercentageCondition:  getReturnPercent(percentage),
				Current:              current,
				Target:               target,
				ProgressMessage:      progressMessageMinTotalPrice,
			})
		}

		if targetMinSkuQuantity != nil {
			var errorCode enum.VoucherErrorCodeType
			percentage := utils.ParseFloat64ToPointer(100)
			isValid := true
			if !isNilOrDefaultValue(targetMinSkuQuantity) && *targetMinSkuQuantity > totalItem {
				messageMinSKU = "Mua " + fmt.Sprintf("%d", totalItem) + "/" + fmt.Sprintf("%d", *targetMinSkuQuantity) + " loại sản phẩm để sử dụng mã"
				if resultsCondition.sellerName != "" {
					messageMinSKU = "Mua " + fmt.Sprintf("%d", totalItem) + "/" + fmt.Sprintf("%d", *targetMinSkuQuantity) + " loại sản phẩm từ " + resultsCondition.sellerName
				}
				errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
				isValid = false
				percentage = utils.ParseFloat64ToPointer(math.Floor((float64(totalItem) / float64(*targetMinSkuQuantity)) * 100))
				current = utils.ParseIntToPointer(totalItem)
				target = targetMinSkuQuantity
				progressMessageMinSKU = fmt.Sprintf("Đã thỏa %d/%d loại sản phẩm", totalItem, *targetMinSkuQuantity)
			} else {
				messageMinSKU = "Đã thỏa " + fmt.Sprintf("%d", *targetMinSkuQuantity) + "/" + fmt.Sprintf("%d", *targetMinSkuQuantity) + " loại sản phẩm để sử dụng mã"
				current = targetMinSkuQuantity
				target = targetMinSkuQuantity
				progressMessageMinSKU = fmt.Sprintf("Đã thỏa %d/%d loại sản phẩm", *targetMinSkuQuantity, *targetMinSkuQuantity)
			}

			validateResult = append(validateResult, model.ConditionMessage{
				Message:              messageMinSKU,
				IsValid:              &isValid,
				ErrorCode:            errorCode,
				VoucherConditionType: enum.ConditionType.ORDER_VALUE,
				ConditionType:        enum.Operator.AND,
				PercentageCondition:  getReturnPercent(percentage),
				Current:              current,
				Target:               target,
				ProgressMessage:      progressMessageMinSKU,
			})
		}

		// TODO: calculate cart SKU and lastMonth SKU
		if resultsCondition.customerExperience != nil && len(resultsCondition.customerExperience.SkusData) > 0 {
			target := "purchased_1" // skus ordered in last month
			if data, ok := resultsCondition.customerExperience.SkusData[target]; ok {
				lastMonthSKUs = data
			}
		}

		totalLastMonthSKU := len(lastMonthSKUs)
		if totalItem > 0 {
			diffSKUs := getDuplicateTwoSliceString(lastMonthSKUs, cartSKUs)
			totalDiffSKUs := len(diffSKUs)
			newSKUs = totalItem - totalDiffSKUs
			repeatSKUs = totalDiffSKUs
		}

		if !isNilOrDefaultValue(targetNewSKU) {
			var errorCode enum.VoucherErrorCodeType
			percentage := utils.ParseFloat64ToPointer(100)
			isValid := true
			if newSKUs < *targetNewSKU {
				messageNewSKU = "Mua thêm " + fmt.Sprintf("%d", newSKUs) + "/" + fmt.Sprintf("%d", *targetNewSKU) + " loại sản phẩm chưa từng mua để sử dụng mã"
				if resultsCondition.sellerName != "" {
					messageNewSKU = "Mua " + fmt.Sprintf("%d", newSKUs) + "/" + fmt.Sprintf("%d", *targetNewSKU) + " loại sản phẩm chưa từng mua từ " + resultsCondition.sellerName
				}
				errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
				isValid = false
				percentage = utils.ParseFloat64ToPointer(math.Floor((float64(newSKUs) / float64(*targetNewSKU)) * 100))
				current = utils.ParseIntToPointer(newSKUs)
				target = targetNewSKU
				progressNewSKU = fmt.Sprintf("Đã thỏa %d/%d loại sản phẩm chưa từng mua", newSKUs, *targetNewSKU)
			} else {
				messageNewSKU = "Đã thỏa " + fmt.Sprintf("%d", *targetNewSKU) + "/" + fmt.Sprintf("%d", *targetNewSKU) + " loại sản phẩm chưa từng mua để sử dụng mã"
				if resultsCondition.sellerName != "" {
					messageNewSKU = "Đã thỏa " + fmt.Sprintf("%d", *targetNewSKU) + "/" + fmt.Sprintf("%d", *targetNewSKU) + " loại sản phẩm chưa từng mua từ " + resultsCondition.sellerName
				}
				current = targetNewSKU
				target = targetNewSKU
				progressNewSKU = fmt.Sprintf("Đã thỏa %d/%d loại sản phẩm chưa từng mua", *targetNewSKU, *targetNewSKU)
			}

			validateResult = append(validateResult, model.ConditionMessage{
				Message:              messageNewSKU,
				IsValid:              &isValid,
				ErrorCode:            errorCode,
				VoucherConditionType: enum.ConditionType.ORDER_VALUE,
				ConditionType:        enum.Operator.AND,
				PercentageCondition:  getReturnPercent(percentage),
				Current:              current,
				Target:               target,
				ProgressMessage:      progressNewSKU,
			})
		}

		if !isNilOrDefaultValue(targetRepeatSKU) {
			var errorCode enum.VoucherErrorCodeType
			percentage := utils.ParseFloat64ToPointer(100)
			isValid := true

			if totalLastMonthSKU == 0 {
				messageRepeatSKU = "Bạn chưa mua sản phẩm nào trong tháng trước đó để sử dụng mã"
				isValid = false
				errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
				percentage = utils.ParseFloat64ToPointer(0)
				current = utils.ParseIntToPointer(0)
				target = targetRepeatSKU
				progressRepeatSKU = "Bạn chưa thể sử dụng mã"
			} else if repeatSKUs < *targetRepeatSKU {
				messageRepeatSKU = "Mua thêm " + fmt.Sprintf("%d", repeatSKUs) + "/" + fmt.Sprintf("%d", *targetRepeatSKU) + " loại sản phẩm đã từng mua để sử dụng mã"
				if resultsCondition.sellerName != "" {
					messageRepeatSKU = "Mua " + fmt.Sprintf("%d", repeatSKUs) + "/" + fmt.Sprintf("%d", *targetRepeatSKU) + " loại sản phẩm đã từng mua từ " + resultsCondition.sellerName
				}
				errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
				isValid = false
				percentage = utils.ParseFloat64ToPointer(math.Floor((float64(repeatSKUs) / float64(*targetRepeatSKU)) * 100))
				current = utils.ParseIntToPointer(repeatSKUs)
				target = targetRepeatSKU
				progressRepeatSKU = fmt.Sprintf("Đã thỏa %d/%d loại sản phẩm đã từng mua", repeatSKUs, *targetRepeatSKU)
			} else {
				messageRepeatSKU = "Đã thỏa " + fmt.Sprintf("%d", *targetRepeatSKU) + "/" + fmt.Sprintf("%d", *targetRepeatSKU) + " loại sản phẩm đã từng mua để sử dụng mã"
				if resultsCondition.sellerName != "" {
					messageRepeatSKU = "Đã thỏa " + fmt.Sprintf("%d", *targetRepeatSKU) + "/" + fmt.Sprintf("%d", *targetRepeatSKU) + " loại sản phẩm đã từng mua từ " + resultsCondition.sellerName
				}
				current = targetRepeatSKU
				target = targetRepeatSKU
				progressRepeatSKU = fmt.Sprintf("Đã thỏa %d/%d loại sản phẩm đã từng mua", *targetRepeatSKU, *targetRepeatSKU)
			}

			validateResult = append(validateResult, model.ConditionMessage{
				Message:              messageRepeatSKU,
				IsValid:              &isValid,
				ErrorCode:            errorCode,
				VoucherConditionType: enum.ConditionType.ORDER_VALUE,
				ConditionType:        enum.Operator.AND,
				PercentageCondition:  getReturnPercent(percentage),
				Current:              current,
				Target:               target,
				ProgressMessage:      progressRepeatSKU,
			})
		}
	}

	if len(validateResult) > 0 {
		result := make([]*model.ConditionMessage, len(validateResult))
		for i := range validateResult {
			result[i] = &validateResult[i]
		}
		return result
	}
	return nil
}

func getMessageProductTagCondition(resultsCondition *voucherConditionResult, conditions []model.PromotionCondition, operator string) []*model.ConditionMessage {
	validateResult := make([]model.ConditionMessage, 0)

	for index, targetCondition := range conditions {
		targetTagName := "sản phẩm chỉ định"
		targetTagCode := targetCondition.ProductTagConditionField.TagCode
		targetSellerCode := targetCondition.ProductTagConditionField.SellerCode
		targetMinQuantity := targetCondition.ProductTagConditionField.MinQuantity
		targetMinTotalPrice := targetCondition.ProductTagConditionField.MinTotalPrice
		targetSkuQuantity := targetCondition.ProductTagConditionField.SkuQuantity
		if targetMinQuantity == nil && targetMinTotalPrice == nil && targetSkuQuantity == nil && targetSellerCode == nil && targetTagCode == "" {
			continue
		}

		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", targetTagCode)

		if targetSellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *targetSellerCode)
		}

		item := resultsCondition.cart.TagValue[keyValidate]

		if targetMinQuantity != nil {
			var errorCode enum.VoucherErrorCodeType
			var errorMessage, message, progressMessage string
			isValid := true
			var percentage *float64
			var current, target *int

			if int(item.Quantity) < *targetMinQuantity {
				isValid = false
				errorMessage = fmt.Sprintf("Mua %d/%d sản phẩm trong nhóm %s", int(item.Quantity), *targetMinQuantity, targetTagName)
				errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
				percentage = utils.ParseFloat64ToPointer(math.Floor((float64(int(item.Quantity)) / float64(*targetMinQuantity)) * 100))
				current = utils.ParseIntToPointer(int(item.Quantity))
				target = targetMinQuantity
				progressMessage = fmt.Sprintf("Đã mua %d/%d sản phẩm", int(item.Quantity), *targetMinQuantity)
			} else {
				message = fmt.Sprintf("Đã thỏa %d/%d sản phẩm trong nhóm %s", *targetMinQuantity, *targetMinQuantity, targetTagName)
				current = targetMinQuantity
				target = targetMinQuantity
				progressMessage = fmt.Sprintf("Đã thỏa %d/%d sản phẩm", *targetMinQuantity, *targetMinQuantity)
			}

			validateResult = append(validateResult, model.ConditionMessage{
				IsValid:              &isValid,
				Message:              getReturnMessage(errorMessage, message),
				VoucherConditionType: enum.ConditionType.PRODUCT_TAG,
				ErrorCode:            errorCode,
				ConditionType:        operator,
				FilterProduct:        formatConditionMessageByFilter(resultsCondition, formatConditionMessageByKey(targetCondition, enum.ConditionType.PRODUCT_TAG)),
				PercentageCondition:  getReturnPercent(percentage),
				Current:              current,
				Target:               target,
				ProgressMessage:      progressMessage,
				GroupIndex:           index,
			})
		}

		if targetMinTotalPrice != nil && *targetMinTotalPrice > 0 { // handle case 0 min total price
			var errorCode enum.VoucherErrorCodeType
			var errorMessage, message, progressMessage string
			isValid := true
			var percentage *float64
			var current, target *int

			if int(item.Total) < *targetMinTotalPrice {
				isValid = false
				errorMessage = fmt.Sprintf("Mua %s/%s vnđ trong nhóm %s", utils.FormatVNDCurrency(fmt.Sprint(item.Total)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), targetTagName)
				errorCode = enum.VoucherErrorCode.NOT_ENOUGH_ORDER_VALUE
				percentage = utils.ParseFloat64ToPointer(math.Floor((float64(int(item.Total)) / float64(*targetMinTotalPrice)) * 100))
				current = utils.ParseIntToPointer(int(item.Total))
				target = targetMinTotalPrice
				progressMessage = fmt.Sprintf("Đã mua %s/%s vnđ", utils.FormatVNDCurrency(fmt.Sprint(item.Total)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)))
			} else {
				message = fmt.Sprintf("Đã thỏa %s/%s vnđ trong nhóm %s", utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), targetTagName)
				current = targetMinTotalPrice
				target = targetMinTotalPrice
				progressMessage = fmt.Sprintf("Đã thoả %s/%s vnđ", utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)), utils.FormatVNDCurrency(fmt.Sprint(*targetMinTotalPrice)))
			}

			validateResult = append(validateResult, model.ConditionMessage{
				IsValid:              &isValid,
				Message:              getReturnMessage(errorMessage, message),
				VoucherConditionType: enum.ConditionType.PRODUCT_TAG,
				ErrorCode:            errorCode,
				ConditionType:        operator,
				FilterProduct:        formatConditionMessageByFilter(resultsCondition, formatConditionMessageByKey(targetCondition, enum.ConditionType.PRODUCT_TAG)),
				PercentageCondition:  getReturnPercent(percentage),
				Current:              current,
				Target:               target,
				ProgressMessage:      progressMessage,
				GroupIndex:           index,
			})
		}

		if targetSkuQuantity != nil {
			var errorCode enum.VoucherErrorCodeType
			var errorMessage, message, progressMessage string
			isValid := true
			var percentage *float64
			var current, target *int

			if item.TotalItem < *targetSkuQuantity {
				isValid = false
				errorMessage = fmt.Sprintf("Mua %d/%d loại sản phẩm trong nhóm %s", int(item.TotalItem), *targetSkuQuantity, targetTagName)
				errorCode = enum.VoucherErrorCode.NOT_ENOUGH_SKU_QUANTITY
				percentage = utils.ParseFloat64ToPointer(math.Floor((float64(int(item.TotalItem)) / float64(*targetSkuQuantity)) * 100))
				current = utils.ParseIntToPointer(int(item.TotalItem))
				target = targetSkuQuantity
				progressMessage = fmt.Sprintf("Đã mua %d/%d loại sản phẩm", int(item.TotalItem), *targetSkuQuantity)
			} else {
				message = fmt.Sprintf("Đã thỏa %d/%d loại sản phẩm trong nhóm %s", *targetSkuQuantity, *targetSkuQuantity, targetTagName)
				current = targetSkuQuantity
				target = targetSkuQuantity
				progressMessage = fmt.Sprintf("Đã thỏa %d/%d loại sản phẩm", *targetSkuQuantity, *targetSkuQuantity)
			}

			validateResult = append(validateResult, model.ConditionMessage{
				IsValid:              &isValid,
				Message:              getReturnMessage(errorMessage, message),
				VoucherConditionType: enum.ConditionType.PRODUCT_TAG,
				ErrorCode:            errorCode,
				ConditionType:        operator,
				FilterProduct:        formatConditionMessageByFilter(resultsCondition, formatConditionMessageByKey(targetCondition, enum.ConditionType.PRODUCT_TAG)),
				PercentageCondition:  getReturnPercent(percentage),
				Current:              current,
				Target:               target,
				ProgressMessage:      progressMessage,
				GroupIndex:           index,
			})
		}
	}

	if len(validateResult) > 0 {
		result := make([]*model.ConditionMessage, len(validateResult))
		for i := range validateResult {
			result[i] = &validateResult[i]
		}
		return result
	}

	return nil
}

func getMessageProductBlackListCondition(resultsCondition *voucherConditionResult, conditions []model.PromotionCondition, operator string) []*model.ConditionMessage {
	validateResult := make([]model.ConditionMessage, 0)

	for _, targetCondition := range conditions {
		if targetCondition.ProductBlackListConditionField.ProductCode == "" {
			continue
		}

		var errorMessage, message, progressMessage string
		var percentage *float64
		var current, target *int
		isValid := true

		keyValidate := fmt.Sprintf("%s_%s", "SELLER_CODE", targetCondition.ProductBlackListConditionField.ProductCode)
		if targetCondition.ProductBlackListConditionField.SellerCode != nil {
			keyValidate = strings.ReplaceAll(keyValidate, "SELLER_CODE", *targetCondition.ProductBlackListConditionField.SellerCode)
		}

		if _, ok := resultsCondition.cart.ProductValue[keyValidate]; ok {
			isValid = false
			errorMessage = "Không áp dụng khi mua sản phẩm " + targetCondition.ProductBlackListConditionField.ProductName
			percentage = utils.ParseFloat64ToPointer(0)
			current = utils.ParseIntToPointer(1)
			target = utils.ParseIntToPointer(1)
			progressMessage = fmt.Sprintf("Đã mua sản phẩm %s", targetCondition.ProductBlackListConditionField.ProductName)
		} else {
			message = "Áp dụng khi mua sản phẩm " + targetCondition.ProductBlackListConditionField.ProductName
			current = utils.ParseIntToPointer(0)
			target = utils.ParseIntToPointer(1)
			progressMessage = fmt.Sprintf("Chưa mua sản phẩm %s", targetCondition.ProductBlackListConditionField.ProductName)
		}

		validateResult = append(validateResult, model.ConditionMessage{
			IsValid:              &isValid,
			Message:              getReturnMessage(errorMessage, message),
			VoucherConditionType: enum.ConditionType.PRODUCT_BLACKLIST,
			ConditionType:        operator,
			PercentageCondition:  getReturnPercent(percentage),
			Current:              current,
			Target:               target,
			ProgressMessage:      progressMessage,
		})
	}

	if len(validateResult) > 0 {
		result := make([]*model.ConditionMessage, len(validateResult))
		for i := range validateResult {
			result[i] = &validateResult[i]
		}
		return result
	}
	return nil
}

func getMessagePaymentCondition(resultsCondition *voucherConditionResult, conditions []model.PromotionCondition, operator string) []*model.ConditionMessage {
	validateResult := make([]model.ConditionMessage, 0)

	for _, targetCondition := range conditions {
		targetPaymentMethod := targetCondition.PaymentConditionField.PaymentMethod
		targetPaymentMethodName := targetCondition.PaymentConditionField.PaymentMethodName
		if targetPaymentMethod == "" && targetPaymentMethodName == "" {
			continue
		}

		var errorCode enum.VoucherErrorCodeType
		var errorMessage, message, progressMessage string
		var percentage *float64
		var current, target *int
		isValid := true

		if resultsCondition.cart.PaymentMethod != "" && targetPaymentMethod != resultsCondition.cart.PaymentMethod {
			isValid = false
			errorMessage = fmt.Sprintf("Hình thức thanh toán %s", targetPaymentMethodName)
			errorCode = enum.VoucherErrorCode.INVALID_PAYMENT_METHOD
			percentage = utils.ParseFloat64ToPointer(0)
			current = utils.ParseIntToPointer(0)
			target = utils.ParseIntToPointer(1)
			progressMessage = fmt.Sprintf("Chưa thỏa hình thức thanh toán %s", targetPaymentMethodName)
		} else {
			message = fmt.Sprintf("Hình thức thanh toán %s", targetPaymentMethodName)
			current = utils.ParseIntToPointer(1)
			target = utils.ParseIntToPointer(1)
			progressMessage = "Đã thỏa hình thức thanh toán"
		}

		validateResult = append(validateResult, model.ConditionMessage{
			IsValid:              &isValid,
			Message:              getReturnMessage(errorMessage, message),
			VoucherConditionType: enum.ConditionType.PAYMENT_METHOD,
			ErrorCode:            errorCode,
			ConditionType:        operator,
			PercentageCondition:  getReturnPercent(percentage),
			Current:              current,
			Target:               target,
			ProgressMessage:      progressMessage,
		})
	}

	if len(validateResult) > 0 {
		result := make([]*model.ConditionMessage, len(validateResult))
		for i := range validateResult {
			result[i] = &validateResult[i]
		}
		return result
	}
	return nil
}

func getReturnMessage(errorMessage, validMessage string) string {
	if errorMessage != "" {
		return errorMessage
	}
	return validMessage
}

func getReturnPercent(percentageError *float64) *float64 {
	if percentageError != nil {
		return percentageError
	}
	return utils.ParseFloat64ToPointer(100)
}

func formatConditionMessageByKey(condition model.PromotionCondition, voucherConditionType enum.ConditionTypeValue) map[enum.ConditionTypeValue]model.PromotionType {
	return map[enum.ConditionTypeValue]model.PromotionType{
		voucherConditionType: {
			AndConditions: []model.PromotionCondition{condition},
		},
	}
}

func formatConditionMessageByFilter(resultsCondition *voucherConditionResult, condition map[enum.ConditionTypeValue]model.PromotionType) *model.FilterProduct {
	result := &model.FilterProduct{}
	productCodes, tagCodes, skuCodes, sellers, skusNotIn, tagCodesIn := getProductInfoByCondition(condition, condition, resultsCondition.applyDiscount, &resultsCondition.sellerCodes, resultsCondition.storeCode)

	if len(tagCodes) > 0 {
		result.Tags = &tagCodes
	}
	if len(skuCodes) > 0 {
		result.Skus = &skuCodes
	}
	if len(sellers) > 0 {
		result.Sellers = &sellers
	} else if len(productCodes) > 0 {
		result.ProductCodes = &productCodes
	}
	if len(skusNotIn) > 0 {
		result.SkuNotIn = &skusNotIn
	}
	if len(tagCodesIn) > 0 {
		result.TagIn = &tagCodesIn
	}
	return result
}

func checkMesageCondition(condition func(*voucherConditionResult, []model.PromotionCondition, string) []*model.ConditionMessage, in *voucherConditionResult, results *[]*model.ConditionMessage) {
	appendMessages := func(conditionMessages []*model.ConditionMessage) {
		if conditionMessages != nil {
			*results = append(*results, conditionMessages...)
		}
	}
	for _, promoType := range in.andConditions {
		if promoType.AndConditions != nil {
			conditionAndMessage := condition(in, promoType.AndConditions, enum.Operator.AND)
			appendMessages(conditionAndMessage)
		}
		if promoType.OrConditions != nil {
			conditionOrMessage := condition(in, promoType.OrConditions, enum.Operator.OR)
			appendMessages(conditionOrMessage)
		}
	}

	for _, promoType := range in.orCondition {
		if promoType.AndConditions != nil {
			conditionAndMessage := condition(in, promoType.AndConditions, enum.Operator.AND)
			appendMessages(conditionAndMessage)
		}
		if promoType.OrConditions != nil {
			conditionOrMessage := condition(in, promoType.OrConditions, enum.Operator.OR)
			appendMessages(conditionOrMessage)
		}
	}
}

func handleMainMessage(voucherConditionType string, totalCondition int) string {
	// total conditon gather than 1, using message for multiple condition
	if totalCondition > 1 {
		if message, ok := ConditionMessageConfigCache.OrConditionsMessage[voucherConditionType]; ok {
			return message
		}
	}
	return "" // return mesage of first condition
}

func isValidMessageCondition(in voucherConditionResult) (ConditionMessages, *float64) {
	var wg sync.WaitGroup
	results := []*model.ConditionMessage{}

	// check condition though each condition type
	checkConditions := []struct {
		getMessageCondition func(*voucherConditionResult, []model.PromotionCondition, string) []*model.ConditionMessage
	}{
		{getMessageCondition: getMessagePaymentCondition},
		{getMessageCondition: getMessageProductBlackListCondition},
		{getMessageCondition: getMessageOrderCondition},
		{getMessageCondition: getMessageProductCondition},
		{getMessageCondition: getMessageProductTagCondition},
		// {getMessageCondition: getMessageCustomerCondition}, // temporary hide customer condition, will handle later
	}

	if in.andConditions == nil && in.orCondition == nil {
		return nil, nil
	}

	// map condition type to make map item
	isHasMakeMapItem := false
	mapHasMakeMapItem := map[enum.ConditionTypeValue]bool{
		enum.ConditionType.PRODUCT_TAG:       true,
		enum.ConditionType.PRODUCT:           true,
		enum.ConditionType.PRODUCT_BLACKLIST: true,
		enum.ConditionType.ORDER_VALUE:       true,
	}

	for promoType := range in.andConditions {
		if mapHasMakeMapItem[promoType] {
			isHasMakeMapItem = true
			break
		}
	}

	for promoType := range in.orCondition {
		if mapHasMakeMapItem[promoType] {
			isHasMakeMapItem = true
			break
		}
	}

	if isHasMakeMapItem {
		makeMapItem(in.cart, in.cart.CartItems)
	}

	priorityMap := make(map[enum.ConditionTypeValue]int)
	priorityCounter := 1
	// Define the desired order of conditions
	desiredOrder := []enum.ConditionTypeValue{
		// enum.ConditionType.CUSTOMER_HISTORY, // temporary hide customer condition, will handle later
		enum.ConditionType.PRODUCT,
		enum.ConditionType.ORDER_VALUE,
		enum.ConditionType.PRODUCT_TAG,
		enum.ConditionType.PRODUCT_BLACKLIST,
		enum.ConditionType.PAYMENT_METHOD,
	}

	// Loop through the desired order and populate the priority map
	for _, conditionType := range desiredOrder {
		if _, exists := in.andConditions[conditionType]; exists {
			priorityMap[conditionType] = priorityCounter
			priorityCounter++
		}
	}

	for _, cond := range checkConditions {
		wg.Add(1)
		go func(cond struct {
			getMessageCondition func(*voucherConditionResult, []model.PromotionCondition, string) []*model.ConditionMessage
		}) {
			defer wg.Done()
			checkMesageCondition(cond.getMessageCondition, &in, &results)
		}(cond)
	}

	wg.Wait()

	finalResults := formatResults(results)
	percentValidCondition := calculateValidConditionPercentage(finalResults)

	sort.Slice(finalResults, func(i, j int) bool {
		return priorityMap[finalResults[i].VoucherConditionType] < priorityMap[finalResults[j].VoucherConditionType]
	})

	return finalResults, &percentValidCondition
}

func formatResults(results []*model.ConditionMessage) []*model.ConditionMessage {
	// group condition message by condition type and voucher condition type
	groupedConditionMessage := groupConditionsByType(results)
	finalResults := make([]*model.ConditionMessage, 0)
	for conditionType, voucherConditions := range groupedConditionMessage {
		for voucherConditionType, conditions := range voucherConditions {
			if len(conditions) > 1 {
				switch voucherConditionType {
				case "PRODUCT_TAG":
					// TODO: product_tag is special case, child have subs
					finalResults = append(finalResults, handleProductTagConditions(conditions, voucherConditionType, conditionType)...)
				case "ORDER_VALUE":
					isValidSatisfy, percentage, _, _ := evaluateConditionSatisfaction(conditionType, conditions, voucherConditionType)
					finalResults = append(finalResults, handleOrderConditions(conditions, isValidSatisfy, percentage)...)
				default:
					// TODO: normal case not complexity so isValid and percent can calculate directly
					isValidSatisfy, percentage, _, _ := evaluateConditionSatisfaction(conditionType, conditions, voucherConditionType)
					finalResults = append(finalResults, handleDefaultConditions(conditions, voucherConditionType, isValidSatisfy, percentage)...)
				}
			} else {
				if len(conditions) == 0 {
					continue
				}
				conditions[0].ConditionType = enum.Operator.AND // force set condition type to AND when condition only have one
				finalResults = append(finalResults, conditions...)
			}
		}
	}
	return finalResults
}

func calculateValidConditionPercentage(finalResults []*model.ConditionMessage) float64 {
	totalCondition := len(finalResults)
	if totalCondition == 0 {
		return 0.0
	}
	validCondition := 0
	for _, result := range finalResults {
		if result.IsValid != nil && *result.IsValid {
			validCondition++
		}
	}
	return math.Floor((float64(validCondition) / float64(totalCondition)) * 100)
}

func findHighestAvailableCondition(conditionMessage []*model.ConditionMessage) *NearestAvailableCondition {
	var mainCurrent, mainTarget *int

	if len(conditionMessage) == 1 {
		return &NearestAvailableCondition{
			current: conditionMessage[0].Current,
			target:  conditionMessage[0].Target,
		}
	}

	for _, condition := range conditionMessage {
		if condition.IsValid != nil && condition.Current != nil && condition.Target != nil {
			if mainCurrent == nil && mainTarget == nil {
				mainCurrent = condition.Current
				mainTarget = condition.Target
			}
			if *condition.Current > *mainCurrent {
				mainCurrent = condition.Current
				mainTarget = condition.Target
			}
		}
	}

	return &NearestAvailableCondition{
		current: mainCurrent,
		target:  mainTarget,
	}
}

func groupConditionsByType(results []*model.ConditionMessage) map[string]map[string][]*model.ConditionMessage {
	groupedConditionMessage := make(map[string]map[string][]*model.ConditionMessage)
	for _, resultsCondition := range results {
		conditionType := string(resultsCondition.ConditionType)
		voucherConditionType := string(resultsCondition.VoucherConditionType)

		if _, ok := groupedConditionMessage[conditionType]; !ok {
			groupedConditionMessage[conditionType] = make(map[string][]*model.ConditionMessage)
		}
		if _, ok := groupedConditionMessage[conditionType][voucherConditionType]; !ok {
			groupedConditionMessage[conditionType][voucherConditionType] = make([]*model.ConditionMessage, 0)
		}
		groupedConditionMessage[conditionType][voucherConditionType] = append(groupedConditionMessage[conditionType][voucherConditionType], resultsCondition)
	}
	return groupedConditionMessage
}

func evaluateConditionSatisfaction(conditionType string, conditions []*model.ConditionMessage, voucherConditionType string) (bool, float64, int, int) {
	isValidSatisfy := true
	percentage := 0.0
	mapSpecialVoucherType := map[enum.ConditionTypeValue]bool{
		enum.ConditionType.PRODUCT_TAG: true,
	}

	// TODO: special case, return valid & total condition
	if mapSpecialVoucherType[enum.ConditionTypeValue(voucherConditionType)] {
		validGroupConditionCount := 0               // valid condition count
		totalGroupConditionCount := len(conditions) // total condition count
		for _, condition := range conditions {
			if condition.IsValid != nil && *condition.IsValid {
				validGroupConditionCount++
			}
		}

		if conditionType == string(enum.Operator.AND) {
			if validGroupConditionCount == totalGroupConditionCount {
				isValidSatisfy = true
			} else {
				isValidSatisfy = false
			}
		} else if conditionType == string(enum.Operator.OR) {
			if validGroupConditionCount > 0 {
				isValidSatisfy = true
			} else {
				isValidSatisfy = false
			}
		}
		if totalGroupConditionCount > 0 {
			if conditionType == string(enum.Operator.AND) {
				percentage = (float64(validGroupConditionCount) / float64(totalGroupConditionCount)) * 100
			} else if conditionType == string(enum.Operator.OR) {
				if validGroupConditionCount > 0 {
					percentage = 100
				}
			}
		}
		return isValidSatisfy, percentage, validGroupConditionCount, totalGroupConditionCount
	}

	// TODO: Normal case
	if conditionType == string(enum.Operator.OR) {
		isValidSatisfy = false
		for _, condition := range conditions {
			if condition.IsValid != nil && *condition.IsValid {
				isValidSatisfy = true
				percentage = 100
				break
			}
		}
	} else {
		for _, condition := range conditions {
			percentage = calculateValidConditionPercentage(conditions)
			if condition.IsValid != nil && !*condition.IsValid {
				isValidSatisfy = false
				break
			}
		}
	}
	return isValidSatisfy, percentage, 0, 0 // normal case didn't need valid & total group condition count
}

func handleProductTagConditions(conditions []*model.ConditionMessage, voucherConditionType string, conditionType string) []*model.ConditionMessage {
	finalResults := make([]*model.ConditionMessage, 0)
	groupIndexCondition := make(map[int][]*model.ConditionMessage)
	// Group conditions based on GroupIndex
	for _, condition := range conditions {
		groupIndexCondition[condition.GroupIndex] = append(groupIndexCondition[condition.GroupIndex], condition)
	}

	// If there are multiple group indexes, combine all into one subs
	if len(groupIndexCondition) > 1 {
		mainSubs := make([]*model.ConditionMessage, 0)
		// Iterate over each group of conditions
		for _, groupCondition := range groupIndexCondition {
			// Evaluate the satisfaction of the current group of conditions
			isValidSatisfy, percentage, validConditionCount, totalCondition := evaluateSubConditionSatisfaction(groupCondition, voucherConditionType)
			// format message & progressMessage
			formatPercentMessage := make([]string, 0)
			formatProgressMessage := fmt.Sprintf("Đã thỏa %d/%d điều kiện theo loại điều kiện %s", validConditionCount, totalCondition, mapVoucherConditionType[enum.ConditionTypeValue(voucherConditionType)])
			for _, condition := range groupCondition {
				formatPercentMessage = append(formatPercentMessage, fmt.Sprintf("- %s", condition.Message))
			}
			formatMessage := strings.Join(formatPercentMessage, "\n")

			formatData := &model.ConditionMessage{
				Message:             formatMessage,
				IsValid:             utils.ParseBoolToPointer(isValidSatisfy),
				PercentageCondition: &percentage,
				// Subs:                 groupCondition, // hide this subs mobile old version require
				VoucherConditionType: enum.ConditionTypeValue(voucherConditionType),
				ConditionType:        conditionType,
				ProgressMessage:      formatProgressMessage,
				// current & target will base on valid/total condition on child
				Current: utils.ParseIntToPointer(validConditionCount),
				Target:  utils.ParseIntToPointer(totalCondition),
			}
			// Add the formatted condition message to the main subs
			mainSubs = append(mainSubs, formatData)
		}
		// Evaluate the satisfaction of the combined main subs
		isMiddleValidSatisfy, middlePercentage, _, _ := evaluateConditionSatisfaction(conditionType, mainSubs, voucherConditionType)
		formatMainSubs := &model.ConditionMessage{
			Message:              handleMainMessageProductTag(len(mainSubs), voucherConditionType, conditionType),
			IsValid:              utils.ParseBoolToPointer(isMiddleValidSatisfy),
			PercentageCondition:  &middlePercentage,
			Subs:                 mainSubs,
			VoucherConditionType: enum.ConditionTypeValue(voucherConditionType),
			ConditionType:        enum.Operator.AND, // default parent condition is alawys AND
		}
		// Add the formatted condition message to the final results
		finalResults = append(finalResults, formatMainSubs)
	} else {
		// If there is only one group index, format the condition message as usual
		for _, condition := range conditions {
			condition.ConditionType = enum.Operator.AND
			finalResults = append(finalResults, condition)
		}
	}

	return finalResults
}

func handleDefaultConditions(conditions []*model.ConditionMessage, voucherConditionType string, isValidSatisfy bool, percentage float64) []*model.ConditionMessage {
	finalResults := make([]*model.ConditionMessage, 0)
	mainMessage := handleMainMessage(voucherConditionType, len(conditions))
	formatData := &model.ConditionMessage{
		Message:              mainMessage,
		IsValid:              utils.ParseBoolToPointer(isValidSatisfy),
		PercentageCondition:  &percentage,
		Subs:                 conditions,
		VoucherConditionType: enum.ConditionTypeValue(voucherConditionType),
		ConditionType:        enum.Operator.AND,
	}

	finalResults = append(finalResults, formatData)
	return finalResults
}

func handleOrderMainMessage(current, target int) string {
	if current < target {
		return fmt.Sprintf("Đã thỏa %d/%d điều kiện", current, target)
	}
	return fmt.Sprintf("Đã thỏa đủ %d/%d điều kiện để sử dụng mã", current, target)
}

func handleOrderConditions(conditions []*model.ConditionMessage, isValidSatisfy bool, percentage float64) []*model.ConditionMessage {
	finalResults := make([]*model.ConditionMessage, 0)
	target := len(conditions)
	current := 0
	for _, condition := range conditions {
		if condition.IsValid != nil && *condition.IsValid {
			current++
		}
	}

	mainMessage := handleOrderMainMessage(current, target)
	formatData := &model.ConditionMessage{
		Message:              mainMessage,
		IsValid:              utils.ParseBoolToPointer(isValidSatisfy),
		PercentageCondition:  &percentage,
		Subs:                 conditions,
		VoucherConditionType: enum.ConditionTypeValue("ORDER_VALUE"),
		ConditionType:        enum.Operator.AND,
	}

	finalResults = append(finalResults, formatData)
	return finalResults
}

func evaluateSubConditionSatisfaction(conditions []*model.ConditionMessage, voucherConditionType string) (bool, float64, int, int) {
	isValidSatisfy := true
	percentage := 0.0
	mapSpecialVoucherType := map[enum.ConditionTypeValue]bool{
		enum.ConditionType.PRODUCT_TAG: true,
	}

	// TODO: special case, return valid & total condition
	if mapSpecialVoucherType[enum.ConditionTypeValue(voucherConditionType)] {
		validGroupConditionCount := 0               // valid condition count
		totalGroupConditionCount := len(conditions) // total condition count
		for _, condition := range conditions {
			if condition.IsValid != nil && *condition.IsValid {
				validGroupConditionCount++
			}
		}

		if validGroupConditionCount == totalGroupConditionCount {
			isValidSatisfy = true
		} else {
			isValidSatisfy = false
		}

		if totalGroupConditionCount > 0 {
			percentage = (float64(validGroupConditionCount) / float64(totalGroupConditionCount)) * 100
		}
		return isValidSatisfy, percentage, validGroupConditionCount, totalGroupConditionCount
	}

	return isValidSatisfy, percentage, 0, 0 // normal case didn't need valid & total group condition count
}

func handleMainMessageProductTag(totalCondition int, voucherConditionType, conditionType string) string {
  if totalCondition > 1 {
    if conditionType == string(enum.Operator.AND) {
      if totalCondition > 1 {
        return "Thỏa các điệu kiện sau"
      }
    } else if conditionType == string(enum.Operator.OR) {
      if message, ok := ConditionMessageConfigCache.OrConditionsMessage[voucherConditionType]; ok {
        return message
      }
    }
	}
	return "" // return mesage of first condition
}
