package model

type AppValueResponse struct {
	Status  string      `json:"status"`
	Message string      `json:"message"`
	Data    []AppConfig `json:"data"`
}

type AppConfig struct {
	AppCode      string   `json:"appCode,omitempty" bson:"appCode,omitempty"`
	AppValueCode string   `json:"appValueCode,omitempty" bson:"appValueCode,omitempty"`
	Value        AppValue `json:"value,omitempty" bson:"value,omitempty"`
}

type AppValue struct {
	Key         string `json:"key,omitempty" bson:"key,omitempty"`
	ValueInt    int64  `json:"valueInt,omitempty" bson:"valueInt,omitempty"`
	Provinces   string `json:"provinces,omitempty" bson:"provinces,omitempty"`
	Districts   string `json:"districts,omitempty" bson:"districts,omitempty"`
	Wards       string `json:"wards,omitempty" bson:"wards,omitempty"`
	CustomerIds string `json:"customerIds,omitempty" bson:"customer_ids,omitempty"`
	ValString   string `json:"valString,omitempty" bson:"val_string,omitempty"`
	Object      string `json:"object,omitempty" bson:"object,omitempty"`

	DefaultParam string `json:"DefaultParam,omitempty" bson:"default_param,omitempty"`
}

type ConditionMessageConfig struct {
	OrConditionMessage  map[string]string `json:"orConditionMessage,omitempty" bson:"orConditionMessage,omitempty"`
	OrConditionsMessage map[string]string `json:"orConditionsMessage,omitempty" bson:"orConditionsMessage,omitempty"`
}
