package api

import (
	"encoding/json"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/action"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
)

// CreateLuckyWheelNotification is func create lucky wheel notification
func CreateLuckyWheelNotification(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload model.LuckyWheelNotification
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}
	return resp.Respond(action.CreateLuckyWheelNotification(getActionSource(req), &payload))
}

// UpdateLuckyWheelNotification is func update lucky wheel notification
func UpdateLuckyWheelNotification(req sdk.APIRequest, resp sdk.APIResponder) error {
	var payload model.LuckyWheelNotification
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "JSON_INVALID",
		})
	}
	return resp.Respond(action.UpdateLuckyWheelNotification(getActionSource(req), &payload))
}

// GetLuckyWheelNotification is func get lucky wheel notification
func GetLuckyWheelNotification(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GetLuckyWheelNotification(getActionSource(req), req.GetParam("code")))
}

// GetLuckyWheelNotificationList is func get lucky wheel notification list
func GetLuckyWheelNotificationList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	query := model.LuckyWheelNotification{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}
	return resp.Respond(action.GetLuckyWheelNotificationList(getActionSource(req), &query, offset, limit, getTotal))
}

// GetSelfLuckyWheelNotification is func get self lucky wheel notification
func GetSelfLuckyWheelNotification(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GetSelfLuckyWheelNotificationList(getActionSource(req), req.GetParam("luckyWheelCode")))
}
