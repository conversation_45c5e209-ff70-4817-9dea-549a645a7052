package enum

type VoucherErrorCodeType string

type voucherErrorCode struct {
	VOUCHER_NOT_ACTIVE      VoucherErrorCodeType
	VOUCHER_NOT_STARTED     VoucherErrorCodeType
	VOUCHER_EXPIRED         VoucherErrorCodeType
	VOUCHER_INVALID_USER    VoucherErrorCodeType
	VOUCHER_OUT_OF_STOCK    VoucherErrorCodeType
	VOUCHER_USED            VoucherErrorCodeType
	INVALID_APP_VERSION     VoucherErrorCodeType
	VOUCHER_NOT_FOUND       VoucherErrorCodeType
	NOT_ENOUGH_ORDER_VALUE  VoucherErrorCodeType
	NOT_ENOUGH_SKU_QUANTITY VoucherErrorCodeType
	INVALID_APPLY_DISCOUNT  VoucherErrorCodeType
	INVALID_NUMBER_APPLY    VoucherErrorCodeType
	INVALID_PAYMENT_METHOD  VoucherErrorCodeType

	NOT_ENOUGH_SKU_TYPES       VoucherErrorCodeType
	NOT_ENOUGH_SKU_VALUE       VoucherErrorCodeType
	NOT_ENOUGH_SKU_TOTAL_PRICE VoucherErrorCodeType
}

var VoucherErrorCode = &voucherErrorCode{
	VOUCHER_NOT_ACTIVE:      "VOUCHER_NOT_ACTIVE",
	VOUCHER_NOT_STARTED:     "VOUCHER_NOT_STARTED",
	VOUCHER_EXPIRED:         "VOUCHER_EXPIRED",
	VOUCHER_INVALID_USER:    "VOUCHER_INVALID_USER",
	VOUCHER_OUT_OF_STOCK:    "VOUCHER_OUT_OF_STOCK",
	VOUCHER_USED:            "VOUCHER_USED",
	INVALID_APP_VERSION:     "INVALID_APP_VERSION",
	VOUCHER_NOT_FOUND:       "VOUCHER_NOT_FOUND",
	NOT_ENOUGH_ORDER_VALUE:  "NOT_ENOUGH_ORDER_VALUE",
	NOT_ENOUGH_SKU_QUANTITY: "NOT_ENOUGH_SKU_QUANTITY",
	INVALID_APPLY_DISCOUNT:  "INVALID_APPLY_DISCOUNT",
	INVALID_NUMBER_APPLY:    "INVALID_NUMBER_APPLY",
	INVALID_PAYMENT_METHOD:  "INVALID_PAYMENT_METHOD",

	NOT_ENOUGH_SKU_TYPES:       "NOT_ENOUGH_SKU_TYPES",
	NOT_ENOUGH_SKU_VALUE:       "NOT_ENOUGH_SKU_VALUE",
	NOT_ENOUGH_SKU_TOTAL_PRICE: "NOT_ENOUGH_SKU_TOTAL_PRICE",
}

type sortParamType = struct {
	Discount        string
	MinOrder        string
	RefWishlist     string
	RefPurchase     string
	RefCart         string
	RefRecentView   string
	RefRecentSearch string
	ScopeMe         string
	RemainDay       string
	TotalDay        string
	MarketplaceOrg  string
	SellerOrg       string
	VendorOrg       string
}

var SortParam = sortParamType{
	Discount:        "discount",
	MinOrder:        "min_order",
	RefWishlist:     "ref_wishlist",
	RefPurchase:     "ref_purchase",
	RefCart:         "ref_cart",
	RefRecentView:   "ref_recent_view",
	RefRecentSearch: "ref_recent_search",
	ScopeMe:         "scope_me",
	RemainDay:       "remain_day",
	TotalDay:        "total_day",
	MarketplaceOrg:  "marketplace_org",
	SellerOrg:       "seller_org",
	VendorOrg:       "vendor_org",
}
