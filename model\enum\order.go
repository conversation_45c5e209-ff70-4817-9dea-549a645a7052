package enum

// OrderStateValue ...
type OrderStateValue string

type orderStateValue struct {
	WaitConfirm OrderStateValue
	Confirmed   OrderStateValue
	Canceled    OrderStateValue
	Processing  OrderStateValue
	Completed   OrderStateValue
	Delivering  OrderStateValue
}

// OrderState ...
var OrderState = &orderStateValue{
	"WAIT_TO_CONFIRM",
	"CONFIRMED",
	"CANCEL",
	"PROCESSING",
	"COMPLETED",
	"DELIVERING",
}

// IsOrderStatus is func check customer type
func IsOrderStatus(val string) bool {
	switch OrderStateValue(val) {
	case OrderState.WaitConfirm, OrderState.Canceled,
		OrderState.Completed, OrderState.Delivering,
		OrderState.Processing, OrderState.Confirmed:
		{
			return true
		}
	default:
		return false
	}
}

// OrderConfirmTypeValue ...
type OrderConfirmTypeValue string

type orderConfirmTypeValue struct {
	Auto   OrderConfirmTypeValue
	Manual OrderConfirmTypeValue
}

// OrderConfirmType ...
var OrderConfirmType = &orderConfirmTypeValue{
	"AUTO",
	"MANUAL",
}

//ReconciliationStatusValue
type ReconciliationStatusValue string

type reconciliationStatus struct {
	Waiting   ReconciliationStatusValue
	Completed ReconciliationStatusValue
}

// ReconciliationStatus ...
var ReconciliationStatus = &reconciliationStatus{
	"WAITING",
	"COMPLETED",
}

// OrderSellerStateValue ...
type OrderSellerStateValue string

type orderSellerStateValue struct {
	Draft             OrderSellerStateValue
	WaitToConfirm     OrderSellerStateValue
	Confirmed         OrderSellerStateValue
	WaitingToPick     OrderSellerStateValue
	WaitingToPack     OrderSellerStateValue
	WaitingToDelivery OrderSellerStateValue
	PickedByCarrier   OrderSellerStateValue
	Delivering        OrderSellerStateValue
	Delivered         OrderSellerStateValue
	Completed         OrderSellerStateValue
	Cancelled         OrderSellerStateValue
}

// OrderSellerState ...
var OrderSellerState = &orderSellerStateValue{
	"DRAFT",
	"WAIT_TO_CONFIRM",
	"CONFIRMED",
	"WAITING_TO_PICK",
	"WAITING_TO_PACK",
	"WAITING_TO_DELIVERY",
	"PICKED_BY_CARRIER",
	"DELIVERING",
	"DELIVERED",
	"COMPLETED",
	"CANCELLED",
}
