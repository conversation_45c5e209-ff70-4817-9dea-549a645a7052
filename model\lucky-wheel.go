package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type LuckyWheel struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code          string `json:"code,omitempty" bson:"code,omitempty"`
	Description   string `json:"description,omitempty" bson:"description,omitempty"`
	IsActive      *bool  `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Name          string `json:"name,omitempty" bson:"name,omitempty"`
	SystemDisplay string `json:"systemDisplay,omitempty" bson:"system_display,omitempty"` // BUYMED, CIRCA, ...
	Type          string `json:"type,omitempty" bson:"type,omitempty"`                    // LUCKY_WHEEL, LUCKY_MONEY_ENVELOPE,...

	HashTag string `json:"-" bson:"hash_tag,omitempty"` // to search

	StartTime        *time.Time       `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime          *time.Time       `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Scope            *LuckyWheelScope `json:"scope,omitempty" bson:"scope,omitempty"`
	IsAcceptFreeTurn *bool            `json:"isAcceptFreeTurn,omitempty" bson:"is_accept_free_turn,omitempty"`
	TimeToFreeTurn   int              `json:"timeToFreeTurn,omitempty" bson:"time_to_free_turn,omitempty"` // minutes
	IsEnableShare    *bool            `json:"isEnableShare,omitempty" bson:"is_enable_share,omitempty"`
	GameUrl          *string          `json:"gameUrl,omitempty" bson:"game_url,omitempty"`

	VersionUpdate string `json:"versionUpdate,omitempty" bson:"version_update,omitempty"`

	Items            []*LuckyWheelItem          `json:"items,omitempty" bson:"-"`
	Turns            int64                      `json:"turns" bson:"-"`
	Points           int64                      `json:"points" bson:"-"`
	NextFreeTime     *time.Time                 `json:"nextFreeTime,omitempty" bson:"-"`
	ItemDescriptions *LuckyWheelItemDescription `json:"itemDescriptions,omitempty" bson:"-"`

	SharingLink      *string `json:"sharingLink,omitempty" bson:"sharing_link,omitempty"`
	BackgroundWeb    *string `json:"backgroundWeb,omitempty" bson:"background_web,omitempty"`
	BackgroundMobile *string `json:"backgroundMobile,omitempty" bson:"background_mobile,omitempty"`
	BannerWeb        *string `json:"bannerWeb,omitempty" bson:"banner_web,omitempty"`
	BannerMobile     *string `json:"bannerMobile,omitempty" bson:"banner_mobile,omitempty"`          //Full image for only one lucky wheel
	BannerMobileHalf *string `json:"bannerMobileHalf,omitempty" bson:"banner_mobile_half,omitempty"` //Size image for more than 2 lucky wheels
	ImageTitleWeb    *string `json:"imageTitleWeb,omitempty" bson:"image_title_web,omitempty"`
	ImageTitleMobile *string `json:"imageTitleMobile,omitempty" bson:"image_title_mobile,omitempty"`
	MainImage        *string `json:"mainImage,omitempty" bson:"main_image,omitempty"`
	MainImageMobile  *string `json:"mainImageMobile,omitempty" bson:"main_image_mobile,omitempty"`

	//Gift
	MainGif       *string `json:"mainGif,omitempty" bson:"main_gif,omitempty"`
	MainGifMobile *string `json:"mainGifMobile,omitempty" bson:"main_gif_mobile,omitempty"`
	RewardGif     *string `json:"rewardGif,omitempty" bson:"reward_gif,omitempty"`
	MusicUrl      *string `json:"musicUrl,omitempty" bson:"music_url,omitempty"`

	LimitItemGroup map[string]int64 `json:"limitItemGroup,omitempty" bson:"limit_item_group,omitempty"`

	//query
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

type LuckyWheelScope struct {
	ProvinceCodes     []string `json:"provinceCodes,omitempty" bson:"province_codes,omitempty"`
	RegionCodes       []string `json:"regionCodes,omitempty" bson:"region_codes,omitempty"`
	CustomerLevels    []string `json:"levels,omitempty" bson:"levels,omitempty"`
	CustomerScopes    []string `json:"scopes,omitempty" bson:"scopes,omitempty"`
	CustomerApplyType string   `json:"customerApplyType,omitempty" bson:"customer_apply_type,omitempty"` // ALL || MANY
}

type LuckyWheelItem struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CreatedBy       int64              `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	LuckyWheelCode string `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`

	ItemName               string                   `json:"itemName,omitempty" bson:"item_name,omitempty"`
	ItemCode               string                   `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	MaxQuantity            *int64                   `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`
	ImageUrl               string                   `json:"itemUrl,omitempty" bson:"item_url,omitempty"`
	Reward                 *[]*LuckyWheelItemReward `json:"reward,omitempty" bson:"reward,omitempty"`
	Percentage             *float64                 `json:"percentage,omitempty" bson:"percentage,omitempty"`
	IsActive               *bool                    `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Index                  int64                    `json:"index,omitempty" bson:"index,omitempty"`
	MaxQuantityPerDay      *int64                   `json:"maxQuantityPerDay,omitempty" bson:"max_quantity_per_day,omitempty"`
	MaxQuantityPerCustomer *int64                   `json:"maxQuantityPerCustomer,omitempty" bson:"max_quantity_per_customer,omitempty"`
	UsedQuantity           int64                    `json:"usedQuantity,omitempty" bson:"used_quantity,omitempty"`
	UsedItemQuantity       *map[string]int64        `json:"usedItemQuantity,omitempty" bson:"used_item_quantity,omitempty"`
	BackgroundColor        string                   `json:"backgroundColor,omitempty" bson:"background_color,omitempty"`
	RewardDescription      *string                  `json:"rewardDescription,omitempty" bson:"reward_description,omitempty"`
	StartTime              *time.Time               `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime                *time.Time               `json:"endTime,omitempty" bson:"end_time,omitempty"`
	ActionName             *string                  `json:"actionName,omitempty" bson:"action_name,omitempty"`
	ActionLink             *string                  `json:"actionLink,omitempty" bson:"action_link,omitempty"`
	IsIncreasePercentage   *bool                    `json:"isIncreasePercentage,omitempty" bson:"is_increase_percentage,omitempty"`
	LuckyWheelEventCode    string                   `json:"luckyWheelEventCode,omitempty" bson:"lucky_wheel_event_code,omitempty"`
	IsApplyForEvent        *bool                    `json:"isApplyForEvent,omitempty" bson:"is_apply_for_event,omitempty"`
	GroupCode              *string                  `json:"groupCode,omitempty" bson:"group_code,omitempty"`

	IsNotify      *bool   `json:"isNotify,omitempty" bson:"is_notify,omitempty"`
	ContentNotify *string `json:"contentNotify,omitempty" bson:"content_notify,omitempty"`
	UrlNotify     *string `json:"urlNotify,omitempty" bson:"url_notify,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

type LuckyWheelItemReward struct {
	TypeReward            enum.LuckyWheelItemRewardType `json:"typeReward,omitempty" bson:"type_reward,omitempty"`
	RewardDescription     string                        `json:"rewardDescription,omitempty" bson:"reward_description,omitempty"`
	Points                int64                         `json:"points,omitempty" bson:"points,omitempty"`
	LkPoints              int64                         `json:"lkPoints,omitempty" bson:"lk_points,omitempty"`
	PromotionID           int64                         `json:"promotionID,omitempty" bson:"promotion_id,omitempty"`
	TicketPattern         string                        `json:"ticketPattern,omitempty" bson:"ticket_pattern,omitempty"`
	VoucherPattern        *string                       `json:"voucherPattern,omitempty" bson:"voucher_pattern,omitempty"`
	TurnsRotation         int64                         `json:"turnsRotation,omitempty" bson:"turns_rotation,omitempty"`
	VoucherID             int64                         `json:"voucherID,omitempty" bson:"-"`
	VoucherCode           string                        `json:"voucherCode,omitempty" bson:"-"`
	PieceCode             string                        `json:"pieceCode,omitempty" bson:"piece_code,omitempty"`
	ComboCode             string                        `json:"comboCode,omitempty" bson:"combo_code,omitempty"`
	NumberOfDayUseVoucher *int64                        `json:"numberOfDayUseVoucher,omitempty" bson:"number_of_day_use_voucher,omitempty"`
}

type LuckyWheelItemDescription struct {
	ID                  primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	LuckyWheelCode      string             `json:"luckyWheelCode,omitempty" bson:"lucky_wheel_code,omitempty"`
	LuckyWheelEventCode string             `json:"luckyWheelEventCode,omitempty" bson:"lucky_wheel_event_code,omitempty"`
	IsApplyForEvent     *bool              `json:"isApplyForEvent,omitempty" bson:"is_apply_for_event,omitempty"`
	Code                string             `json:"code,omitempty" bson:"code,omitempty"`

	Items *[]*struct {
		ItemName        string `json:"itemName,omitempty" bson:"item_name,omitempty"`
		ImageUrl        string `json:"imageUrl,omitempty" bson:"image_url,omitempty"`
		Description     string `json:"description,omitempty" bson:"description,omitempty"`
		DisplayPriority *int64 `json:"displayPriority,omitempty" bson:"display_priority,omitempty"`
	} `json:"items,omitempty" bson:"items,omitempty"`
}

var LuckyWheelDB = &db.Instance{
	ColName:        "lucky_wheel",
	TemplateObject: &LuckyWheel{},
}

// InitLuckyWheelModel is func init model
func InitLuckyWheelModel(s *mongo.Database) {
	LuckyWheelDB.ApplyDatabase(s)
}

var LuckyWheelItemDB = &db.Instance{
	ColName:        "lucky_wheel_item",
	TemplateObject: &LuckyWheelItem{},
}

// InitLuckyWheelItemModel is func init model
func InitLuckyWheelItemModel(s *mongo.Database) {
	LuckyWheelItemDB.ApplyDatabase(s)
}

var LuckyWheelItemDescriptionDB = &db.Instance{
	ColName:        "lucky_wheel_item_description",
	TemplateObject: &LuckyWheelItemDescription{},
}

// InitLuckyWheelItemModel is func init model
func InitLuckyWheelItemsDescriptionModel(s *mongo.Database) {
	LuckyWheelItemDescriptionDB.ApplyDatabase(s)
}
