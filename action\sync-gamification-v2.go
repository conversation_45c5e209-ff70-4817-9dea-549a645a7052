package action

import (
	"fmt"
	"runtime/debug"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/promotion/model/cache"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/promotion/client"
	"gitlab.com/thuocsi.vn/marketplace/promotion/conf"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/promotion/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetGamificationLogList(query *model.SyncGamificationLog, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.SyncGamificationLogDB.Query(query, offset, limit, &primitive.M{"created_time": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		result.Total = model.SyncGamificationLogDB.Count(query).Total
	}
	return result
}

func SyncGamificationResult(acc *model.Account, createdBySystem string) *common.APIResponse {
	go func() {
		syncGamificationResult(acc, enum.SyncGamification.MANUAL, createdBySystem)
	}()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Đang tiến hành cập nhật kết quả chương trình trả thưởng",
	}
}

// SyncGamificationResultTask ...
func SyncGamificationResultTask(item *job.JobItem) (returnErr error) {
	if conf.Config.Env == "uat" {
		return nil
	}
	now := time.Now()
	go syncGamificationResult(nil, enum.SyncGamification.AUTO, "ALL")
	lastDay := time.Date(now.Year(), now.Month(), now.Day()+1, 15, 0, 0, 0, now.Location())
	model.SyncGamificationResultJobExecutor.Push(nil, &job.JobItemMetadata{
		Topic:     "default",
		ReadyTime: &lastDay,
	})
	return nil
}

func syncGamificationResult(acc *model.Account, syncGamificationType enum.SyncGamificationType, createdBySystem string) *common.APIResponse {
	now := time.Now()

	gamificationList := getGamificationToSync2(createdBySystem)
	ids := make([]*int64, 0)
	for _, gamification := range gamificationList {
		ids = append(ids, &gamification.GamificationID)
	}
	//resultList := make([]*model.GamificationResult, 0)
	//detailList := getGamificationDetailToSync2(ids)
	//detailMap := makeGamificationDetailMap(detailList)
	for _, g := range gamificationList {
		// NOTE: log sync start
		log := &model.SyncGamificationLog{
			StartTime:        &now,
			OrderFromTime:    g.StartTime,
			OrderToTime:      g.EndCalResultTime,
			GamificationCode: g.GamificationCode,
			GamificationID:   g.GamificationID,
			Status:           enum.GamificationLogStatus.IN_PROCESS,
			SystemStatus:     enum.GamificationLogStatus.TODO,
			Type:             syncGamificationType,
		}
		if acc != nil {
			log.AccountID = acc.AccountID
		}
		log.Code = model.GenCodeWithTime()
		_ = model.SyncGamificationLogDB.Create(log)
		_, err := client.Services.Order.CountOrderValue(&model.RequestCountOrderPoint{
			ConfirmFrom:             g.StartTime,
			ConfirmTo:               g.EndTime,
			SellerCode:              g.SellerCode,
			LogGamificationSyncCode: log.Code,
			GamificationCode:        g.GamificationCode,
			SystemDisplay:           string(g.SystemDisplay),
		})
		if err != nil {
			errRes := &common.APIResponse{
				Status:    common.APIStatus.Error,
				Message:   err.Error(),
				ErrorCode: "GET_ORDER_FAIL",
			}
			logSyncGamificationFail(log, errRes)
			return errRes
		}
		// sleep 1p
		time.Sleep(2 * time.Minute)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Sync success",
	}
}

func SyncGamificationFromOrder(values1 []*model.OrderCountPoint, logCode string, systemDisplay string) *common.APIResponse {
	now := time.Now()
	readyTime := now
	if conf.Config.Env == "stg" {
		//readyTime = now.Add(0 * time.Minute)
	} else {
		readyTime = now.Add(5 * time.Minute)
	}

	_ = model.SyncGamificationJob.Push(model.SyncGamificationLog{Code: logCode}, &job.JobItemMetadata{
		UniqueKey: logCode,
		SortedKey: logCode,
		Keys:      []string{logCode},
		ReadyTime: &readyTime,
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Sync success",
	}
}

func SyncGamificationConsumer(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var syncData model.SyncGamificationLog
	err = bson.Unmarshal(data, &syncData)
	if err != nil {
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic SyncGamificationConsumer : %s", string(debug.Stack()))
			fmt.Println(err.Error())
		}
	}()
	res := excuseSyncGamification(syncData.Code)
	if res.Status != common.APIStatus.Ok {
		return fmt.Errorf(res.Message)
	}
	return nil
}

func excuseSyncGamification(logCode string) *common.APIResponse {
	qLog := model.SyncGamificationLogDB.QueryOne(model.SyncGamificationLog{Code: logCode, SystemStatus: enum.GamificationLogStatus.TODO})
	if qLog.Status != common.APIStatus.Ok {
		return qLog
	}
	updateStatusLog := model.SyncGamificationLogDB.UpdateOne(model.SyncGamificationLog{Code: logCode, SystemStatus: enum.GamificationLogStatus.TODO}, model.SyncGamificationLog{SystemStatus: enum.GamificationLogStatus.IN_PROCESS})
	if updateStatusLog.Status != common.APIStatus.Ok {
		return qLog
	}
	log := qLog.Data.([]*model.SyncGamificationLog)[0]
	qGamification := model.GamificationDB.QueryOne(model.Gamification{GamificationID: log.GamificationID})
	if qGamification.Status != common.APIStatus.Ok {
		return qGamification
	}
	g := qGamification.Data.([]*model.Gamification)[0]
	detailList := getGamificationDetailToSync2([]*int64{&log.GamificationID})
	detailMap := makeGamificationDetailMap(detailList)
	offset, limit := int64(0), int64(100)
	for {
		qCurrentResult := model.GamificationResultDB.Query(&model.GamificationResult{GamificationID: g.GamificationID}, offset*limit, limit, nil)
		if qCurrentResult.Status == common.APIStatus.Ok {
			for _, cur := range qCurrentResult.Data.([]*model.GamificationResult) {
				cur.LogCode = logCode
				cur.ID = primitive.NilObjectID
				createBackup := model.GamificationResultDeletedDB.Create(cur)
				if createBackup.Status != common.APIStatus.Ok {
					logSyncGamificationFail(&model.SyncGamificationLog{Code: logCode}, createBackup)
					return createBackup
				}
			}
			offset++
		} else {
			break
		}
	}
	qDeleteResult := model.GamificationResultDB.Delete(&model.GamificationResult{GamificationID: g.GamificationID})
	if qDeleteResult.Status != common.APIStatus.Ok {
		logSyncGamificationFail(&model.SyncGamificationLog{Code: logCode}, qDeleteResult)
		return qDeleteResult
	}
	customerMap := make(map[int64]*model.Customer)
	customerIDs := make([]int, 0)
	values, customers := getGamificationValues(log, g)
	customerBlackList := getGamificationCustomerBlackList(g)

	for _, v := range values {
		if _, ok := customerBlackList[int64(v.CustomerId)]; ok {
			continue
		}
		customerIDs = append(customerIDs, v.CustomerId)
		if len(customerIDs) == 500 {
			if customers, err := client.Services.Customer.GetListCustomerByIDs(customerIDs); err == nil {
				for _, customer := range customers {
					customerMap[customer.CustomerID] = customer
				}
			}
			customerIDs = make([]int, 0)
		}
	}
	if customers, err := client.Services.Customer.GetListCustomerByIDs(customerIDs); err == nil {
		for _, customer := range customers {
			customerMap[customer.CustomerID] = customer
		}
	}

	for _, value := range values {

		if details, ok := detailMap[g.GamificationCode]; ok && len(details) > 0 {
			for _, detail := range details {
				customer := customerMap[int64(value.CustomerId)]
				if customer == nil {
					continue
				}
				// NOTE: validate customer
				if isValid := isValidCustomerWithGamification(customer, g, customers); !isValid {
					continue
				}

				result := model.GamificationResult{
					GamificationCode:       detail.GamificationCode,
					GamificationID:         detail.GamificationID,
					GamificationDetailCode: detail.GamificationDetailCode,
					GamificationDetailID:   detail.GamificationDetailID,
					Status:                 enum.GamificationResultStatus.IN_PROGRESS,
					AccountID:              customer.AccountID,
					CustomerID:             customer.CustomerID,
					CustomerScope:          customer.Scope,
					//Value:                  value.TotalPrice,
					//OrderIds:               value.Orders,
					//OrderValues:            value.OrderValues,
				}
				switch detail.Condition.Type {
				case enum.GamificationConditionType.TotalActualPrice:
					result.Value = 0
					for _, v := range value.OrderValues {
						if v.Status == "COMPLETED" {
							result.Value = result.Value + v.TotalActualPrice
							result.OrderIds = append(result.OrderIds, v.OrderID)
							result.OrderValues = append(result.OrderValues, v)
						}
					}
					if result.Value == 0 {
						continue
					}
				case enum.GamificationConditionType.TotalOrderPrice:
					result.Value = 0
					for _, v := range value.OrderValues {
						result.Value = result.Value + v.TotalPrice
						result.OrderIds = append(result.OrderIds, v.OrderID)
						result.OrderValues = append(result.OrderValues, v)
					}
					if result.Value == 0 {
						continue
					}
				case enum.GamificationConditionType.TotalOrderPriceWithDelivered:
					result.Value = 0
					for _, v := range value.OrderValues {
						if v.Status == "COMPLETED" || v.Status == "DELIVERED" {
							result.Value = result.Value + v.TotalPrice
							result.OrderIds = append(result.OrderIds, v.OrderID)
							result.OrderValues = append(result.OrderValues, v)
						}
					}
					if result.Value == 0 {
						continue
					}
				}

				if result.Value >= detail.Condition.Target {
					result.Status = enum.GamificationResultStatus.COMPLETED
				}
				result.GamificationResultID, result.GamificationResultCode = model.GenGamificationResult()
				createResult := model.GamificationResultDB.Create(result)
				if createResult.Status != common.APIStatus.Ok {
					logSyncGamificationFail(log, createResult)
					return createResult
				}
			}
		}
	}
	numberOfCustomerJoin := model.GamificationResultDB.Count(model.GamificationResult{GamificationCode: g.GamificationCode}).Total
	numberOfCustomerCompleted := model.GamificationResultDB.Count(model.GamificationResult{GamificationCode: g.GamificationCode, Status: enum.GamificationResultStatus.COMPLETED}).Total
	updateGamificationStatistic := model.GamificationDB.UpdateOne(model.Gamification{GamificationCode: g.GamificationCode},
		model.Gamification{NumberOfJoinedCustomer: utils.ParseIntToPointer(int(numberOfCustomerJoin)), NumberOfCompletedCustomer: utils.ParseIntToPointer(int(numberOfCustomerCompleted))})
	if updateGamificationStatistic.Status != common.APIStatus.Ok {
		logSyncGamificationFail(log, updateGamificationStatistic)
		return updateGamificationStatistic
	}
	end := time.Now()
	return model.SyncGamificationLogDB.UpdateOne(model.SyncGamificationLog{Code: log.Code},
		model.SyncGamificationLog{EndTime: &end, Status: enum.GamificationLogStatus.DONE, NumberOfCompletedCustomer: int(numberOfCustomerCompleted), NumberOfJoinedCustomer: int(numberOfCustomerJoin)})

}

func getGamificationValues(log *model.SyncGamificationLog, g *model.Gamification) ([]*cache.GamificationValue, []*model.GamificationCustomer) {
	values := make([]*cache.GamificationValue, 0)
	offset, limit := int64(0), int64(1000)
	customers := getGamificationCustomers(g)
	if len(customers) > 0 {
		customerCondition := make([]int64, 0)
		for _, customer := range customers {
			customerCondition = append(customerCondition, customer.CustomerID)
			if len(customerCondition) == 500 {
				qValue := cache.GamificationValueDB.Query(cache.GamificationValue{LogSyncGamificationCode: log.Code, ComplexQuery: []*bson.M{
					{
						"customer_id": bson.M{"$in": customerCondition},
					},
				}}, 0, int64(len(customerCondition)), nil)
				//fmt.Println("DEBUG 320", qValue.Message, log.Code, len(customerCondition))
				if qValue.Status == common.APIStatus.Ok {
					for _, value := range qValue.Data.([]*cache.GamificationValue) {
						values = append(values, value)
					}
				}
				customerCondition = make([]int64, 0)
			}
		}
		qValue := cache.GamificationValueDB.Query(cache.GamificationValue{LogSyncGamificationCode: log.Code, ComplexQuery: []*bson.M{
			{
				"customer_id": bson.M{"$in": customerCondition},
			},
		}}, 0, int64(len(customerCondition)), nil)
		if qValue.Status != common.APIStatus.Ok {
			return values, customers
		}
		for _, value := range qValue.Data.([]*cache.GamificationValue) {
			values = append(values, value)
		}
	} else if g.Scope != nil && (g.Scope.CustomerApplyType == "" || g.Scope.CustomerApplyType == "ALL") {
		for {
			qValue := cache.GamificationValueDB.Query(cache.GamificationValue{LogSyncGamificationCode: log.Code}, offset*limit, limit, nil)
			if qValue.Status != common.APIStatus.Ok {
				return values, customers
			}
			for _, value := range qValue.Data.([]*cache.GamificationValue) {
				values = append(values, value)
			}
			offset++
		}
	}
	return values, customers
}

func logSyncGamificationFail(log *model.SyncGamificationLog, err *common.APIResponse) {
	now := time.Now()
	model.SyncGamificationLogDB.UpdateOne(model.SyncGamificationLog{Code: log.Code},
		model.SyncGamificationLog{Status: enum.GamificationLogStatus.FAIL, FailReason: fmt.Sprintf("%s-%s", err.ErrorCode, err.Message), EndTime: &now})
}

func isValidCustomerWithGamification(customer *model.Customer, gamification *model.Gamification, gamificationCustomers []*model.GamificationCustomer) bool {
	if gamification.Scope != nil {
		isValidScope := false
		isValidCustomerID := false
		isValidRegion := false
		isValidLevel := false
		isValidCustomerActiveDate := false

		if len(gamification.Scope.CustomerScopes) == 0 {
			isValidScope = true
		} else {
			for _, scope := range gamification.Scope.CustomerScopes {
				if customer.Scope == scope {
					isValidScope = true
				}
				if isValidScope {
					break
				}
			}
		}

		if len(gamification.Scope.CustomerAreas) == 0 {
			isValidRegion = true
		} else {
			for _, region := range gamification.Scope.CustomerAreas {
				if customer.ProvinceCode == region {
					isValidRegion = true
				}
				if isValidRegion {
					break
				}
			}
		}

		if len(gamification.Scope.CustomerLevels) == 0 {
			isValidLevel = true
		} else {
			for _, level := range gamification.Scope.CustomerLevels {
				if customer.Level == level {
					isValidLevel = true
				}
				if isValidLevel {
					break
				}
			}
		}

		if gamification.Scope.CustomerApplyType != "MANY" && (gamification.RequireSubmit == nil || (gamification.RequireSubmit != nil && !*gamification.RequireSubmit)) {
			isValidCustomerID = true
		} else {
			for _, v := range gamificationCustomers {

				if customer.CustomerID == v.CustomerID {
					isValidCustomerID = true
				}
				if isValidCustomerID {
					break
				}
			}
		}
		if customer.ConfirmedTime == nil {
			customer.ConfirmedTime = customer.CreatedTime
		}
		if gamification.CustomerActiveDate == nil {
			isValidCustomerActiveDate = true
		} else {
			// check customer active date into gamification active date and gamification end time
			if customer.ConfirmedTime != nil && customer.ConfirmedTime.After(*gamification.CustomerActiveDate) && customer.ConfirmedTime.Before(*gamification.EndTime) {
				isValidCustomerActiveDate = true
			}
		}

		return isValidScope && isValidCustomerID && isValidRegion && isValidLevel && isValidCustomerActiveDate
	} else {
		return true
	}
}

func getGamificationToSync2(createdBySystem string) []*model.Gamification {
	gamificationList := make([]*model.Gamification, 0)
	now := time.Now()
	query := model.Gamification{
		IsActive: utils.ParseBoolToPointer(true),
		ComplexQuery: []*bson.M{
			{
				"start_time":          bson.M{"$lte": now},
				"end_cal_result_time": bson.M{"$gte": now},
				"$or": []*bson.M{
					{
						"is_score_real_time": bson.M{"$exists": false},
					},
					{
						"is_score_real_time": false,
					},
				},
			},
		}}
	if createdBySystem != "ALL" {
		query.CreatedBySystem = createdBySystem
	}
	if createdBySystem == "" {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_by_system": nil,
		})
	}
	if qGamification := model.GamificationDB.Query(query, 0, 0, nil); qGamification.Status == common.APIStatus.Ok {
		gamificationList = qGamification.Data.([]*model.Gamification)
	}
	return gamificationList
}

func getGamificationDetailToSync2(ids []*int64) []*model.GamificationDetail {
	details := make([]*model.GamificationDetail, 0)
	queryDetail := &model.Gamification{ComplexQuery: []*bson.M{
		{
			"gamification_id": bson.M{"$in": ids},
		},
	}}
	offset := int64(0)
	limit := int64(1000)
	for {
		if qDetail := model.GamificationDetailDB.Query(queryDetail, offset*limit, limit, nil); qDetail.Status == common.APIStatus.Ok {
			details = append(details, qDetail.Data.([]*model.GamificationDetail)...)
			offset++
		} else {
			break
		}
	}
	return details
}

func makeGamificationDetailMap(results []*model.GamificationDetail) map[string][]*model.GamificationDetail {
	resultMap := make(map[string][]*model.GamificationDetail)
	for _, result := range results {
		resultMap[result.GamificationCode] = append(resultMap[result.GamificationCode], result)
	}
	return resultMap
}

func getGamificationCustomers(gamification *model.Gamification) []*model.GamificationCustomer {
	gamificationCustomers := make([]*model.GamificationCustomer, 0)
	if gamification.Scope != nil && (gamification.Scope.CustomerApplyType == "MANY" || (gamification.RequireSubmit != nil && *gamification.RequireSubmit)) {
		offset, limit := int64(0), int64(1000)
		for {
			query := model.GamificationCustomer{GamificationID: gamification.GamificationID}
			if gamification.RequireSubmit != nil && *gamification.RequireSubmit {
				query.Status = "ACTIVE"
			}
			qCustomer := model.GamificationCustomerDB.Query(query, offset*limit, limit, nil)
			if qCustomer.Status != common.APIStatus.Ok {
				return gamificationCustomers
			}
			gamificationCustomers = append(gamificationCustomers, qCustomer.Data.([]*model.GamificationCustomer)...)
			offset++
		}
	}
	return gamificationCustomers
}

func getGamificationCustomerBlackList(gamification *model.Gamification) map[int64]bool {
	mapCustomerBlackList := make(map[int64]bool)
	offset, limit := int64(0), int64(1000)
	for {
		qCustomer := model.GamificationCustomerDB.Query(model.GamificationCustomer{GamificationID: gamification.GamificationID, Status: "BLACKLIST"}, offset*limit, limit, nil)
		if qCustomer.Status != common.APIStatus.Ok {
			return mapCustomerBlackList
		}
		for _, customer := range qCustomer.Data.([]*model.GamificationCustomer) {
			mapCustomerBlackList[customer.CustomerID] = true
		}
		offset++
	}
}
