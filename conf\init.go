package conf

import (
	"fmt"
	"os"

	"gitlab.buymed.tech/sdk/golang/configuration"
)

type config struct {
	Env      string ``
	Protocol string
	Version  string
	//MainDBName       string
	//MainAuthDB       string
	//CacheDBName      string
	//LogDBName        string
	//LogAuthDB        string
	//APIHost          string
	CircaAPIHost     string
	CircaCallbackKey string
	//APIKey           string
	JobDBName            string
	JobAuthDB            string
	SkipIndex            bool
	MainDBConf           configuration.Database
	LogDBConf            configuration.Database
	JobDBConf            configuration.Database
	CacheDBConf          configuration.Database
	PromotionCacheDBConf configuration.Database
	CustomerCacheDBConf  configuration.Database

	StorageName string
	StorageKey  []byte // Credential JSON

	APIHost          string
	APIKey           string
	AppConfigCode    string
	WarmupConfigData int // minutes

	BuymedAPIHost string
	BuymedAPIKey  string
}

// Config main config object
var Config *config
var DENTAL_SELLERS []string = []string{"DENX"}

func init() {
	env := os.Getenv("env")
	if env == "" {
		env = "dev"
	}
	protocol := os.Getenv("protocol")
	version := os.Getenv("version")
	switch env {

	// config for dev
	case "dev":
		Config = &config{
			Env:      "dev",
			Protocol: protocol,
			Version:  version,
			//MainDBName:  "marketplace_dev_promotion",
			//MainAuthDB:  "admin",
			//CacheDBName: "marketplace_dev_product-v2",
			//LogDBName:   "marketplace_dev_promotion_log",
			//LogAuthDB:   "admin",
			//APIHost:     "https://api.v2-dev.thuocsi.vn",
			//APIKey:      "Basic UEFSVE5FUi92Mi5jdXN0b21lci5jdXN0b21lcjpWNHRqTDI5UVQ0",
			//JobDBName:   "marketplace_dev_promotion_job",
			//JobAuthDB:   "admin",
			SkipIndex:        false,
			AppConfigCode:    "XKU72AXH",
			WarmupConfigData: 3 * 60,
		}
		break

	// config for staging
	case "stg":
		Config = &config{
			Env:      "stg",
			Protocol: protocol,
			Version:  version,
			//MainDBName:  "marketplace_stg_promotion",
			//MainAuthDB:  "admin",
			//CacheDBName: "marketplace_stg_product-v2",
			//LogDBName:   "marketplace_stg_promotion_log",
			//LogAuthDB:   "admin",
			//APIHost:     "http://proxy-service.frontend-stg",
			////APIHost:     "https://api.v2-stg.thuocsi.vn",
			//CircaAPIHost:     "https://api-stg.circa.vn",
			//CircaCallbackKey: "Ea6vS2VRqzEtKl7",
			//APIKey:           "Basic UEFSVE5FUi92Mi5jdXN0b21lci5jdXN0b21lcjpWNHRqTDI5UVQ0",
			//JobDBName:        "marketplace_stg_promotion_job",
			//JobAuthDB:        "admin",
			SkipIndex:        false,
			AppConfigCode:    "XKU72AXH",
			WarmupConfigData: 3 * 60,
		}

		DENTAL_SELLERS = append(DENTAL_SELLERS, "TJRRRRRRRR")
		break
	case "uat":
		Config = &config{
			Env:      "uat",
			Protocol: protocol,
			Version:  version,
			//MainDBName:       "marketplace_prd_promotion",
			//MainAuthDB:       "marketplace_prd_promotion",
			//CacheDBName:      "marketplace_prd_product-v2",
			//LogDBName:        "marketplace_uat_promotion_log",
			//LogAuthDB:        "admin",
			//APIHost:          "https://api.v2-uat.thuocsi.vn",
			//APIKey:           "Basic UEFSVE5FUi92Mi5tYXJrZXRwbGFjZS5wcm9tb3Rpb246U0xRSlFIUktESEZLRFJFSUNCMjM4NDI3",
			//JobDBName:        "marketplace_uat_promotion_job",
			//JobAuthDB:        "admin",
			//SkipIndex:        true,
			//CircaAPIHost:     "https://api.circa.vn",
			//CircaCallbackKey: "Gov7Di5N7PdIe2q",
			AppConfigCode:    "XKU72AXH",
			WarmupConfigData: 10 * 60,
		}
		DENTAL_SELLERS = append(DENTAL_SELLERS, "FD1YACBVI4")
		break
	case "prd":
		Config = &config{
			Env:      "prd",
			Protocol: protocol,
			Version:  version,
			//MainDBName:       "marketplace_prd_promotion",
			//MainAuthDB:       "marketplace_prd_promotion",
			//CacheDBName:      "marketplace_prd_product-v2",
			//LogDBName:        "marketplace_prd_promotion_log",
			//LogAuthDB:        "admin",
			//APIHost:          "http://proxy-service.frontend-prd",
			//APIKey:           "Basic UEFSVE5FUi92Mi5tYXJrZXRwbGFjZS5wcm9tb3Rpb246U0xRSlFIUktESEZLRFJFSUNCMjM4NDI3",
			//JobDBName:        "marketplace_prd_promotion_job",
			//JobAuthDB:        "admin",
			SkipIndex: true,
			//CircaAPIHost:     "https://api.circa.vn",
			//CircaCallbackKey: "Gov7Di5N7PdIe2q",
			AppConfigCode:    "XKU72AXH",
			WarmupConfigData: 10 * 60,
		}
		DENTAL_SELLERS = append(DENTAL_SELLERS, "FD1YACBVI4")
		break
	}
	if protocol == "" {
		protocol = "THRIFT"
	}
	// DB Config
	{
		dbFormat := "marketplace_%s_promotion"
		dbLogFormat := dbFormat + "_log"
		dbJobFormat := dbFormat + "_job"

		dbCacheFormat := "marketplace_%s_product-v2"
		dbPromotionCacheFormat := "marketplace_%s_promotion"
		dbCustomerCacheFormat := "marketplace_%s_customer"
		// _mapDB := getDatabaseConfig()
		Config.MainDBConf = fineMainDBConfig(configuration.Get("db").ToDatabaseConfig(), dbFormat, env)
		Config.LogDBConf = fineLogDBConfig(configuration.Get("logDB").ToDatabaseConfig(), dbLogFormat, env)
		Config.JobDBConf = fineQueueDBConfig(configuration.Get("jobDB").ToDatabaseConfig(), dbJobFormat, env)
		Config.CacheDBConf = fineCacheDBConfig(configuration.Get("cacheDB").ToDatabaseConfig(), dbCacheFormat, env)
		Config.PromotionCacheDBConf = findPromotionCacheDBConfig(configuration.Get("promotionCacheDB").ToDatabaseConfig(), dbPromotionCacheFormat, env)
		Config.CustomerCacheDBConf = fineCustomerCacheDBConfig(configuration.Get("customerCacheDB").ToDatabaseConfig(), dbCustomerCacheFormat, env)
	}

	// Service config
	{
		buymedVNClient := configuration.Get("buymed-vn-client").ToServiceConfig()
		Config.APIHost = buymedVNClient.Host
		Config.APIKey = buymedVNClient.Authorization

		circaClient := configuration.Get("circa-client").ToServiceConfig()
		Config.CircaAPIHost = circaClient.Host
		Config.CircaCallbackKey = circaClient.Authorization

		buymedComClient := configuration.Get("buymed-com-client").ToServiceConfig()
		Config.BuymedAPIHost = buymedComClient.Host
		Config.BuymedAPIKey = buymedComClient.Authorization
	}

}

func fineMainDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {

	// UAT và PRD dùng chung db main của prd
	if env == "uat" {
		env = "prd"
	}
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineQueueDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {

	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineLogDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineCacheDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	if env == "uat" {
		env = "prd"
	}
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func findPromotionCacheDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	if env == "uat" {
		env = "prd"
	}
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineCustomerCacheDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	if env == "uat" {
		env = "prd"
	}
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}
