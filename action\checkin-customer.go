package action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/promotion/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func DailyCheckinCustomerList(input *model.CheckinCustomer, offset, limit int64, getTotal bool) *common.APIResponse {
	sortFields := &primitive.M{"_id": -1}
	res := model.CheckinCustomerDB.Query(input, offset, limit, sortFields)
	if res.Status != common.APIStatus.Ok {
		return res
	}
	if getTotal {
		countResult := model.CheckinCustomerDB.Count(input)
		res.Total = countResult.Total
	}
	return res
}

func AddCustomerToDailyCheckin(checkinCode string, customerIds []int64, status string) *common.APIResponse {
	now := time.Now()
	news := make([]*model.CheckinCustomer, 0)
	for i, customerId := range customerIds {
		item := &model.CheckinCustomer{
			CustomerID:  customerId,
			CheckinCode: checkinCode,
			Status:      status,
		}
		item.Code = model.GenCodeWithTime(i)
		item.JoinTime = &now
		if item.JoinType == "" {
			item.JoinType = "ADD_BY_STAFF"
		}
		if item.Status == "" {
			item.Status = "INACTIVE"
		}
		news = append(news, item)
		if len(news) >= 500 {
			res := model.CheckinCustomerDB.CreateMany(news)
			if res.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    common.APIStatus.Existed,
					ErrorCode: "CUSTOMER_EXISTED",
					Message:   "Customer existed",
				}
			}
			news = make([]*model.CheckinCustomer, 0)
		}
	}
	if len(news) > 0 {
		res := model.CheckinCustomerDB.CreateMany(news)
		if res.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Existed,
				ErrorCode: "CUSTOMER_EXISTED",
				Message:   "Customer existed",
			}
		}
		return res
	}
	return &common.APIResponse{
		Status:    common.APIStatus.Existed,
		ErrorCode: "CUSTOMER_EXISTED",
		Message:   "Customer existed",
	}
}

func DeleteCustomerOfDailyCheckin(code string) *common.APIResponse {
	if code == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing code",
			ErrorCode: "MISSING_CODE",
		}
	}
	if qCheckExist := model.CheckinCustomerDB.QueryOne(&model.CheckinCustomer{
		Code: code,
	}); qCheckExist.Status != common.APIStatus.Ok {
		return qCheckExist
	}
	return model.CheckinCustomerDB.Delete(&model.CheckinCustomer{
		Code: code,
	})
}

func UpdateCustomerOfDailyCheckin(input model.CheckinCustomer) *common.APIResponse {
	if input.Code == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing code",
			ErrorCode: "MISSING_CODE",
		}
	}
	if qCheckExist := model.CheckinCustomerDB.QueryOne(&model.CheckinCustomer{
		Code: input.Code,
	}); qCheckExist.Status != common.APIStatus.Ok {
		return qCheckExist
	}
	return model.CheckinCustomerDB.UpdateOne(&model.CheckinCustomer{
		Code: input.Code,
	}, input)
}
