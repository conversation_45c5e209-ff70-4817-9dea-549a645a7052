package enum

type CustomerScopeType string

type customerScope struct {
	PHARMACY       CustomerScopeType
	CLINIC         CustomerScopeType
	DRUGSTORE      CustomerScopeType
	HOSPITAL       CustomerScopeType
	PHARMA_COMPANY CustomerScopeType
	DENTISTRY      CustomerScopeType
	BEAUTY_SALON   CustomerScopeType
	HEALTH_CENTER  CustomerScopeType
	PHARMACIST     CustomerScopeType
}

var CustomerScope = &customerScope{
	PHARMACY:       "PHARMACY",
	CLINIC:         "CLIN<PERSON>",
	DRUGSTORE:      "DRUGSTORE",
	HOSPITAL:       "HOSPITAL",
	PHARMA_COMPANY: "PHARMA_COMPANY",
	DENTISTRY:      "DENTISTRY",
	BEAUTY_SALON:   "BEAUTY_SALON",
	HEALTH_CENTER:  "HEALTH_CENTER",
	PHARMACIST:     "PHARMACIST",
}
